@{
    ViewBag.Title = "批量导入内容报备链接号码模板";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<nav class="breadcrumb">
    <i class="Hui-iconfont">&#xe67f;</i> 首页
    <span class="c-gray en">&gt;</span>
    内容报备链接号码模板
    <span class="c-gray en">&gt;</span>
    批量导入
    <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新">
        <i class="Hui-iconfont">&#xe68f;</i>
    </a>
</nav>

<div class="page-container">
    <div class="text-c">
        <div class="panel panel-default">
            <div class="panel-header">
                <h3>📁 批量导入内容报备链接号码模板</h3>
            </div>
            <div class="panel-body">
                <!-- 使用说明 -->
                <div class="alert alert-info" style="margin-bottom: 20px;">
                    <h4><i class="fa fa-info-circle"></i> 使用说明</h4>
                    <div style="margin-top: 10px;">
                        <p><strong>📋 支持的文件格式：</strong></p>
                        <ul style="text-align: left; margin-left: 20px;">
                            <li><strong>Excel文件（.xlsx, .xls）：</strong>支持两种格式
                                <ul>
                                    <li>格式1：第1列为内容</li>
                                    <li>格式2：第1列为用户ID，第2列为内容，第3列为备注（可选）</li>
                                </ul>
                            </li>
                            <li><strong>CSV文件（.csv）：</strong>格式同Excel</li>
                            <li><strong>文本文件（.txt）：</strong>每行一个模板内容</li>
                        </ul>
                        
                        <p style="margin-top: 15px;"><strong>⚠️ 重要提示：</strong></p>
                        <ul style="text-align: left; margin-left: 20px;">
                            <li>系统会自动跳过重复的模板（基于用户ID和内容的组合唯一键）</li>
                            <li>Excel和CSV文件的第一行会被视为表头，自动跳过</li>
                            <li>空行和空内容会被自动忽略</li>
                            <li>建议单次导入不超过10000条记录</li>
                        </ul>
                    </div>
                </div>

                <!-- 导入表单 -->
                <form id="importForm" enctype="multipart/form-data" style="margin-top: 20px;">
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">默认用户：</label>
                        <div class="formControls col-xs-8 col-sm-4">
                            @Html.DropDownList("UserId", (List<SelectListItem>)ViewBag.client, new { @class = "select", @id = "userId" })
                            <small class="help-block">当文件中没有指定用户ID时使用此默认用户</small>
                        </div>
                    </div>

                    <div class="row cl" style="margin-top: 15px;">
                        <label class="form-label col-xs-4 col-sm-2">选择文件：</label>
                        <div class="formControls col-xs-8 col-sm-6">
                            <input type="file" id="importFile" name="importFile" accept=".xlsx,.xls,.csv,.txt" class="input-text" style="width: 100%;" />
                            <small class="help-block">支持 .xlsx, .xls, .csv, .txt 格式</small>
                        </div>
                    </div>

                    <div class="row cl" style="margin-top: 20px;">
                        <div class="col-xs-8 col-sm-9 col-xs-offset-4 col-sm-offset-2">
                            <button type="button" id="btnImport" class="btn btn-primary radius">
                                <i class="fa fa-upload"></i> 开始导入
                            </button>
                            <button type="button" id="btnDownloadTemplate" class="btn btn-secondary radius" style="margin-left: 10px;">
                                <i class="fa fa-download"></i> 下载模板
                            </button>
                            <button type="button" onclick="window.history.back();" class="btn btn-default radius" style="margin-left: 10px;">
                                <i class="fa fa-arrow-left"></i> 返回
                            </button>
                        </div>
                    </div>
                </form>

                <!-- 进度显示 -->
                <div id="progressContainer" style="display: none; margin-top: 20px;">
                    <div class="alert alert-warning">
                        <i class="fa fa-spinner fa-spin"></i> 正在导入，请稍候...
                    </div>
                </div>

                <!-- 结果显示 -->
                <div id="resultContainer" style="display: none; margin-top: 20px;">
                    <div id="resultContent"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
$(function() {
    // 导入按钮点击事件
    $('#btnImport').click(function() {
        var fileInput = $('#importFile')[0];
        var userId = $('#userId').val();
        
        if (!fileInput.files || fileInput.files.length === 0) {
            layer.alert('请选择要导入的文件', { icon: 5 });
            return;
        }

        var file = fileInput.files[0];
        var fileName = file.name;
        var fileExt = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();
        var allowedExts = ['.xlsx', '.xls', '.csv', '.txt'];
        
        if (allowedExts.indexOf(fileExt) === -1) {
            layer.alert('不支持的文件格式，只支持：' + allowedExts.join(', '), { icon: 5 });
            return;
        }

        // 文件大小检查（限制10MB）
        if (file.size > 10 * 1024 * 1024) {
            layer.alert('文件大小不能超过10MB', { icon: 5 });
            return;
        }

        // 显示进度
        $('#progressContainer').show();
        $('#resultContainer').hide();
        $('#btnImport').prop('disabled', true);

        // 创建FormData
        var formData = new FormData();
        formData.append('importFile', file);
        formData.append('userId', userId);

        // 上传文件
        $.ajax({
            url: '/ContentPreparationLink/BatchImport',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            timeout: 300000, // 5分钟超时
            success: function(result) {
                $('#progressContainer').hide();
                $('#btnImport').prop('disabled', false);
                
                if (result.success) {
                    showImportResult(result);
                } else {
                    layer.alert('导入失败：' + result.message, { icon: 2 });
                }
            },
            error: function(xhr, status, error) {
                $('#progressContainer').hide();
                $('#btnImport').prop('disabled', false);
                
                var errorMsg = '导入失败';
                if (status === 'timeout') {
                    errorMsg = '导入超时，请检查文件大小或网络连接';
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }
                
                layer.alert(errorMsg, { icon: 2 });
            }
        });
    });

    // 下载模板按钮点击事件
    $('#btnDownloadTemplate').click(function() {
        downloadTemplate();
    });
});

// 显示导入结果
function showImportResult(result) {
    var html = '<div class="alert alert-success">';
    html += '<h4><i class="fa fa-check-circle"></i> 导入完成</h4>';
    html += '<div style="margin-top: 10px;">';
    html += '<p><strong>📊 导入统计：</strong></p>';
    html += '<ul style="margin-left: 20px;">';
    html += '<li>总记录数：<span class="text-primary">' + result.totalCount + '</span></li>';
    html += '<li>成功添加：<span class="text-success">' + result.successCount + '</span></li>';
    html += '<li>跳过重复：<span class="text-warning">' + result.skippedCount + '</span></li>';
    html += '<li>失败记录：<span class="text-danger">' + result.errorCount + '</span></li>';
    html += '</ul>';
    
    if (result.errors && result.errors.length > 0) {
        html += '<p style="margin-top: 15px;"><strong>❌ 错误信息：</strong></p>';
        html += '<ul style="margin-left: 20px; color: #d9534f;">';
        for (var i = 0; i < result.errors.length; i++) {
            html += '<li>' + result.errors[i] + '</li>';
        }
        if (result.errorCount > result.errors.length) {
            html += '<li>... 还有 ' + (result.errorCount - result.errors.length) + ' 个错误</li>';
        }
        html += '</ul>';
    }
    
    html += '<div style="margin-top: 15px;">';
    html += '<button type="button" onclick="location.href=\'/ContentPreparationLink\'" class="btn btn-primary radius">';
    html += '<i class="fa fa-list"></i> 查看列表</button>';
    html += '<button type="button" onclick="location.reload()" class="btn btn-default radius" style="margin-left: 10px;">';
    html += '<i class="fa fa-refresh"></i> 继续导入</button>';
    html += '</div>';
    html += '</div>';
    html += '</div>';
    
    $('#resultContent').html(html);
    $('#resultContainer').show();
}

// 下载模板文件
function downloadTemplate() {
    var templateData = [
        ['用户ID', '内容', '备注'],
        ['0', '【示例签名】这是一个示例模板内容{Any}', '示例备注'],
        ['0', '【测试】另一个模板内容{Phone}', '测试用途']
    ];
    
    // 创建CSV内容
    var csvContent = '';
    for (var i = 0; i < templateData.length; i++) {
        csvContent += templateData[i].join(',') + '\n';
    }
    
    // 创建下载链接
    var blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    var link = document.createElement('a');
    var url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', '内容报备模板导入模板.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    layer.msg('模板文件已下载', { icon: 1, time: 2000 });
}
</script>

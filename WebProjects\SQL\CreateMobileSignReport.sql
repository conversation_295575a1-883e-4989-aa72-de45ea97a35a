-- 创建移动签名报备表
CREATE TABLE T_MOBILE_SIGN_REPORT (
    ID NUMBER(10) NOT NULL,
    REPORT_DATE DATE,                              -- 日期
    TYPE VARCHAR2(100),                            -- 类型
    ACCOUNT_NAME VARCHAR2(200),                    -- 客户名称
    SIGN_TYPE VARCHAR2(100),                       -- 签名类型:企业简称/APP/商标
    SIGN VARCHAR2(200) NOT NULL,                   -- 签名
    SIGN_COMPANY_NAME VARCHAR2(200),               -- 签名企业名称
    UNIFIED_SOCIAL_CREDIT_CODE VARCHAR2(100),      -- 统一社会信用代码
    LEGAL_PERSON VARCHAR2(100),                    -- 法人
    LEGAL_PERSON_NAME VARCHAR2(100),               -- 法人/经办人姓名
    LEGAL_PERSON_ID_CARD VARCHAR2(100),            -- 法人/经办人身份证号
    LEGAL_PERSON_PHONE VARCHAR2(100),              -- 法人/经办人手机号
    DRAINAGE_LINK VARCHAR2(500),                   -- 引流链接
    DRAINAGE_NUMBER1 VARCHAR2(100),                -- 引流号码1
    DRAINAGE_NUMBER2 VARCHAR2(100),                -- 引流号码2
    SIGN_MATERIALS VARCHAR2(500),                  -- 签名材料
    ID_CARD_PHOTOS VARCHAR2(500),                  -- 身份证照片(反正面)
    EXT_NUMBER VARCHAR2(100),                      -- 扩展号
    CONTENT CLOB,                                  -- 内容
    CHANNEL_ID NUMBER(10),                         -- 通道ID
    USER_ID NUMBER(10),                            -- 用户ID
    STATUS NUMBER(1) DEFAULT 1,                    -- 状态（1-启用 0-禁用）
    ADD_TIME DATE DEFAULT SYSDATE,                 -- 添加时间
    ADMIN_ID NUMBER(10),                           -- 管理员ID
    SIGN_STATUS NUMBER(1) DEFAULT 1,               -- 签名状态
    CONSTRAINT PK_T_MOBILE_SIGN_REPORT PRIMARY KEY (ID)
);

-- 添加表注释
COMMENT ON TABLE T_MOBILE_SIGN_REPORT IS '移动签名报备表';

-- 添加列注释
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.ID IS '主键ID';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.REPORT_DATE IS '日期';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.TYPE IS '类型';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.ACCOUNT_NAME IS '客户名称';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.SIGN_TYPE IS '签名类型:企业简称/APP/商标';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.SIGN IS '签名';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.SIGN_COMPANY_NAME IS '签名企业名称';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.UNIFIED_SOCIAL_CREDIT_CODE IS '统一社会信用代码';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.LEGAL_PERSON IS '法人';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.LEGAL_PERSON_NAME IS '法人/经办人姓名';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.LEGAL_PERSON_ID_CARD IS '法人/经办人身份证号';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.LEGAL_PERSON_PHONE IS '法人/经办人手机号（必须是本人实名验证，否则无法通过校验）';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.DRAINAGE_LINK IS '引流链接（1个子端口对应1个链接）';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.DRAINAGE_NUMBER1 IS '引流号码1';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.DRAINAGE_NUMBER2 IS '引流号码2';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.SIGN_MATERIALS IS '签名材料：1.商标截图（商标网截图）2.APP（工信部ICP备案截图）3.企业简称（营业执照）';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.ID_CARD_PHOTOS IS '身份证照片(反正面)';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.EXT_NUMBER IS '扩展号';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.CONTENT IS '内容';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.CHANNEL_ID IS '通道ID';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.USER_ID IS '用户ID';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.STATUS IS '状态（1-启用 0-禁用）';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.ADD_TIME IS '添加时间';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.ADMIN_ID IS '管理员ID';
COMMENT ON COLUMN T_MOBILE_SIGN_REPORT.SIGN_STATUS IS '签名状态';

-- 创建序列
CREATE SEQUENCE SEQ_T_MOBILE_SIGN_REPORT
    INCREMENT BY 1
    START WITH 1
    NOMAXVALUE
    NOCYCLE
    NOCACHE;

-- 创建触发器
CREATE OR REPLACE TRIGGER TRG_T_MOBILE_SIGN_REPORT
BEFORE INSERT ON T_MOBILE_SIGN_REPORT
FOR EACH ROW
BEGIN
    SELECT SEQ_T_MOBILE_SIGN_REPORT.NEXTVAL INTO :NEW.ID FROM DUAL;
END;
/

-- 创建索引
CREATE INDEX IDX_MSR_SIGN ON T_MOBILE_SIGN_REPORT(SIGN);
CREATE INDEX IDX_MSR_USER ON T_MOBILE_SIGN_REPORT(USER_ID);
CREATE INDEX IDX_MSR_CHANNEL ON T_MOBILE_SIGN_REPORT(CHANNEL_ID);
CREATE INDEX IDX_MSR_DATE ON T_MOBILE_SIGN_REPORT(REPORT_DATE);
CREATE INDEX IDX_MSR_ACCOUNT ON T_MOBILE_SIGN_REPORT(ACCOUNT_NAME);
CREATE INDEX IDX_MSR_TYPE ON T_MOBILE_SIGN_REPORT(TYPE);
CREATE INDEX IDX_MSR_SIGNTYPE ON T_MOBILE_SIGN_REPORT(SIGN_TYPE);
CREATE INDEX IDX_MSR_STATUS ON T_MOBILE_SIGN_REPORT(STATUS);
CREATE INDEX IDX_MSR_ADD_TIME ON T_MOBILE_SIGN_REPORT(ADD_TIME);

-- 创建签名唯一性检查的函数
CREATE OR REPLACE FUNCTION F_CHECK_MOBILE_SIGN_REPORT(
    p_sign IN VARCHAR2,
    p_user_id IN VARCHAR2,
    p_channel_id IN VARCHAR2,
    p_ext_number IN VARCHAR2,
    p_content IN CLOB
) RETURN NUMBER IS
    v_count NUMBER;
BEGIN
    SELECT COUNT(*)
    INTO v_count
    FROM T_MOBILE_SIGN_REPORT
    WHERE SIGN = p_sign
    AND USER_ID = TO_NUMBER(p_user_id)
    AND CHANNEL_ID = TO_NUMBER(p_channel_id)
    AND NVL(EXT_NUMBER, ' ') = NVL(p_ext_number, ' ')
    AND DBMS_LOB.COMPARE(CONTENT, p_content) = 0;
    
    RETURN v_count;
EXCEPTION
    WHEN OTHERS THEN
        RETURN 0;
END;
/

-- 添加示例数据
INSERT INTO T_MOBILE_SIGN_REPORT (
    REPORT_DATE, TYPE, ACCOUNT_NAME, SIGN_TYPE,
    SIGN, SIGN_COMPANY_NAME, UNIFIED_SOCIAL_CREDIT_CODE, LEGAL_PERSON, 
    LEGAL_PERSON_NAME, LEGAL_PERSON_ID_CARD, LEGAL_PERSON_PHONE, 
    DRAINAGE_LINK, DRAINAGE_NUMBER1, DRAINAGE_NUMBER2, 
    SIGN_MATERIALS, ID_CARD_PHOTOS, 
    EXT_NUMBER, CONTENT, CHANNEL_ID, USER_ID, 
    STATUS, ADMIN_ID, SIGN_STATUS
) VALUES (
    TO_DATE('2023-01-01', 'YYYY-MM-DD'), 
    '类型A', 
    '示例客户名称', 
    '企业简称', 
    '示例签名', 
    '示例签名企业', 
    '91110000123456789X', 
    '李四', 
    '王五', 
    '110101199001011234', 
    '***********', 
    'http://example.com', 
    '10086', 
    '10010', 
    '营业执照', 
    '身份证正反面', 
    '1234', 
    '示例内容', 
    1, 
    1, 
    1, 
    1, 
    1
);

-- 授权语句（根据实际情况可能需要调整）
GRANT SELECT, INSERT, UPDATE, DELETE ON T_MOBILE_SIGN_REPORT TO YLHT_USER;
GRANT SELECT ON SEQ_T_MOBILE_SIGN_REPORT TO YLHT_USER;
GRANT EXECUTE ON F_CHECK_MOBILE_SIGN_REPORT TO YLHT_USER; 
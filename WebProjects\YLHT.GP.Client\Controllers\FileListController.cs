﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using YLHT.GP.Common;
using YLHT.GP.Models;
using YLHT_GP_Business.Business;

namespace YLHT.GP.Client.Controllers
{
    public class FileListController : BaseController
    {
        ExportFileBll export = new ExportFileBll();
        /// <summary>
        /// 文件列表
        /// </summary>
        /// <param name="currpage"></param>
        /// <param name="pagesize"></param>
        /// <returns></returns>
        public ActionResult Index(int currpage = 1, int pagesize = 50)
        {
            if (showmenu.Contains(22))
            {
                if (Request.IsAuthenticated && Session["UserID"] != null)
                {
                    int allpage = 0;
                    int allsize = 0;
                    if (currpage <= 0)
                    {
                        currpage = 0;
                    }
                    ViewBag.list = export.GetExportFileList(UserId.ToString(), "1", pagesize, currpage, out allpage, out allsize);
                    if (currpage > allpage)//如果当前页大于所有页
                    {
                        currpage = allpage;//当前页等于所有页
                    }
                    if (currpage <= 0 && allpage != 0)//如果当前页小于0，和所有页不等于0的情况下
                    {
                        currpage = 1;//当前页=1
                    }
                    //总页数
                    ViewBag.allPage = allpage;
                    //当前页
                    ViewBag.curpage = currpage;
                    //总条数
                    ViewBag.allSize = allsize;
                    ViewBag.PageSize = pagesize;
                    return View();
                }
            }
            return RedirectToAction("Notfound", "Account");
        }
        public FileContentResult DownFile(string id)
        {
            if (showmenu.Contains(22))
            {
                ExportFile ef = export.DownFile(id);
                return File(ByteHelper.ReadFileToByte(ef.PathUrl), "application/octet-stream", ef.PathUrl.Substring(ef.PathUrl.LastIndexOf('\\') + 1));
            }
            return null;
        }
    }
}
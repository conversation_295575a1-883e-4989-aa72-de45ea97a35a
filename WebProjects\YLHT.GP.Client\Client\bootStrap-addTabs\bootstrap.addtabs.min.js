
/**
 * Website: http://git.oschina.net/hbbcs/bootStrap-addTabs
 *
 * Version : 2.1
 *
 * Created by joe on 2016-2-4.Update 2017-10-24
 */

/**
 * Website: http://git.oschina.net/hbbcs/bootStrap-addTabs
 *
 * Version : 2.1
 *
 * Created by joe on 2016-2-4.Update 2017-10-24
 */
(function(b){var a={content:"",close:true,monitor:"body",iframe:false,height:b(window).height()-118,target:".nav-tabs",loadbar:true,contextmenu:true,store:false,storeName:"",contentStyle:"content",ajax:{"async":true,"dataType":"html","type":"get"},local:{"refreshLabel":"刷新此标签","closeThisLabel":"关闭此标签","closeOtherLabel":"关闭其他标签","closeLeftLabel":"关闭左侧标签","closeRightLabel":"关闭右侧标签","loadbar":"正在加载内容，请稍候．．．"},callback:function(){}};var c;_store=function(){if(typeof(arguments[0])=="object"){arguments[0].each(function(d,e){localStorage.setItem(d,e)})}else{if(arguments[1]){localStorage.setItem(arguments[0],arguments[1])}else{return localStorage.getItem(arguments[0])}}};_click=function(e){var d,f;d=(typeof e.data("addtab")=="object")?e.data("addtab"):e.data();if(!d.id&&!d.addtab){d.id=Math.random().toString(36).substring(3,35);e.data("id",d.id)}b.addtabs.add({"target":d.target?d.target:c,"id":d.id?d.id:d.addtab,"title":d.title?d.title:e.html(),"content":a.content?a.content:d.content,"url":d.url?d.url:e.attr("href"),"ajax":d.ajax?d.ajax:false})};_createMenu=function(d,e,f){return b("<a>",{"href":"javascript:void(0);","class":"list-group-item","data-right":d}).append(b("<i>",{"class":"glyphicon "+e})).append(f)};_pop=function(l,k,g){b("body").find("#popMenu").remove();var i=k.attr("id")?_createMenu("refresh","glyphicon-refresh",a.local.refreshLabel):"";var f=k.attr("id")?_createMenu("remove","glyphicon-remove",a.local.closeThisLabel):"";var j=k.prev("li").attr("id")?_createMenu("remove-left","glyphicon-chevron-left",a.local.closeLeftLabel):"";var h=k.next("li").attr("id")?_createMenu("remove-right","glyphicon-chevron-right",a.local.closeRightLabel):"";var d=b("<ul>",{"aria-controls":l,"class":"rightMenu list-group",id:"popMenu","aria-url":k.attr("aria-url"),"aria-ajax":k.attr("aria-ajax")}).append(i).append(f).append(_createMenu("remove-circle","glyphicon-remove-circle",a.local.closeOtherLabel)).append(j).append(h);d.css({"top":g.pageY,"left":g.pageX});d.appendTo(b("body")).fadeIn("slow");b("ul.rightMenu a[data-right=refresh]").on("click",function(){var n=b(this).parent("ul").attr("aria-controls").substring(4);var e=b(this).parent("ul").attr("aria-url");var m=b(this).parent("ul").attr("aria-ajax");b.addtabs.add({"id":n,"url":e,"refresh":true,"ajax":m})});b("ul.rightMenu a[data-right=remove]").on("click",function(){var e=b(this).parent("ul").attr("aria-controls");if(e.substring(0,4)!="tab_"){return}b.addtabs.close({"id":e});b.addtabs.drop()});b("ul.rightMenu a[data-right=remove-circle]").on("click",function(){var e=b(this).parent("ul").attr("aria-controls");c.find("li").each(function(){var m=b(this).attr("id");if(m&&m!="tab_"+e){b.addtabs.close({"id":b(this).children("a").attr("aria-controls")})}});b.addtabs.drop()});b("ul.rightMenu a[data-right=remove-left]").on("click",function(){var e=b(this).parent("ul").attr("aria-controls");b("#tab_"+e).prevUntil().each(function(){var m=b(this).attr("id");if(m&&m!="tab_"+e){b.addtabs.close({"id":b(this).children("a").attr("aria-controls")})}});b.addtabs.drop()});b("ul.rightMenu a[data-right=remove-right]").on("click",function(){var e=b(this).parent("ul").attr("aria-controls");b("#tab_"+e).nextUntil().each(function(){var m=b(this).attr("id");if(m&&m!="tab_"+e){b.addtabs.close({"id":b(this).children("a").attr("aria-controls")})}});b.addtabs.drop()});d.mouseleave(function(){b(this).fadeOut("slow")});b("body").click(function(){d.fadeOut("slow")})};_listen=function(){b(a.monitor).on("click","[data-addtab]",function(){_click(b(this));b.addtabs.drop()});b("body").on("click",".close-tab",function(){var e=b(this).prev("a").attr("aria-controls");b.addtabs.close({"id":e});b.addtabs.drop()});b("body").on("mouseover",'li[role = "presentation"]',function(){b(this).find(".close-tab").show()});b("body").on("mouseleave",'li[role = "presentation"]',function(){b(this).find(".close-tab").hide()});if(a.contextmenu){b("body").on("contextmenu","li[role=presentation]",function(f){var g=b(this).children("a").attr("aria-controls");_pop(g,b(this),f);return false})}var d;b("body").on("dragstart.h5s",".nav-tabs li",function(f){d=b(this)}).on("dragover.h5s dragenter.h5s drop.h5s",".nav-tabs li",function(f){if(d==b(this)){return}b(".dragBack").removeClass("dragBack");b(this).addClass("dragBack");d.insertAfter(b(this))}).on("dragend.h5s",".nav-tabs li",function(){b(".dragBack").removeClass("dragBack")});b("body").on("shown.bs.tab",'a[data-toggle="tab"]',function(){var f=b(this).parent("li").attr("id");f=f?f.substring(8):"";if(a.store){var e=b.parseJSON(_store("addtabs"+a.storeName));b.each(e,function(g,h){(h.id==f)?(h.active="true"):(delete h.active)});e=JSON.stringify(e);_store("addtabs"+a.storeName,e)}})};b.addtabs=function(d){b.addtabs.set(d);_listen();if(a.store){var e=_store("addtabs"+a.storeName)?b.parseJSON(_store("addtabs"+a.storeName)):{};var f;b.each(e,function(g,h){if(h.active){f=g}b.addtabs.add(h)});if(f){c.children(".active").removeClass("active");b("#tab_"+f).addClass("active");c.next(".tab-content").children(".active").removeClass("active");b("#"+f).addClass("active")}}};b.addtabs.set=function(){if(arguments[0]){if(typeof arguments[0]=="object"){a=b.extend(a,arguments[0]||{})}else{a[arguments[0]]=arguments[1]}}if(typeof a.target=="object"){c=a.target}else{c=b("body").find(a.target).length>0?b(a.target).first():b("body").find(".nav-tabs").first()}};b.addtabs.add=function(d){var j,i;d.id=d.id?d.id:Math.random().toString(36).substring(3,35);if(typeof d.target=="object"){j=d.target}else{if(typeof d.target=="string"){j=b("body").find(d.target).first()}else{j=c}}var g="tab_"+d.id;var f=j;if(a.store){var k=_store("addtabs"+a.storeName)?b.parseJSON(_store("addtabs"+a.storeName)):{};k[g]=d;k[g].target=(typeof k[g].target=="object")?a.target:k[g].target;b.each(k,function(n,o){delete o.active});k[g].active="true";k=JSON.stringify(k);_store("addtabs"+a.storeName,k)}var h=f.next(".tab-content");f.children('li[role = "presentation"].active').removeClass("active");h.children('div[role = "tabpanel"].active').removeClass("active");if(f.find("#tab_"+g).length<1){var m=b("<div>",{"id":"tabCover","class":"tab-cover"});var l=b("<li>",{"role":"presentation","id":"tab_"+g,"aria-url":d.url,"aria-ajax":d.ajax?true:false}).append(b("<a>",{"href":"#"+g,"aria-controls":g,"role":"tab","data-toggle":"tab"}).html(d.title));if(a.close){l.append(b("<i>",{"class":"close-tab glyphicon glyphicon-remove"}))}var i=b("<div>",{"class":"tab-pane "+a.contentStyle,"id":g,"height":a.height-5,"role":"tabpanel"});f.append(l);h.append(i.append(m))}else{if(!d.refresh){b("#tab_"+g).addClass("active");b("#"+g).addClass("active");return}else{i=b("#"+g);i.html("")}}if(a.loadbar){i.html(b("<div>",{"class":""}).append(b("<div>",{"class":"progress-bar progress-bar-striped progress-bar-success active","role":"progressbar","aria-valuenow":"100","aria-valuemin":"0","aria-valuemax":"100","style":"width:100%"}).append('<span class="sr-only">100% Complete</span>').append("<span>"+a.local.loadbar+"</span>")))}if(d.content){i.html(d.content)}else{if(a.iframe==true&&(d.ajax=="false"||!d.ajax)){i.html(b("<iframe>",{"class":"iframeClass","height":a.height,"frameborder":"no","border":"0","src":d.url}))}else{var e=b.extend(a.ajax,d.ajax||{});e.url=d.url;e.success=function(n){i.html(n)};b.ajax(e)}}f.find("#tab_"+g).addClass("active");h.find("#"+g).addClass("active");h.find("#"+g).find("#tabCover").remove()};b.addtabs.close=function(e){if(b("#tab_"+e.id).hasClass("active")){if(b("#tab_"+e.id).parents("li.tabdrop").length>0&&!b("#tab_"+e.id).parents("li.tabdrop").hasClass("hide")){b("#tab_"+e.id).parents(".nav-tabs").find("li").last().tab("show")}else{b("#tab_"+e.id).prev("li").tab("show")}b("#"+e.id).prev().addClass("active")}b("#tab_"+e.id).remove();b("#"+e.id).remove();if(a.store){var d=b.parseJSON(_store("addtabs"+a.storeName));delete d[e.id];d=JSON.stringify(d);_store("addtabs"+a.storeName,d)}b.addtabs.drop();a.callback()};b.addtabs.closeAll=function(e){if(typeof e=="string"){e=b("body").find(e)}b.each(e.find("li[id]"),function(){var f=b(this).children("a").attr("aria-controls");b("#tab_"+f).remove();b("#"+f).remove()});e.find('li[role = "presentation"]').first().addClass("active");var d=e.find('li[role = "presentation"]').first().children("a").attr("aria-controls");b("#"+d).addClass("active");b.addtabs.drop()};b.addtabs.drop=function(){var d=b("<li>",{"class":"dropdown pull-right hide tabdrop tab-drop"}).append(b("<a>",{"class":"dropdown-toggle","data-toggle":"dropdown","href":"#"}).append(b("<i>",{"class":"glyphicon glyphicon-align-justify"})).append(b("<b>",{"class":"caret"}))).append(b("<ul>",{"class":"dropdown-menu"}));b("body").find(".nav-tabs").each(function(){var e=b(this);if(e.find(".tabdrop").length<1){d.prependTo(e)}else{d=e.find(".tabdrop")}if(e.parent().is(".tabs-below")){d.addClass("dropup")}var f=0;e.append(d.find("li")).find(">li").not(".tabdrop").each(function(){if(this.offsetTop>0||e.width()-b(this).position().left-b(this).width()<83){d.find("ul").prepend(b(this));f++}});if(f>0){d.removeClass("hide");if(d.find(".active").length==1){d.addClass("active")}else{d.removeClass("active")}}else{d.addClass("hide")}})}})(jQuery);$(function(){$.addtabs()});

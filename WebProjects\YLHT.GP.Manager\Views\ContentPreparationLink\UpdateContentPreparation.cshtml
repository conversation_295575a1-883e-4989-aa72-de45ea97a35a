﻿@model YLHT.GP.Models.ContentPreparationModel

@{ ViewBag.Title = "修改内容报备链接号码模板";}
<article class="page-container">
    @using (Html.BeginForm("UpdateContentPreparation", "ContentPreparationLink", FormMethod.Post, new { @class = "form form-horizontal", id = "form1" }))
    {

        <div class="row cl">
            <label class="form-label col-xs-4 col-sm-2"><span class="c-red">*</span>用户账号：</label>
            <div class="formControls col-xs-8 col-sm-9">
                @Html.DropDownList("UserId", (List<SelectListItem>)ViewBag.client, new { style = "width:100%" })
            </div>
        </div>
        <div class="row cl">
            <label class="form-label col-xs-4 col-sm-2">报备内容：</label>
            <div class="formControls col-xs-8 col-sm-9">
                <textarea class="textarea" autocomplete="off" placeholder="报备内容" onKeyUp="$.Huitextarealength(this,300)" name="Text" id="Text">@Model.Text</textarea>
                <label style="color:red">注：内容中用@替换5个字符以内的可变内容，@@ 可多次出现，连续的@最多允许出现20次</label>
            </div>
        </div>
        <!--<div class="row cl">
            <label class="form-label col-xs-4 col-sm-2"><span class="c-red">*</span>优先级：</label>
            <div class="formControls col-xs-8 col-sm-9">
                <input type="number" class="number input-text" style="width:300px" name="Priority" value="@Model.Priority" />
                <label style="color:red">
                    注：与文本匹配比较，值越大，优先级越高-->
                    <!--
                     与审核敏感词的优先级对比
                    -->
                <!--</label>
            </div>
        </div>-->
        <div class="row cl">
            <label class="form-label col-xs-4 col-sm-3">状态：</label>
            <div class="formControls col-xs-8 col-sm-9 skin-minimal">
                @if (Model.Status == 1)
                {
                    <div class="radio-box">
                        <input type="radio" id="TdType-2" name="Status" value="1" checked>
                        <label for="TdType-2">启用</label>
                    </div>
                    <div class="radio-box">
                        <input name="Status" type="radio" id="TdType-1" value="0">
                        <label for="TdType-1">停用</label>
                    </div>
                }
                else
                {
                    <div class="radio-box">
                        <input type="radio" id="TdType-2" name="Status" value="1">
                        <label for="TdType-2">启用</label>
                    </div>
                    <div class="radio-box">
                        <input name="Status" type="radio" id="TdType-1" value="0" checked>
                        <label for="TdType-1">停用</label>
                    </div>
                }

            </div>
        </div>
        <div class="row cl">
            <label class="form-label col-xs-4 col-sm-2">备注：</label>
            <div class="formControls col-xs-8 col-sm-9">
                <textarea class="textarea" autocomplete="off" placeholder="备注" onKeyUp="$.Huitextarealength(this,200)" name="Remark" id="Remark">@Model.Remark</textarea>
            </div>
        </div>
        <div class="row cl">
            <div class="col-xs-8 col-sm-9 col-xs-offset-4 col-sm-offset-5">
                <input class="btn btn-primary radius" type="button" onclick="return Content_Check()" value="&nbsp;&nbsp;保存&nbsp;&nbsp;">
            </div>
        </div>
    }
</article>

   
    
    <script type="text/javascript">
        $(function () {
            $("select").select2();
        })
        function Content_Check() {
            loadshow();
            modalshow();
            var text = $("#Text").val();
            if (text == "") {
                loadhide();
                modalhide();
                layer.alert("请输入报备内容", {icon:2});
                return false;
            }
            $("#form1").ajaxSubmit(function (data) {
                loadhide();
                modalhide();
                if (data == 1) {
                    layer.alert("修改成功！", { icon: 1 }, function () {
                        layer_close(this);
                    });
                } else if (data == 2) {
                    layer.alert("修改失败！", { icon: 2 }, function () {
                        layer_close(this);
                    });
                } else {
                    layer.alert("您的身份已过期，请重新登陆！", { icon: 2 }, function () {
                        parent.parent.location.replace("/Account/NotFound");
                    });
                }
            });
        }
    </script>


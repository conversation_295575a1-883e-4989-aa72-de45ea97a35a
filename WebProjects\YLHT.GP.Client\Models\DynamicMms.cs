﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace YLHT.GP.Client.Models
{
    /// <summary>
    /// 动态彩信需要的实体
    /// </summary>
    public class DynamicMms
    {
        public Frame[] MmsFiles { get; set; }
        /// <summary>
        /// 文件信息
        /// </summary>
        public class Frame
        {
            /// <summary>
            /// 文件名称
            /// </summary>
            public string FileName { get; set; }
            /// <summary>
            /// 帧id
            /// </summary>
            public string Zid { get; set; }
            /// <summary>
            /// 帧运行时间
            /// </summary>
            public string ZTime { get; set; }
            /// <summary>
            /// KeyId，这里暂为文件名，用于前台做予删除的唯一标识
            /// </summary>
            public string KeyID { get; set; }
            /// <summary>
            /// 前台帧自增id
            /// </summary>
            public string Did { get; set; }
        }
    }
}
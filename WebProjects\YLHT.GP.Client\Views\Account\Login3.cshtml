﻿
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="renderer" content="webkit">
    <title>登录</title>
    <link href="~/Client/css/bootstrap.min.css" rel="stylesheet" />
    <link href="~/Client/css/font-awesome.css" rel="stylesheet" />
    <style type="text/css">
        html, body {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            font-size: 14px;
            font-weight: 300;
        }

        .form-control {
            border-radius: 0;
        }

        .form-control:focus {
            box-shadow: none;
            border-color: #1ab394 !important
        }

        .container-fluid > .molde {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, .1);
        }

    .bg-filter { position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-image: url("https://bjchs.org.cn/Sites/Uploaded/UserUpLoad/20190115/20190115095743.jpg"); background-size: cover; background-position: 50%; filter: blur(5px); -webkit-filter: blur(5px); -moz-filter: blur(5px); -ms-filter: blur(5px); -o-filter: blur(5px); }

        .login-inner {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 1260px;
            transform: translate(-50%, -50%);
            display: flex;
        }

            .login-inner > .login-img { width: 1260px; height: 640px; background-image: url(""); background-size: cover; background-position: 50%; }
        .guanghe {
            width: 200px;
            height: 50px;
            background-image: url('../../../images/微信图片_20190808102317.jpg');
            background-size: cover;
            background-position: 50%;
        }
        .login-inner > .login-box {
            position: absolute;
            top: 120px;
            right: 0;
            height: 340px;
            width: 300px;
            background-color: #fff;
        }

        .login-box {
            padding: 50px 35px 0 35px;
        }

        .login-box > .title {
            font-size: 22px;
            font-weight: 500;
            text-align: center;
            margin-bottom: 30px;
        }

        .login-box > div > .form-group {
            position: relative;
        }

        .login-box > div > .form-group > i {
            position: absolute;
            top: 1px;
            left: 1px;
            width: 34px;
            height: 32px;
            font-size: 22px;
            line-height: 32px;
            background-color: #fff;
            border-right: 1px solid #ccc;
        }

        .login-box > div > .form-group > img {
            position: absolute;
            top: 1px;
            right: 1px;
            height: 32px;
            width: 85px;
            border-left: 1px solid #ccc;
        }

        .login-box > div > .form-group > .form-control {
            width: 100%;
            padding-left: 46px;
        }

        .login-box > div > .form-group > .form-control#code {
            padding-left: 12px;
            padding-right: 97px;
        }

        input:-webkit-autofill, textarea:-webkit-autofill, select:-webkit-autofill {
            background-color: #fff !important;
            background-image: none !important;
            color: #555 !important;
        }

        @@media (max-width: 968px) {
            .login-inner {
                position: absolute;
                top: 50%;
                left: 50%;
                height: 340px;
                width: 300px;
                transform: translate(-50%, -50%);
                display: flex;
            }

            .login-inner > .login-img {
                display: none;
            }
        }
        .nav_bj {
            width: 100%;
            height:100px;
            position:absolute;
            top:30px;
            z-index:10000;
        }
        .banner {
            height: 126px;
            margin: 0 auto;
            position: relative;
            width: 1280px;
        }
        .banner_a {
            color: #333;
            text-decoration: none;
            cursor: pointer;
        }
        .rowheight{
            height:200px;
            border:1px solid red;
        }
        .rowright{
            position:absolute;
            top:50px;
            left:30px;
            width:900px;
            height:500px;
        }
        .heightwidth {
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <div class="nav_bj">
        <div class="banner">
            <a class="banner_a">
                <img src="https://bjchs.org.cn/Sites/Uploaded/UserUpLoad/20190115/20190115092414.png">
            </a>
           </div>
    </div>
    <div class="container-fluid">
        <div class="bg-filter"></div>
        <div class="molde"></div>
        <div class="login-inner">
            <div class="login-img">
                @*<div class="rowright">
            <div class="row">
                <div class="col-md-3 rowheight guanghe">

                </div>
                <div class="col-md-3 rowheight guanghe"></div>
                <div class="col-md-3 rowheight guanghe"></div>
            </div>
            <div class="row">
                <div class="col-md-3 rowheight guanghe"></div>
                <div class="col-md-3 rowheight guanghe"></div>
                <div class="col-md-3 rowheight guanghe"></div>
            </div>
        </div>*@
                <div id="myCarousel" class="carousel slide rowright">
                    <!-- 轮播（Carousel）指标 -->
                    <ol class="carousel-indicators">
                        <li data-target="#myCarousel" data-slide-to="0" class="active"></li>
                        <li data-target="#myCarousel" data-slide-to="1"></li>
                        <li data-target="#myCarousel" data-slide-to="2"></li>
                    </ol>
                    <!-- 轮播（Carousel）项目 -->
                    <div class="carousel-inner">
                        <div class="item active">
                            @*<img src="/wp-content/uploads/2014/07/slide1.png" alt="First slide">*@
                            <img class="heightwidth" src="~/images/微信图片_20190808102317.jpg" />
                        </div>
                        <div class="item">
                            @*<img src="/wp-content/uploads/2014/07/slide2.png" alt="Second slide">*@
                            <img class="heightwidth" src="~/images/微信图片_20190808102433.jpg" />
                        </div>
                        <div class="item">
                            @*<img src="/wp-content/uploads/2014/07/slide3.png" alt="Third slide">*@
                            <img class="heightwidth" src="~/images/微信图片_20190808102436.jpg" />
                        </div>
                    </div>
                    <!-- 轮播（Carousel）导航 -->
                    <a class="left carousel-control" href="#myCarousel" role="button" data-slide="prev">
                        <span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>
                        <span class="sr-only">Previous</span>
                    </a>
                    <a class="right carousel-control" href="#myCarousel" role="button" data-slide="next">
                        <span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>
                        <span class="sr-only">Next</span>
                    </a>
                </div>
            </div>
            
            <div class="login-box">
                <div class="title">短信系统登录</div>
                <div>
                    <div class="form-group">
                        <i class="fa fa-user-o fa-fw"></i>
                        <input type="text"
                               id="username"
                               name="username"
                               class="form-control"
                               placeholder="用户名" />
                    </div>
                    <div class="form-group">
                        <i class="fa fa-keyboard-o fa-fw"></i>
                        <input type="password"
                               id="password"
                               name="password"
                               class="form-control"
                               placeholder="密码" />
                    </div>
                    <div class="form-group">
                        <input type="text"
                               id="code"
                               name="code"
                               class="form-control"
                               placeholder="验证码" />
                        <img class="code" src="${baseUrl}login.do?method=getRCode">
                    </div>
                    <button type="submit" class="btn btn-success btn-block">登 录</button>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Client/js/jquery.min.js"></script>
    <script src="~/Client/js/bootstrap.min.js"></script>
    <script src="~/Client/js/layer/layer.js"></script>
    <script>
        $(document).ready(function () {
            var username = $('#username'),
                password = $('#password'),
                code = $('#code'),
                codeimg = $('.code'),
                button = $('button');
            var imgsrc = codeimg.attr('src');
            button.on('click', function () {
                if (username.val().trim() === '') {
                    layer.msg('用户名不能为空!');
                    username.focus();
                } else if (password.val().trim() === '') {
                    layer.msg('密码不能为空!');
                    password.focus();
                } else if (code.val().trim() === '') {
                    layer.msg('验证码不能为空!');
                    code.focus();
                } else {
                    $.ajax({
                        type: 'POST',
                        url: '${pageContext.request.contextPath }/login.do',
                        data: {
                            username: username.val(),
                            password: password.val(),
                            code: code.val(),
                        },
                        success: function (result) {
                            console.log(result);
                            try {
                                result = JSON.parse(result);
                                if (result.code === 200) {
                                    layer.msg('登录成功！');
                                    window.location.href = './index.do';
                                } else if (result.code === 600) {
                                    layer.alert(result.message, { icon: 2 });
                                    codeimg.attr('src', imgsrc + '&time=' + new Date().getTime());
                                } else {
                                    layer.alert('未知的返回！', { icon: 2 });
                                }
                            } catch (e) {
                                layer.alert('返回数据错误！' + e, { icon: 2 });
                            }
                        },
                        error: function (error) {
                            layer.alert('请求失败！' + error, { icon: 2 });
                        }
                    });
                }
            });

                           
                codeimg.on('click', function () {
                    codeimg.attr('src', imgsrc + '&time=' + new Date().getTime());
                });

            $(document).keydown(function (event) {
                if (event.keyCode == 13) {
                    button.trigger('click');
                }
            });
        });
    </script>
</body>
</html>

﻿@model YLHT.GP.Models.BirthdayTemplate
@using YLHT.GP.Models
@{
    ViewBag.Title = "短信查询";
    ViewBag.KeyWords = "";
    ViewBag.description = "";
}
<link href="~/Client/css/plugins/bootstrap-table/bootstrap-table.min.css" rel="stylesheet">
<body class="gray-bg">
    <div class="sk-three-bounce11" style="z-index:999999" id="loading">
        <div class="sk-child sk-bounce1"></div>
        <div class="sk-child sk-bounce2"></div>
        <div class="sk-child sk-bounce3"></div>
    </div>
    <div id="modal" class="modal" style="display:none;"></div>
    <div class="wrapper wrapper-content animated fadeInRight">
        <div class="ibox float-e-margins">
            <div class="ibox-title fixedBox">
                <h5>生日祝福模板</h5>
            </div>
            <div class="ibox-content">
                @using (Html.BeginForm("TemplateList", "ClientBook", FormMethod.Post, new { @class = "form-inline", role = "form", id = "form1" }))
                {
                <div class="form-group">
                    <span>模板编号：</span>
                    <input type="text" placeholder="模板编号" id="" value="@Model.TEMPLATEID" name="TEMPLATEID" class="form-control">
                </div>
                    <div class="form-group">
                        <span>模板内容：</span>
                        <input type="text" placeholder="模板内容" id="" value="@Model.Template" name="Template" class="form-control">
                    </div>
                    <div class="form-group">
                        <button class="btn btn-white" onclick="return submit1()" type="button">查询</button>
                    </div>
                }
                <div class="hr-line-dashed"></div>
                <div class="row row-lg">
                    <div class="col-sm-12">
                        <div class="example">
                            <table class="table table-bordered table-striped table-hover table-condensed">
                                <thead>
                                    <tr>
                                        <th>模板编号</th>
                                        
                                        <th>添加时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (ViewBag.Templates != null)
                                    {

                                        if (ViewBag.Templates.Count == 0)
                                        {
                                            <tr>
                                                <td colspan="3" class="text-center">暂无数据</td>
                                            </tr>
                                        }
                                        foreach (var item in (List<BirthdayTemplate>)ViewBag.Templates)
                                        {
                                            <tr>
                                                <td>@item.TEMPLATEID</td>
                                                <td>@item.AddTime</td>
                                                <td><a href="javascript:void(0);" onclick="updatet(@item.TEMPLATEID)">修改</a> <a href="javascript:void(0);" onclick="deletes(@item.TEMPLATEID)">删除</a></td>
                                            </tr>
                                            <tr>
                                                <td colspan="3" style="text-align:right">
                                                    <span>@item.Template <span style="color:red">(共 @item.Template.Length 字)</span></span>
                                                </td>
                                            </tr>
                                        }
                                    }
                                    else
                                    {
                                        <tr>
                                            <td colspan="3" class="text-center">暂无数据</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 自定义js -->
    <script src="../Client/js/content.js?v=1.0.0"></script>

    <!-- Bootstrap table -->
    <!-- Peity -->
    <script src="../Client/js/plugins/layer/laydate/laydate.js"></script>

    <script src="~/Client/js/plugins/bootstrap-table/bootstrap-table.min.js"></script>
    <script src="~/Client/js/plugins/bootstrap-table/bootstrap-table-mobile.min.js"></script>
    <script src="~/Client/js/plugins/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>
   <script>
       $(function () {
           $("#loading").hide();
       });
       function submit1() {
           $("#loading").show();
           $("#form1").submit();
       }
       function deletes(isd){
           layer.confirm('您确定要删除这个模板？', {
               btn: ['确定', '取消'] //按钮
           }, function () {
               $.ajax({
                   type: 'post',
                   url: '/ClientBook/DeleteTemplate',
                   data: {
                       id: isd
                   },
                   success: function (data) {
                       layer.closeAll();
                       layer.msg(data, {
                           end: function () {
                               location.reload();//刷新页面
                           }
                       });
                   }
               });
           }, function () {
           });
       }
       //修改生日模板
       function updatet(id) {
           layer.open({
               type: 2,
               title: '修改生日祝福模板',
               area: ['600px', '300px'],
               content: '/ClientBook/UpdateTemplate?id='+id
           });
       }
       
   </script>
</body>

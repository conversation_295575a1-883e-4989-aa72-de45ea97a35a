/**
 * Created by fanfa on 2017/4/27.
 */

$(document).ready(function () {
    
    layer.ready(function () {
    	//回车键执行方法
		$(document).keydown(function (event) {
            if (event.keyCode == 13) {
            	$('#select-btn').trigger('click');
            }
        });
    	
        var old_list = [],//后台传的list
	        group_list = [],//分组list
            new_list = new Array(),//过度list,存编号和拼音首字母
            now_list = new Array(),//排序后的list
            show_list = new Array(),//搜索显示的list
            check_list = new Array(),//选中list
            select = 0,//
            obj = function (index, value) {
                this.index = index;
                this.value = value;
            };
        var down_menu = $('.down-menu'),//搜索预选列表
        _prev = down_menu.prev(),//搜索按钮和搜索款的父dom
        _input = $('#select-input'),//搜索输入对象
        _contacts = $('.contacts'),//显示联系人的控件对象
        select_btn = $('#select-btn'),//搜索按钮
        check_all = $('#check-all'),//
        contacts_list = $('#contacts-list'),//选中联系人列表
        contacts_info = $('#contacts-info'),//联系人详情
        right_header = $('#right-header'),//
        cancel = $('.right-header>a[name="cancel"]'),//
        g_list = $('#group-list'),//
        contacts_n=$('#contacts-n'),//
        dropdownMenu=$('#dropdownMenu1'),//
        _check = false,//
        _li = false;//鼠标是否在预选列表
        
        
        
   
     	 var tt = (new Date()).valueOf();  
     
        //获取分组
        $.ajax({
        	type : 'post',
            url: '/ClientBook/GetGroups',
        	success : function(data) {
                console.log(data);
                if (data.length > 0) {
                    group_list = data;
                    setGroup();
                    //获取联系人
                    $('.loading').fadeIn("fast", function () {
                        $.ajax({
                            type: 'post',
                            url: '/ClientBook/GetContacts',
                            success: function (data) {
                                old_list = data;
                                init();
                            }
                        });
                    });
                }
        	}
        });
        
        /**
         * 发送短信
         */
        $("#sendMsg").on('click', function() {
        	var phones = "";
        	$.each(check_list, function (index, value) {
        		phones += now_list[value].phone + "\n";
        	});
        	
        	if(phones == ""){
        		layer.msg("请至少选择一条记录");
        	}else{
        		window.top['tempPhone']=phones;
        		window.location.href = "sendMsg.do?method=jumpMsg";
        	}
        });
        
        
        //==============================联系人操作==================================
        /**
         * 添加联系人
         */
        $('#add-contacts').on('click', function () {
            layer.open({
                type: 2,
                title: '添加联系人',
                area: ['380px', '570px'],
                content: '/ClientBook/addContacts'
            });
        });
        
        
        /**
         * 批量添加联系人1
         */
        $('#add-batchcontacts').on('click', function () {
        	layer.open({
                type: 2,
                area: ['600px', '320px'],
                fix: false,
                maxmin: true,
                shadeClose: true,
                shade:0.4,
                title: '批量添加联系人',
                content: '/ClientBook/BatchAdd'
            });
        });
        /**
         * 编辑联系人
         */
        $('.right-header>a[name="compile"]').on('click', function () {
            layer.open({
                type: 2,
                title: '修改联系人',
                area: ['380px', '530px'],
                content: '/ClientBook/updateContacts?Id=' + now_list[select].BookId
            });
        });

        

        /**
         * 删除联系人
         */
        $('.right-header>a[name="delete"]').on('click', function () {
        	var ids = "";
        	$.each(check_list, function (index, value) {
        		ids += now_list[value].BookId + ",";
        	});
        	
            layer.confirm('您确定要删除这些联系人？', {
                btn: ['确定', '取消'] //按钮
            }, function () {
				$.ajax({
					type :'POST',
                    url: '/ClientBook/DeleteContacts',
					data : {
                        cid : ids
					},
					success : function(data) {
						layer.closeAll();
						layer.msg(data, {end : function() {
							location.reload();//刷新页面
						}});
					} 
				});
            }, function () {
            });
        });
        
        
        /**
         * 移动联系人到指定分组
         */
        $('.right-header>a[name="move"]').on('click', function () {
        	var ids = "";
            $.each(check_list, function (index, value) {
                ids += now_list[value].BookId + ",";
        	});
            layer.open({
                type: 2,
                title: '移动到',
                area: ['380px', (160 + group_list.length * 35) + 'px'],
                content: '/ClientBook/Move?ids=' + ids
            });
        });

        
        //==================================分组操作==================================
        /**
         * 添加分组
         */
        $('#add-group').on('click', function () {
            layer.open({
                type: 2,
                title: '添加分组',
                area: ['380px', '290px'],
                content: '/ClientBook/AddBook'
            });
        });

        /**
         * 编辑分组
         */
        $('#edit-group').on('click', function () {
            var temp = group_list.length - 1;
            if (temp == 0) {
                layer.msg('您没有个自定义的分组!');
            } else if (temp >= 1) {
                if ($("#dropdownMenu1").attr('data-id') != "0") {


                    layer.open({
                        type: 2,
                        title: '编辑分组',
                        area: ['380px', '290px'],
                        content: '/ClientBook/UpdateGroup?groupid=' + $("#dropdownMenu1").attr('data-id')
                    });
                } else {
                    layer.msg("请选择分组！");
                }
            } else {
                layer.msg('程序报错!');
            }

        });
        //生日模板列表
        $("#template-group").on("click", function () {
            layer.open({
                type: 2,
                title: '生日祝福模板',
                area: ['1000px', '500px'],
                content:'/ClientBook/TemplateList'
            });
        });
        //添加生日模板
        $("#templateadd-group").on("click", function () {
            layer.open({
                type: 2,
                title: '添加生日祝福模板',
                area: ['600px', '300px'],
                content: '/ClientBook/AddTemplate'
            });
        });
        /**
         * 删除分组
         */
        $('#delete-group').on('click',function(){
        	if(dropdownMenu.attr('data-id') == 0)
        		layer.msg('不能删除默认分组！');        	
        	else
	        	layer.confirm('您确定要删除“'+$.trim(dropdownMenu.text())+'”这个分组？', {
	        		btn: ['确定', '取消'] //按钮
	        	}, function () {
	        		$.ajax({
	        			type : 'post',
                        url: '/ClientBook/DeleteGroup',
	        			data : {
	        				id : dropdownMenu.attr('data-id')
	        			},
	        			success : function(data) {
	        				layer.closeAll();
	        				layer.msg(data, {end : function() {
								location.reload();//刷新页面
							}});
	        			}
	        		});
	        	}, function () {
        	});
        });


        //取消
        cancel.on('click', function () {
            check_list.splice(0, check_list.length);
            setContacts();
        });

        //点击联系人
        _contacts.on('click', 'li[name="itme"]', function () {
            // if (!_check && check_list.length == 0) {
            if (check_list.length == 0) {
                _contacts.find('li[data-id="' + select + '"]').removeClass('select');
                $(this).addClass('select');
                select = parseInt($(this).attr('data-id'));
                show_contacts(select);
            }
        });

        //鼠标悬浮或离开down_menu时
        down_menu.on("mouseover mouseout", 'li', function (event) {
            if (event.type == "mouseover") { //鼠标悬浮
                _li = true;
            } else if (event.type == "mouseout") { //鼠标离开
                _li = false;
            }
        });

        //鼠标悬浮或离开复选框时
        _contacts.on("mouseover mouseout", 'i', function (event) {
            if (event.type == "mouseover") { //鼠标悬浮
                _check = true;
            } else if (event.type == "mouseout") { //鼠标离开
                _check = false;
            }
        });

        //搜索框失去焦点
        _input.on('blur', function () {
            if (!_li) {
                down_menu.css({'width': '0px', 'display': 'none'});
                down_menu.html('');
            }
        });

        //搜索框输入或聚焦
        _input.on('keyup focus', function () {
            down_menu.css({'width': (_prev.width() - 43) + 'px', 'display': 'block'});
            var _value = $(this).val(),
                _html = '';
            show_list.splice(0, show_list.length);
            $.each(now_list, function (index, value) {
                if (value.name.indexOf($.trim(_value)) != -1 && $.trim(_value) != '') {
                    show_list.push(index);
                    _html += '<li data-id="' + index + '">' + value.name + ' ' + value.phone + '</li>';
                }
            });
            down_menu.html(_html);
        });

        //搜索选择
        down_menu.on('click', 'li', function () {
            check_list.splice(0, check_list.length);
            var data_id = $(this).attr('data-id'),
                _html = '<li class="hint">搜索“' + _input.val() + '”后选择的结果<i class="fa fa-trash-o fa-fw"></i></li>';
            _html += setItme(now_list[parseInt(data_id)], parseInt(data_id));
            _contacts.html(_html);
            down_menu.css({'width': '0px', 'display': 'none'});
            down_menu.html('');
        });

        //点击搜索按钮
        select_btn.on('click', function () {
            check_list.splice(0, check_list.length);
            var _html = '<li class="hint">搜索“' + _input.val() + '”后的结果<i class="fa fa-trash-o fa-fw"></i></li>';
            $.each(show_list, function (index, value) {
                _html += setItme(now_list[value], value);
            });
            _contacts.html(_html);
            if (!_input.is(":focus")) {
                down_menu.css({'width': '0px', 'display': 'none'});
                down_menu.html('');
            }
        });

        //按下enter
        $('#select-input').keyup(function (event) {
            if (event.keyCode == 13) {
                select_btn.trigger('click');
                _input.blur();
            }
        });

        //多选
        _contacts.on('change', 'input', function () {
            var id = $(this).attr('data-id');
            if ($(this).is(':checked')) {
                check_list.push(parseInt(id));
            } else {
                check_all.find('i').removeClass('fa-check-circle').addClass('fa-circle-thin');
                $.each(check_list, function (index, value) {
                    if (value == id) {
                        check_list.splice(index, 1);
                        return true;
                    }
                });
            }
            if (check_list.length == 0)
                show_contacts(select);
            else
                show_contacts_list();
        });

        //取消选择(搜索)
        _contacts.on('click', 'li>i', function () {
            cancel.trigger('click');
        });

        //全选
        check_all.on('click', function () {
            if ($(this).find('i').attr('class').indexOf('fa-circle-thin') != -1) {
                var _in = _contacts.find('input');
                check_list.splice(0, check_list.length);
                $.each(_in, function (index) {
                    check_list.push(parseInt(_in.eq(index).attr('data-id')));
                });
                _contacts.find('input[type="checkbox"]').prop('checked', true);
                $(this).find('i').removeClass('fa-circle-thin').addClass('fa-check-circle');
                show_contacts_list();
            } else {
                check_list.splice(0, check_list.length);
                _contacts.find('input[type="checkbox"]').prop('checked', false);
                $(this).find('i').removeClass('fa-check-circle').addClass('fa-circle-thin');
                show_contacts(select);
            }
        });

        //
        $('.dropdown>.dropdown-menu').on('click', 'li>a', function () {
        	var _html = '', id = parseInt($(this).attr('data-id')), t = true, temp = -1;
        	dropdownMenu.html($(this).html() + ' <span class="caret"></span>').attr('data-id', id );
            if (id == 0)
                setContacts()
            else {
                $.each(now_list, function (index, value) {
                    if (value.GroupId == id) {
                        if (t) {
                            _html += '<li class="hint">' + new_list[index].value + '</li>' + setItme(value, index, t);
                            select = index;
                            t = false;
                        } else if (new_list[index].value == new_list[temp].value) {
                            _html += setItme(value, index);
                        } else {
                            _html += '<li class="hint">' + new_list[index].value + '</li>' + setItme(value, index);
                        }
                        temp = index;
                    }
                });
                _contacts.html(_html);
                if(temp!=-1)
                show_contacts(select);
            }

        });
        
        /**
         * 将now_list的数据显示
         */
        function setContacts() {
        	if($('.loading').length>0){
        		$('.loading').fadeOut("fast");
        	}
            var _html = '';
            $.each(new_list, function (index, value) {
                if (index == 0) {
                    _html += '<li class="hint">' + value.value + '</li>' + setItme(now_list[index], index, true);
                } else if (value.value == new_list[index - 1].value) {
                    _html += setItme(now_list[index], index);
                } else {
                    _html += '<li class="hint">' + value.value + '</li>' + setItme(now_list[index], index);
                }
            });
            _contacts.html(_html);
            select = 0;
            show_contacts(select);
        }

        /**
         * 设置一位联系人
         * @param o 联系人对象
         * @param id 对象在now_list的下标
         * @returns {string}
         */
        function setItme(o, id, t) {
            var ran = parseInt(Math.random() * 7 + 1),
                _html = '<li name="itme" class="itme ' + (t ? 'select' : '') + '" data-id="' + id + '">' +
                    '<div class="img color' + ran + '">' + o.UserName[0] + '</div>' +
                    '<span class="name">' + o.UserName + '</span>' +
                    '<label class="checkbox"><input type="checkbox" data-id="' + id + '"/><i></i></label></li>';
            return _html;
        }
        
        /**
         *显示某个联系人详情
         * @param i 这个联系人在now_list中的下标
         */
        function show_contacts(i) {
            right_header.removeClass('contacts-a').addClass('contacts-i');
            var group_name = '未分组';
			if(now_list.length>0){
                $.each(group_list, function (index, value) {
                    if (now_list[i] && now_list[i].GroupId == value.GroupId && now_list[i].GroupId != 0) {
						group_name=value.GroupName;
					}
				});
                var _html = '<li class="contacts-name"> ' +
                    '<div class="' + $('.select').find('div').attr('class') + '">' + now_list[i].UserName[0] + '</div>' +
                    '<div class="name"><input type="text" disabled value="' + now_list[i].UserName + '"/></div>' +
					'</li>' +
					'<li class="contacts-content">' +
					'<label>分组</label><span>'+group_name+'</span>' +
					'<br/>' +
                    '<label>手机</label><span>' + now_list[i].Msisdn + '</span>' +
                    '<hr/>' +
                    '<label>性别</label><span>' + (now_list[i].Sex==1 ? "女" : "男") + '</span>' +
                    '<hr/>' +
                    '<label>生日</label><span>' + now_list[i].BirthdayTime + '</span>' +
                    '<hr/>' +
                    '<label>是否提醒</label><span><form><input onClick="remind(' + now_list[i].BookId + ',1)" type="radio" ' + (now_list[i].IsRemind == 1 ? "checked" : "") + ' value="1" name="IsRemind">提醒 <input onClick="remind(' + now_list[i].BookId + ',0)" type="radio" ' + (now_list[i].IsRemind == 0 ? "checked" : "") + ' value="0" name="IsRemind">不提醒</from></span>' +
                    '<hr/>' +
                    '<label>生日祝福</label><span>' + now_list[i].Template + '</span>' +
                    '<hr/>' +
                    '<label>备注</label><span>' + now_list[i].Remark + '</span>' +
					'</li>';
				contacts_info.html(_html);
				contacts_list.html('');
			}
        }
        
        
        
        /**
         *显示选中联系人列表
         */
        function show_contacts_list() {
            right_header.removeClass('contacts-i').addClass('contacts-a');
            var _html = '',
                ll = _contacts.find('input:checkbox:checked');
            $.each(check_list, function (index, value) {
                _html += '<li><div class="' + ll.eq(index).parent().parent().children('div').attr('class') + '">' + now_list[value].UserName[0] + '</div><p>' + now_list[value].UserName + '</p></li>';
            });
            contacts_list.html(_html);
            contacts_info.html('');
            contacts_n.html(check_list.length + '个联系人');
        }

        /**
         *设置组别
         */
        function setGroup() {
            var _html = '';
            $.each(group_list, function (index, value) {
                _html += '<li><a data-id="' + value.GroupId + '">' + value.GroupName + '</a></li>';
                if (index != group_list.length - 1) {
                    _html += '<li role="separator" class="divider"></li>';
                }
            });
            g_list.html(_html);
        }

        /**
         * 初始化
         */
        function init() {
            //根据中文生成拼音
            $.each(old_list, function (index, value) {
                new_list.push(new obj(index, ConvertPinyin(value.UserName)[0]));
            });

            //根据拼音首字母排序new_list
            /*new_list.sort(function (a, b) {
                if (a.value > b.value) {
                    return 1;
                }
                if (a.value < b.value) {
                    return -1;
                }
                return 0;
            });*/

            //根据new_list的排序和old_list的数据生成now_list
            $.each(new_list, function (index, value) {
                $.each(old_list, function (_index, _value) {
                    if (value.index == _index) {
                        now_list.push(_value);
                        return true;
                    }
                });
            });
            setContacts();
        }
    });
});
function remind(bookid,value) {
    $.ajax({
        url: '/ClientBook/UpdateRemind',
        type: 'post',
        dataType: 'json',
        data: { bookid: bookid, isremind: value },
        success: function (data) {
            console.log(data);
        }
    });
}
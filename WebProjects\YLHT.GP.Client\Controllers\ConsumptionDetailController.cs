﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using YLHT.GP.Common;
using YLHT.GP.Models;
using YLHT_GP_Business.Business;

namespace YLHT.GP.Client.Controllers
{
    public class ConsumptionDetailController : BaseController
    {
        // GET: ConsumptionDetail
        AmountBll amount = new AmountBll();
        // GET: Amount
        /// <summary>
        /// 消费明细
        /// </summary>
        /// <param name="am"></param>
        /// <param name="currpage"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public ActionResult Indexall(AmountModel am, int currpage = 0, int pageSize = 50)
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                int rows;
                int pageCount;
                if (currpage <= 0)
                {
                    currpage = 1;
                }
                am.UserId = UserId;
                List<AmountModel> alist = amount.GetAmountList(am, currpage, pageSize, "addtime", "1", out rows, out pageCount);
                if (currpage > pageCount)
                {
                    currpage = pageCount;
                }
                if (currpage <= 0 && pageCount != 0)
                {
                    currpage = 1;
                }
                //总页数
                ViewBag.allPage = pageCount;
                //当前页
                ViewBag.curpage = currpage;
                //总条数
                ViewBag.allSize = rows;
                //列表
                ViewBag.alist = alist;
                //(操作类型)业务类型
                List<SelectListItem> actype = new List<SelectListItem>();
                actype.Add(new SelectListItem { Text = "全部", Value = "0" });
                actype.Add(new SelectListItem { Text = "充值", Value = "1" });
                actype.Add(new SelectListItem { Text = "扣费", Value = "2" });
                actype.Add(new SelectListItem { Text = "子账户充值", Value = "3" });
                actype.Add(new SelectListItem { Text = "任务退回", Value = "4" });
                actype.Add(new SelectListItem { Text = "失败返回", Value = "5" });
                ViewBag.actype = actype;
                //额度类型
                List<SelectListItem> amtype = new List<SelectListItem>();
                amtype.Add(new SelectListItem { Text = "全部", Value = "0" });
                amtype.Add(new SelectListItem { Text = "短信", Value = "1" });
                amtype.Add(new SelectListItem { Text = "彩信", Value = "2" });
                amtype.Add(new SelectListItem { Text = "闪信", Value = "3" });
                amtype.Add(new SelectListItem { Text = "金额", Value = "9" });
                ViewBag.amtype = amtype;
                return View(am);

            }
            return RedirectToAction("../Account/Login");
        }
        public ActionResult Index(AmountModel am, int currpage = 0, int pageSize = 50)
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                int rows;
                int pageCount;
                if (currpage <= 0)
                {
                    currpage = 1;
                }
                am.UserId = UserId;
                var xiaofeilogtable= ConfigurationManager.AppSettings["XiaoFeiUsers"];
                string tablename = "user_amountdetailslog";
                if (string.IsNullOrEmpty(xiaofeilogtable))
                {
                    if (xiaofeilogtable.Contains(UserId.ToString()))
                    {
                        tablename = "user_amountdetails";
                    }
                }
                List<AmountModel> alist= new List<AmountModel>();
                if (tablename== "user_amountdetailslog")
                {
                  alist = amount.GetAmountListlog(am, currpage, pageSize, "addtime", "1", out rows, out pageCount);

                }
                else
                {
                    alist = amount.GetAmountList(am, currpage, pageSize, "addtime", "1", out rows, out pageCount);
                }
                if (currpage > pageCount)
                {
                    currpage = pageCount;
                }
                if (currpage <= 0 && pageCount != 0)
                {
                    currpage = 1;
                }
                //总页数
                ViewBag.allPage = pageCount;
                //当前页
                ViewBag.curpage = currpage;
                //总条数
                ViewBag.allSize = rows;
                //列表
                ViewBag.alist = alist;
                //(操作类型)业务类型
                List<SelectListItem> actype = new List<SelectListItem>();
                actype.Add(new SelectListItem { Text = "全部", Value = "0" });
                actype.Add(new SelectListItem { Text = "充值", Value = "1" });
                actype.Add(new SelectListItem { Text = "扣费", Value = "2" });
                actype.Add(new SelectListItem { Text = "子账户充值", Value = "3" });
                actype.Add(new SelectListItem { Text = "任务退回", Value = "4" });
                actype.Add(new SelectListItem { Text = "失败返回", Value = "5" });
                ViewBag.actype = actype;
                //额度类型
                List<SelectListItem> amtype = new List<SelectListItem>();
                amtype.Add(new SelectListItem { Text = "全部", Value = "0" });
                amtype.Add(new SelectListItem { Text = "短信", Value = "1" });
                amtype.Add(new SelectListItem { Text = "彩信", Value = "2" });
                amtype.Add(new SelectListItem { Text = "闪信", Value = "3" });
                amtype.Add(new SelectListItem { Text = "金额", Value = "9" });
                ViewBag.amtype = amtype;
                return View(am);

            }
            return RedirectToAction("../Account/Login");
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Web;
using System.Web.Mvc;
using YLHT.GP.Client.Filters;
using YLHT.GP.Client.Models;
using YLHT.GP.Common;
using YLHT_GP_Business.Business;

namespace YLHT.GP.Client.Controllers
{
    public class HomeController : BaseController
    {
        ClientBLL cbll = new ClientBLL();
        NoticesBll noticesBll = new NoticesBll();
        public ActionResult Index()
        {
            try
            {

            
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                NaviPartial();
                ViewBag.loginip = YLHT.GP.Common.IpHelper.GetRealIP();
                ViewBag.user=cbll.GetCustomerById(UserId.ToString());
                int rows = 0;
                int pageCount = 0;
                int i = 0;
                string noticesContent = "";
                noticesBll.GetNoticesList(new GP.Models.NoticesModel { UserId = UserId,Status=2, StartTime = DateTime.Now.AddDays(-3), IsCoerce = 1 }, 3, 1, out rows, out pageCount).Select(r => r.Content).ToList().ForEach(d=> { i++;noticesContent += i + "." + d+"。"; });
                if (rows == 0)
                {
                    noticesBll.GetNoticesList(new GP.Models.NoticesModel { UserId=UserId, Status = 2, StartTime =DateTime.Now.AddDays(-3)},3,1,out rows,out pageCount).Select(r => r.Content).ToList().ForEach(d => { i++; noticesContent += i + "." + d + "。"; });
                }
                ViewBag.noticesContent = noticesContent;
                
                return View();
            }
            }
            catch (Exception ex)
            {
                Trace.TraceError("Index,ex="+ex);
            }
            return RedirectToAction("Login","Account");
        }
        
        public ActionResult UpPaswd()
        {
            //HttpCookie cookie = CookiesHelper.GetCookie("YLHTClientInfo");
            //ViewBag.pwd = NativeMethods.DESDecrypt(Convert.ToString(cookie.Values["sPassWord"]));
            return View();
        }
        [HttpPost]
        public JsonResult UpdatePwd(string password,string NewPwd,string QNewPwd)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                if (password != null && password != "" && NewPwd != null && NewPwd != "" && QNewPwd != "" && QNewPwd != null)
                {
                    string row=cbll.UpdatePwd(UserId.ToString(), password, QNewPwd);
                    if (row=="1")
                    {
                        int userid = UserId;
                        string username = User.Identity.Name;
                        Trace.TraceInformation($"客户：{User.Identity.Name} 修改密码成功，新密码：{QNewPwd}");
                        Notify(HttpNotifyMethod._DataService.ResetUserInfo, userid,userid,username);
                        return Json(new { Code = 1, Description = "修改成功！" });
                    }
                    else if(row=="2")
                    {
                        Trace.TraceInformation($"客户：{User.Identity.Name} 修改密码失败，新密码：{QNewPwd}");
                        return Json(new { Code = 2, Description = "修改失败！" });
                    }
                    else if (row=="3")
                    {
                        Trace.TraceInformation($"客户：{User.Identity.Name} 修改密码失败，新密码：{QNewPwd}");
                        return Json(new { Code = 2, Description = "原始密码错误！" });
                    }
                }

            }
            return Json(new { Code=2,Description="身份已过期！请重新登陆！"});
        }

        public ActionResult Start(DateTime? id)
        {
            var date = id ?? DateTime.Now;
            date = date.Date;
            var bll = new SendCountBll();
            Stopwatch watch = new Stopwatch();
            watch.Start();
            var result = bll.DayCollect(date, UserId.ToString(),"","") ;
            watch.Stop();
            Trace.TraceInformation("Home,Start=" + watch.ElapsedMilliseconds);
            var model = new StartModel(result);
            model.Date = date;
            return View(model);
        }
    }
}
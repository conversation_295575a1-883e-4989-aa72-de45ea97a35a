﻿using System;
using System.Diagnostics;
using System.Globalization;
using System.Web;
using System.Web.Caching;
using System.Web.Mvc;
using System.Web.Routing;
using System.Configuration;
using System.IO;
using System.Data;
using YLHT_GP_Business.Business;
using YLHT.GP.Client.Filters;
using System.Collections.Generic;
using YLHT.GP.Models;
using YLHT.GP.Common;
using System.Linq;
using System.Threading;
using System.Net;
using System.Text.RegularExpressions;
using System.Text;
using Newtonsoft.Json;

namespace YLHT.GP.Client.Controllers
{
    [MenuAuthorize]
    public class BaseController : Controller
    {
        /// <summary>
        /// 验证是否超时
        /// </summary>
        protected int UserId
        {
            get
            {
                int userId;
                object s = Session["UserId"];
                if (s == null)
                {
                    return 0;
                }
                if (int.TryParse(s + "", out userId))
                {
                    return userId;
                }
                return 0;
            }
        }
        ClientBLL clientBLL = new ClientBLL();
        /// <summary>
        /// 通知 一个参数
        /// </summary>
        /// <param name="func"></param>
        protected void Notify(Func<DataService.VerifyParam, bool> func, int userid,string username)
        {
            try
            {
                var us = clientBLL.GetCustomerById(UserId.ToString());
                string UPassWord = us.PassWord;
                var timeStamp = NativeMethods.GetTimeStamp();
                var md5Secret = NativeMethods.Md5Encrypt(UPassWord + timeStamp).ToLower();
                DataService.VerifyParam verifyParam = new DataService.VerifyParam()
                {
                    Id = userid,
                    Account = username,
                    IsServer = false,
                    Secret = md5Secret,
                    TimeStamp = timeStamp
                };
                if (!func(verifyParam))
                {
                    Trace.TraceInformation($"通知func={func.Method.Name}失败！");
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"通知func={func.Method.Name}异常，ex={ex}");
            }
        }
        /// <summary>
        /// 通知  两个参数
        /// </summary>
        /// <typeparam name="Id"></typeparam>
        /// <param name="func"></param>
        /// <param name="id"></param>
        protected void Notify<Id>(Func<DataService.VerifyParam, Id, bool> func, Id id, int userid, string username)
        {
            try
            {
                var us = clientBLL.GetCustomerById(UserId.ToString());
                string UPassWord = us.PassWord;
                var timeStamp = NativeMethods.GetTimeStamp();
                var md5Secret = NativeMethods.Md5Encrypt(UPassWord + timeStamp).ToLower();
                DataService.VerifyParam verifyParam = new DataService.VerifyParam()
                {
                    Id = userid,
                    Account = username,
                    IsServer = false,
                    Secret = md5Secret,
                    TimeStamp = timeStamp
                };
                if (!func(verifyParam, id))
                {
                    Trace.TraceInformation($"通知func={func.Method.Name}失败！");
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"通知func={func.Method.Name}异常，ex={ex}");
            }
        }
        


        protected List<int> showmenu;
        //protected List<NaviLinkModels> menu;
        NavBll mnrb = new NavBll();
        protected override void Initialize(RequestContext requestContext)
        {
            base.Initialize(requestContext);
            string cacheMenuKey = "ClientMenu_" + UserId.ToString(CultureInfo.InvariantCulture) + "Cache";
            List<int> tmp = new List<int>();
            //string cacheKey = "Client_" + UserId.ToString(CultureInfo.InvariantCulture) + "Cache";
            //menu=(List<NaviLinkModels>)HttpRuntime.Cache[cacheKey];
            if (HttpRuntime.Cache[cacheMenuKey] == null)
            {
                var one=mnrb.NaviList(UserId.ToString(),"0","2");//顶级菜单
                var clientmenu = mnrb.GetClientUserMenus(UserId.ToString());//客户菜单
                if (mnrb.IsIndirectCustomer(UserId.ToString()))//如果是子账户
                {
                    foreach (var item in one)
                    {
                        var dx = clientmenu.Where(d => d.MENUID == item.MENUID);
                        if (dx.Count()>0)
                        {
                            tmp.Add(dx.ElementAt(0).MENUID);
                        }
                        foreach (var item1 in mnrb.NaviList(UserId.ToString(), item.MENUID + "", "2"))//二级菜单
                        {
                            var dx1 = clientmenu.Where(d => d.MENUID == item1.MENUID);
                            if (dx1.Count()>0)
                            {
                                tmp.Add(dx1.ElementAt(0).MENUID);
                            }
                        }
                    }
                }
                else//直接账户
                {
                    foreach (var item in one)
                    {
                        tmp.Add(item.MENUID);

                        foreach (var item1 in mnrb.NaviList(UserId.ToString(), item.MENUID + "", "2"))//二级菜单
                        {
                            tmp.Add(item1.MENUID);
                        }
                    }
                }
                
                HttpRuntime.Cache.Add(cacheMenuKey, tmp, null, DateTime.Now.AddHours(4), TimeSpan.Zero, CacheItemPriority.Normal, null);
            }
            showmenu = (List<int>)HttpRuntime.Cache[cacheMenuKey];
        }
        
        public ActionResult NaviPartial()
        {
            if (!Request.IsAuthenticated || Session["UserId"] == null) return RedirectToAction("Login", "Account");
            if (Session["UserId"] == null) return RedirectToAction("Login", "Account");
            string cacheKey = "Client_" + UserId.ToString(CultureInfo.InvariantCulture) + "Cache";
            List<NaviLinkModels> tmpOne = new List<NaviLinkModels>();
            if (HttpRuntime.Cache[cacheKey] == null)
            {
                var one = mnrb.NaviList(Session["UserId"].ToString(), "0", "2");
                var clientmenu = mnrb.GetClientUserMenus(UserId.ToString());
                if (mnrb.IsIndirectCustomer(UserId.ToString()))
                {
                    foreach (var item in one)
                    {
                        var dx = clientmenu.Where<NaviLinkModels>(d=>d.MENUID==item.MENUID);
                        
                        if (dx.Count()>0)
                        {
                            NaviLinkModels tmpModel = new NaviLinkModels();
                            tmpModel = item;
                            List<NaviLinkModels> t = new List<NaviLinkModels>();
                            foreach (var item1 in mnrb.NaviList(Session["UserId"].ToString(), item.MENUID + "", "2"))//二级菜单
                            {
                                var dx1 = clientmenu.Where(d => d.MENUID == item1.MENUID);
                                if (dx1.Count() > 0)
                                {
                                    t.Add(item1);
                                }
                            }
                            tmpModel.SubMenus = t;
                            tmpOne.Add(tmpModel);
                        }
                    }
                    one = tmpOne;
                }
                else
                {
                    foreach (var item in one)
                    {
                        item.SubMenus = mnrb.NaviList(Session["UserId"].ToString(), item.MENUID + "", "2");//二级菜单
                    }
                }
                HttpRuntime.Cache.Add(cacheKey, one, null, DateTime.Now.AddHours(4), TimeSpan.Zero, CacheItemPriority.Normal, null);//存入缓存菜单
            }
            ViewBag.Menu = HttpRuntime.Cache[cacheKey];
            return null;
        }

        internal static class HttpNotifyMethod
        {
            private static Lazy<DataService.DataService> dataService = new Lazy<DataService.DataService>(() => { return new DataService.DataService() { Url = ConfigurationManager.AppSettings["DataServiceUrl"] }; });
            public static DataService.DataService _DataService
            {
                get { return dataService.Value; }
            }
            private static Lazy<string> passWrod = new Lazy<string>(() => { return NativeMethods.Md532Encrypt(NativeMethods.DESDecrypt(Convert.ToString(CookiesHelper.GetCookie("YLHTClientInfo")?.Values["sPassWord"]))).ToLower(); });
            public static string PassWord
            {
                get { return passWrod.Value; }
            }
        }





    }
    
}

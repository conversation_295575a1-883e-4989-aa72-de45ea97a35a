<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title></title>

    <link rel="stylesheet" href="../theme/css/bootstrap.min.css" type="text/css" media="screen" />
    <script src="../theme/js/jquery.min.js"></script>


    <script type="text/javascript">
        $(function() {
            $('#testbutton').click(function() {
                window.parent.window.$.addtabs.add({
                    target: "#tabs1",
                    url: "example/ajax/mailbox.txt",
                    title: "test"
                });
            })
        })
    </script>

</head>

<body>
    <button class="btn btn-default" id="testbutton">test</button>
</body>

</html>

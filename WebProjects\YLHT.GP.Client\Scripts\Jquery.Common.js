﻿function pagecheck(allpagenum) {
    var curpage = $("#pageindex").val();
    if (isNaN(curpage)) {
        alert("页面跳转必须为数字！");return false;
    }
    if (parseInt(curpage) > parseInt(allpagenum)) {
        alert("跳转页面超出页面总索引！");return false;
    }
    $("#hdcurpage").val(curpage);
    $("form").submit();
    return true;
}
//首页
function FristPage() {
    $("#loading").show();
    $("#hdcurpage").val(1);
    $("form").submit();
}
//下一页
function NextPage(curpage) {
    $("#loading").show();
    $("#hdcurpage").val(curpage);
    $("form").submit();
}
//上一页
function PreviousPage(curpage) {
    $("#loading").show();
    $("#hdcurpage").val(curpage);
    $("form").submit();
}
//末页
function LastPage(allpage) {
    $("#loading").show();
    $("#hdcurpage").val(allpage);
    $("form").submit();
}


//打开dialog事件
function opendialog(divid, width, height, title, openurl) {
    var t = $(top).scrollTop() + ($(top).height() - (parseInt(height) +parseInt(100))) * 0.5;
	$(divid).dialog(
					{
						//此处进行属性设置，参考：http://api.jqueryui.com/dialog/#option-buttons
						
                        //对话框是否在初始化时打开
                        autoOpen:false,
						//是否按esc键退出
                        closeOnEscape:false,
                        //是否可以拖拽
                        draggable: true,
                        //位置
                        position: ['center', t-100], //[260, 160],
                        //高度
                        height:height,
                        //宽度
                        width:width,
                        //标题
						title:title,
						//模态
                        modal:true,
                        //是否可调整大小
                        resizable:false
                    });
                    //调用其他页面填充容器
                    $(divid).html("");
					$(divid).load(openurl).dialog("open");
}
//关闭dialog事件
function closedialog(divid) {
    $(divid).dialog("close");
	}


//	//解决png背景透明的问题
//	function correctPNG() // correctly handle PNG transparency in Win IE 5.5 & 6. 
//	{
//	    var arVersion = navigator.appVersion.split("MSIE")
//	    var version = parseFloat(arVersion[1])
//	    if ((version >= 5.5) && (document.body.filters)) {
//	        for (var j = 0; j < document.images.length; j++) {
//	            var img = document.images[j]
//	            var imgName = img.src.toUpperCase()
//	            if (imgName.substring(imgName.length - 3, imgName.length) == "PNG") {
//	                var imgID = (img.id) ? "id='" + img.id + "' " : ""
//	                var imgClass = (img.className) ? "class='" + img.className + "' " : ""
//	                var imgTitle = (img.title) ? "title='" + img.title + "' " : "title='" + img.alt + "' "
//	                var imgStyle = "display:inline-block;" + img.style.cssText
//	                if (img.align == "left") imgStyle = "float:left;" + imgStyle
//	                if (img.align == "right") imgStyle = "float:right;" + imgStyle
//	                if (img.parentElement.href) imgStyle = "cursor:hand;" + imgStyle
//	                var strNewHTML = "<span " + imgID + imgClass + imgTitle
//             + " style=\"" + "width:" + img.width + "px; height:" + img.height + "px;" + imgStyle + ";"
//             + "filter:progid:DXImageTransform.Microsoft.AlphaImageLoader"
//             + "(src=\'" + img.src + "\', sizingMethod='scale');\"></span>"
//	                img.outerHTML = strNewHTML
//	                j = j - 1
//	            }
//	        }
//	    }
//	}
//	window.attachEvent("onload", correctPNG);

	//	//透明背景结束

function dialogurl(url, options) {
    var html = document.getElementById("dialogurl-window");
    if (html == null) {
        html = '<div class="dialog" id="dialogurl-window" title="提示信息" style="overflow:hidden;">' +
        ' <iframe src="' + url + '" frameBorder="0" style="border: 0; " scrolling="auto" width="100%" height="100%"></iframe>' +
        '</div>';
    } else {
        html.innerHTML = ' <iframe src="' + url + '" frameBorder="0" style="border: 0; " scrolling="auto" width="100%" height="100%"></iframe>';
    }
    var closefunc = options.close;
    var t = $(top).scrollTop() + ($(top).height() - (options.height + 100)) * 0.5;
	var d = $(html).dialog($.extend({
	    modal: true,
	    closeOnEscape: false,
	    draggable: true,
	    resizable: false,
        position: ['center',t-100],
	    close: function (event, ui) {
	        $('#dialogurl-window iframe').attr("src", null);
	        $(d).dialog("destroy"); // 关闭时销毁	        
	        if (closefunc) {
	            closefunc(event, ui);
	        }
	    }
	}, options));
	//$(d).move("left", 0);
	return d;
}

function invoke(url, data) {
    var result, error = null;
    $.ajax({
        url: url,
        type: 'post',
        data: data,
        dataType: 'json',
        async: false,
        // ReSharper disable once DuplicatingLocalDeclaration
        success: function (data) {
            result = data;
        },
        error: function (xmlHttpRequest, textStatus, errorThrown) {
            if (xmlHttpRequest.status == 501) {
                result = { "Status": "-1", "Text": "超时" };
            } else {
                error = textStatus || errorThrown;
            }
        }
    });
    if (error == null) {
        return result;
    } else {
        throw new Error(error);
    }
}

//通道签名的验证 
//签名判断  suffix 要验证的字符串
function suffix_test(suffixstr) {
    //签名的正则验证 【2-10个任意字符】
    var suffixReg = new RegExp("^【[\\w|\u4e00-\u9fa5]{2,10}】$");
    if (suffixstr != null && suffixstr != "") {
        if (suffixReg.test(suffixstr)) {
            return true;
        } else {
            return false;
        }
    }
    else {
        return true;
    }
}

//扩展的判断 extnumStr 要验证的字符串
function extnum_test(extnumStr) {
    //扩展的正则验证 0-8个数字
    var extnumReg = new RegExp("^[0-9]{0,10}$");
    if (extnumStr != null && extnumStr != "") {
        if (extnumReg.test(extnumStr)) {
            return true;
        }
        else {
            return false;
        }
    }
    else {
        return true;
    }
}

function ExportCsvFile(queryFactor, exportType) {
    var form = $("<form id='formexportcsv'>");
    form.attr('style', 'display:none');
    form.attr('target', '_blank');
    form.attr('method', 'post');
    form.attr('action', '/Home/ExportCsv');
    var input1 = $('<input>');
    input1.attr('type', 'hidden');
    input1.attr('name', 'exporttype');
    input1.attr('value', exportType);
    form.append(input1);
    //QueryFactor = { one: 1, two: 2, three: 3 };
    $.each(queryFactor, function (name, value) {
        var input = $('<input>');
        input.attr('type', 'hidden');
        input.attr('name', name);
        input.attr('value', value);
        form.append(input);
    });
    $('body').append(form);
    form.submit();
    form.remove();
}
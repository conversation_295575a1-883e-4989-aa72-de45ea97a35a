﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace YLHT.GP.Client.Filters
{
    /// <summary>
    /// 登陆过滤器
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, Inherited = true, AllowMultiple = true)]
    public class MenuAuthorizeAttribute : AuthorizeAttribute
    {
        public override void OnAuthorization(AuthorizationContext filterContext)
        {
            base.OnAuthorization(filterContext);
        }

        protected override bool AuthorizeCore(HttpContextBase httpContext)
        {
            Object o = httpContext.Session["UserId"];
            Hashtable userOnline = (Hashtable)(httpContext.Application["Online"]);
            if (userOnline == null && o != null)
            {
                if (httpContext.Session["UserName"] != null)
                {
                    Online(httpContext.Session["UserName"].ToString());
                    userOnline = (Hashtable)(httpContext.Application["Online"]);
                }
            }
            if (userOnline != null && o != null)
            {
                IDictionaryEnumerator idE = userOnline.GetEnumerator();
                string strKey = string.Empty;
                if (userOnline.Count > 0)
                {
                    while (idE.MoveNext())
                    {
                        //判断是否登录时保存的session是否与当前页面的sesion相同
                        if (userOnline.Contains(httpContext.Session.SessionID))
                        {
                            if (idE.Key != null && idE.Key.ToString().Equals(httpContext.Session.SessionID))
                            {
                                //判断当前session保存的值是否为被注销值
                                if (idE.Value != null && "XXXXXX".Equals(idE.Value.ToString()))
                                {
                                    //验证被注销则清空session
                                    userOnline.Remove(httpContext.Session.SessionID);
                                    httpContext.Application.Lock();
                                    httpContext.Application["online"] = userOnline;
                                    httpContext.Response.Write("<script>alert('你的帐号在别处登录，你被强迫下线！');location.href='/Load';</script>");
                                    return false;
                                }
                            }
                        }
                        else
                        {
                            return false;
                        }
                    }
                    return true;
                }
                else
                {
                    return false;
                }
            }
            return false;
        }
        /// <summary>
        /// 同一个账号只能再一处登陆
        /// </summary>
        /// <param name="name"></param>
        public void Online(string name)
        {
            HttpContext httpContext = System.Web.HttpContext.Current;
            Hashtable userOnline = (Hashtable)httpContext.Application["Online"];
            if (userOnline != null)
            {
                IDictionaryEnumerator idE = userOnline.GetEnumerator();
                string strKey = string.Empty;
                while (idE.MoveNext())
                {
                    if (idE.Value != null && idE.Value.ToString().Equals(name))
                    {
                        strKey = idE.Key.ToString();
                        userOnline[strKey] = "XXXXXX";
                        break;
                    }
                }
            }
            else
            {
                userOnline = new Hashtable();
            }
            userOnline[httpContext.Session.SessionID] = name;
            httpContext.Application.Lock();
            httpContext.Application["Online"] = userOnline;
            httpContext.Application.UnLock();
        }
    }
}
/*!
 * bootstrap-fileinput v5.1.5
 * http://plugins.krajee.com/file-input
 *
 * Author: <PERSON><PERSON><PERSON>
 * Copyright: 2014 - 2021, <PERSON><PERSON><PERSON>, Krajee.com
 *
 * Licensed under the BSD-3-Clause
 * https://github.com/kartik-v/bootstrap-fileinput/blob/master/LICENSE.md
 */!function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&module.exports?module.exports=e(require("jquery")):e(window.jQuery)}(function(L){"use strict";var N,c;L.fn.fileinputLocales={},L.fn.fileinputThemes={},String.prototype.setTokens=function(e){var t,i,a=this.toString();for(t in e)e.hasOwnProperty(t)&&(i=new RegExp("{"+t+"}","g"),a=a.replace(i,e[t]));return a},Array.prototype.flatMap||(Array.prototype.flatMap=function(e){return[].concat(this.map(e))}),N={FRAMES:".kv-preview-thumb",SORT_CSS:"file-sortable",INIT_FLAG:"init-",OBJECT_PARAMS:'<param name="controller" value="true" />\n<param name="allowFullScreen" value="true" />\n<param name="allowScriptAccess" value="always" />\n<param name="autoPlay" value="false" />\n<param name="autoStart" value="false" />\n<param name="quality" value="high" />\n',DEFAULT_PREVIEW:'<div class="file-preview-other">\n<span class="{previewFileIconClass}">{previewFileIcon}</span>\n</div>',MODAL_ID:"kvFileinputModal",MODAL_EVENTS:["show","shown","hide","hidden","loaded"],logMessages:{ajaxError:"{status}: {error}. Error Details: {text}.",badDroppedFiles:"Error scanning dropped files!",badExifParser:"Error loading the piexif.js library. {details}",badInputType:'The input "type" must be set to "file" for initializing the "bootstrap-fileinput" plugin.',exifWarning:'To avoid this warning, either set "autoOrientImage" to "false" OR ensure you have loaded the "piexif.js" library correctly on your page before the "fileinput.js" script.',invalidChunkSize:'Invalid upload chunk size: "{chunkSize}". Resumable uploads are disabled.',invalidThumb:'Invalid thumb frame with id: "{id}".',noResumableSupport:"The browser does not support resumable or chunk uploads.",noUploadUrl:'The "uploadUrl" is not set. Ajax uploads and resumable uploads have been disabled.',retryStatus:"Retrying upload for chunk # {chunk} for {filename}... retry # {retry}.",chunkQueueError:"Could not push task to ajax pool for chunk index # {index}.",resumableMaxRetriesReached:"Maximum resumable ajax retries ({n}) reached.",resumableRetryError:"Could not retry the resumable request (try # {n})... aborting.",resumableAborting:"Aborting / cancelling the resumable request.",resumableRequestError:"Error processing resumable request. {msg}"},objUrl:window.URL||window.webkitURL,now:function(){return(new Date).getTime()},round:function(e){return e=parseFloat(e),isNaN(e)?0:Math.floor(Math.round(e))},getArray:function(e){for(var t=[],i=e&&e.length||0,a=0;a<i;a++)t.push(e[a]);return t},getFileRelativePath:function(e){return String(e.newPath||e.relativePath||e.webkitRelativePath||N.getFileName(e)||null)},getFileId:function(e,t){var i=N.getFileRelativePath(e);return"function"==typeof t?t(e):e&&i?e.size+"_"+encodeURIComponent(i).replace(/%/g,"_"):null},getFrameSelector:function(e,t){return'[id="'+e+'"]'+(t=t||"")},getZoomSelector:function(e,t){return N.getFrameSelector("zoom-"+e,t)},getFrameElement:function(e,t,i){return e.find(N.getFrameSelector(t,i))},getZoomElement:function(e,t,i){return e.find(N.getZoomSelector(t,i))},getElapsed:function(e){var t=e,i="",a={},r={year:31536e3,month:2592e3,week:604800,day:86400,hour:3600,minute:60,second:1};return N.getObjectKeys(r).forEach(function(e){a[e]=Math.floor(t/r[e]),t-=a[e]*r[e]}),L.each(a,function(e,t){0<t&&(i+=(i?" ":"")+t+e.substring(0,1))}),i},debounce:function(i,a){var r;return function(){var e=arguments,t=this;clearTimeout(r),r=setTimeout(function(){i.apply(t,e)},a)}},stopEvent:function(e){e.stopPropagation(),e.preventDefault()},getFileName:function(e){return e&&(e.fileName||e.name)||""},createObjectURL:function(e){return N.objUrl&&N.objUrl.createObjectURL&&e?N.objUrl.createObjectURL(e):""},revokeObjectURL:function(e){N.objUrl&&N.objUrl.revokeObjectURL&&e&&N.objUrl.revokeObjectURL(e)},compare:function(e,t,i){return void 0!==e&&(i?e===t:e.match(t))},isIE:function(e){var t;return"Microsoft Internet Explorer"===navigator.appName&&(10===e?new RegExp("msie\\s"+e,"i").test(navigator.userAgent):((t=document.createElement("div")).innerHTML="\x3c!--[if IE "+e+"]> <i></i> <![endif]--\x3e",e=t.getElementsByTagName("i").length,document.body.appendChild(t),t.parentNode.removeChild(t),e))},canOrientImage:function(e){var t=L(document.createElement("img")).css({width:"1px",height:"1px"}).insertAfter(e),e=t.css("image-orientation");return t.remove(),!!e},canAssignFilesToInput:function(){var e=document.createElement("input");try{return e.type="file",!(e.files=null)}catch(e){return!1}},getDragDropFolders:function(e){var t,i,a=e?e.length:0,r=0;if(0<a&&e[0].webkitGetAsEntry())for(t=0;t<a;t++)(i=e[t].webkitGetAsEntry())&&i.isDirectory&&r++;return r},initModal:function(e){var t=L("body");t.length&&e.appendTo(t)},isFunction:function(e){return"function"==typeof e},isEmpty:function(e,t){return null==e||!N.isFunction(e)&&(0===e.length||t&&""===L.trim(e))},isArray:function(e){return Array.isArray(e)||"[object Array]"===Object.prototype.toString.call(e)},ifSet:function(e,t,i){return i=i||"",t&&"object"==typeof t&&e in t?t[e]:i},cleanArray:function(e){return e instanceof Array||(e=[]),e.filter(function(e){return null!=e})},spliceArray:function(e,t,i){var a,r,n=0,o=[];if(!(e instanceof Array))return[];for(r=L.extend(!0,[],e),i&&r.reverse(),a=0;a<r.length;a++)a!==t&&(o[n]=r[a],n++);return i&&o.reverse(),o},getNum:function(e,t){return t=t||0,"number"==typeof e?e:("string"==typeof e&&(e=parseFloat(e)),isNaN(e)?t:e)},hasFileAPISupport:function(){return!(!window.File||!window.FileReader)},hasDragDropSupport:function(){var e=document.createElement("div");return!N.isIE(9)&&(void 0!==e.draggable||void 0!==e.ondragstart&&void 0!==e.ondrop)},hasFileUploadSupport:function(){return N.hasFileAPISupport()&&window.FormData},hasBlobSupport:function(){try{return!!window.Blob&&Boolean(new Blob)}catch(e){return!1}},hasArrayBufferViewSupport:function(){try{return 100===new Blob([new Uint8Array(100)]).size}catch(e){return!1}},hasResumableUploadSupport:function(){return N.hasFileUploadSupport()&&N.hasBlobSupport()&&N.hasArrayBufferViewSupport()&&(!!Blob.prototype.webkitSlice||!!Blob.prototype.mozSlice||!!Blob.prototype.slice||!1)},dataURI2Blob:function(e){var t,i,a,r,n=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder,o=N.hasBlobSupport();if(!((o||n)&&window.atob&&window.ArrayBuffer&&window.Uint8Array))return null;for(t=(0<=e.split(",")[0].indexOf("base64")?atob:decodeURIComponent)(e.split(",")[1]),i=new ArrayBuffer(t.length),a=new Uint8Array(i),r=0;r<t.length;r+=1)a[r]=t.charCodeAt(r);return e=e.split(",")[0].split(":")[1].split(";")[0],o?new Blob([N.hasArrayBufferViewSupport()?a:i],{type:e}):((n=new n).append(i),n.getBlob(e))},arrayBuffer2String:function(e){if(window.TextDecoder)return new TextDecoder("utf-8").decode(e);for(var t,i,a,r=Array.prototype.slice.apply(new Uint8Array(e)),n="",o=0,s=r.length;o<s;)switch((t=r[o++])>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:n+=String.fromCharCode(t);break;case 12:case 13:i=r[o++],n+=String.fromCharCode((31&t)<<6|63&i);break;case 14:i=r[o++],a=r[o++],n+=String.fromCharCode((15&t)<<12|(63&i)<<6|(63&a)<<0)}return n},isHtml:function(e){var t=document.createElement("div");t.innerHTML=e;for(var i=t.childNodes,a=i.length;a--;)if(1===i[a].nodeType)return!0;return!1},isSvg:function(e){return e.match(/^\s*<\?xml/i)&&(e.match(/<!DOCTYPE svg/i)||e.match(/<svg/i))},getMimeType:function(e,t,i){switch(e){case"ffd8ffe0":case"ffd8ffe1":case"ffd8ffe2":return"image/jpeg";case"89504e47":return"image/png";case"47494638":return"image/gif";case"49492a00":return"image/tiff";case"52494646":return"image/webp";case"66747970":return"video/3gp";case"4f676753":return"video/ogg";case"1a45dfa3":return"video/mkv";case"000001ba":case"000001b3":return"video/mpeg";case"3026b275":return"video/wmv";case"25504446":return"application/pdf";case"25215053":return"application/ps";case"504b0304":case"504b0506":case"504b0508":return"application/zip";case"377abcaf":return"application/7z";case"75737461":return"application/tar";case"7801730d":return"application/dmg";default:switch(e.substring(0,6)){case"435753":return"application/x-shockwave-flash";case"494433":return"audio/mp3";case"425a68":return"application/bzip";default:switch(e.substring(0,4)){case"424d":return"image/bmp";case"fffb":return"audio/mp3";case"4d5a":return"application/exe";case"1f9d":case"1fa0":return"application/zip";case"1f8b":return"application/gzip";default:return t&&!t.match(/[^\u0000-\u007f]/)?"application/text-plain":i}}}},addCss:function(e,t){e.removeClass(t).addClass(t)},getElement:function(e,t,i){return N.isEmpty(e)||N.isEmpty(e[t])?i:L(e[t])},createElement:function(e,t){return t=t||"div",L(L.parseHTML("<"+t+">"+e+"</"+t+">"))},uniqId:function(){return((new Date).getTime()+Math.floor(Math.random()*Math.pow(10,15))).toString(36)},cspBuffer:{CSP_ATTRIB:"data-csp-01928735",domElementsStyles:{},stash:function(e){var n=this,t=L.parseHTML("<div>"+e+"</div>"),e=L(t);return e.find("[style]").each(function(e,t){var i=L(t),a=i[0].style,t=N.uniqId(),r={};a&&a.length&&(L(a).each(function(){r[this]=a[this]}),n.domElementsStyles[t]=r,i.removeAttr("style").attr(n.CSP_ATTRIB,t))}),e.filter("*").removeAttr("style"),(Object.values?Object.values(t):Object.keys(t).map(function(e){return t[e]})).flatMap(function(e){return e.innerHTML}).join("")},apply:function(e){var a=this;L(e).find("["+a.CSP_ATTRIB+"]").each(function(e,t){var i=L(t),t=i.attr(a.CSP_ATTRIB),t=a.domElementsStyles[t];t&&i.css(t),i.removeAttr(a.CSP_ATTRIB)}),a.domElementsStyles={}}},setHtml:function(e,t){var i=N.cspBuffer;return e.html(i.stash(t)),i.apply(e),e},htmlEncode:function(e,t){return void 0===e?t||null:e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")},replaceTags:function(e,t){var i=e;return t&&L.each(t,function(e,t){"function"==typeof t&&(t=t()),i=i.split(e).join(t)}),i},cleanMemory:function(e){e=(e.is("img")?e:e.find("source")).attr("src");N.revokeObjectURL(e)},findFileName:function(e){var t=e.lastIndexOf("/");return-1===t&&(t=e.lastIndexOf("\\")),e.split(e.substring(t,t+1)).pop()},checkFullScreen:function(){return document.fullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||document.msFullscreenElement},toggleFullScreen:function(e){var t=document,i=t.documentElement,a=N.checkFullScreen();i&&e&&!a?i.requestFullscreen?i.requestFullscreen():i.msRequestFullscreen?i.msRequestFullscreen():i.mozRequestFullScreen?i.mozRequestFullScreen():i.webkitRequestFullscreen&&i.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT):a&&(t.exitFullscreen?t.exitFullscreen():t.msExitFullscreen?t.msExitFullscreen():t.mozCancelFullScreen?t.mozCancelFullScreen():t.webkitExitFullscreen&&t.webkitExitFullscreen())},moveArray:function(e,t,i,a){var r=L.extend(!0,[],e);if(a&&r.reverse(),i>=r.length)for(var n=i-r.length;1+n--;)r.push(void 0);return r.splice(i,0,r.splice(t,1)[0]),a&&r.reverse(),r},closeButton:function(e){return'<button type="button" class="'+(e=e?"close "+e:"close")+'" aria-label="Close">\n  <span aria-hidden="true">&times;</span>\n</button>'},getRotation:function(e){switch(e){case 2:return"rotateY(180deg)";case 3:return"rotate(180deg)";case 4:return"rotate(180deg) rotateY(180deg)";case 5:return"rotate(270deg) rotateY(180deg)";case 6:return"rotate(90deg)";case 7:return"rotate(90deg) rotateY(180deg)";case 8:return"rotate(270deg)";default:return""}},setTransform:function(e,t){e&&(e.style.transform=t,e.style.webkitTransform=t,e.style["-moz-transform"]=t,e.style["-ms-transform"]=t,e.style["-o-transform"]=t)},getObjectKeys:function(e){var t=[];return e&&L.each(e,function(e){t.push(e)}),t},getObjectSize:function(e){return N.getObjectKeys(e).length},whenAll:function(e){for(var t,i,a,r=[].slice,n=1===arguments.length&&N.isArray(e)?e:r.call(arguments),o=L.Deferred(),s=0,l=n.length,d=l,c=i=a=Array(l),u=function(e,t,i){return function(){i!==n&&s++,o.notifyWith(t[e]=this,i[e]=r.call(arguments)),--d||o[(s?"reject":"resolve")+"With"](t,i)}},p=0;p<l;p++)(t=n[p])&&L.isFunction(t.promise)?t.promise().done(u(p,a,n)).fail(u(p,c,i)):(o.notifyWith(this,t),--d);return d||o.resolveWith(a,n),o.promise()}},(c=function(e,t){var i=this;i.$element=L(e),i.$parent=i.$element.parent(),i._validate()&&(i.isPreviewable=N.hasFileAPISupport(),i.isIE9=N.isIE(9),i.isIE10=N.isIE(10),(i.isPreviewable||i.isIE9)&&(i._init(t),i._listen()),i.$element.removeClass("file-loading"))}).prototype={constructor:c,_cleanup:function(){var e=this;e.reader=null,e.clearFileStack(),e.fileBatchCompleted=!0,e.isError=!1,e.isDuplicateError=!1,e.isPersistentError=!1,e.cancelling=!1,e.paused=!1,e.lastProgress=0,e._initAjax()},_isAborted:function(){return this.cancelling||this.paused},_initAjax:function(){var i=this.taskManager={pool:{},addPool:function(e){return i.pool[e]=new i.TasksPool(e)},getPool:function(e){return i.pool[e]},addTask:function(e,t){return new i.Task(e,t)},TasksPool:function(e){var c=this;c.id=e,c.cancelled=!1,c.cancelledDeferrer=L.Deferred(),c.tasks={},c.addTask=function(e,t){return c.tasks[e]=new i.Task(e,t)},c.size=function(){return N.getObjectSize(c.tasks)},c.run=function(e){var t,i,a,r=0,n=!1,o=N.getObjectKeys(c.tasks).map(function(e){return c.tasks[e]}),s=[],l=L.Deferred();if(c.cancelled)return c.cancelledDeferrer.resolve(),l.reject();if(!e){var d=N.getObjectKeys(c.tasks).map(function(e){return c.tasks[e].deferred});return N.whenAll(d).done(function(){var e=N.getArray(arguments);c.cancelled?(l.reject.apply(null,e),c.cancelledDeferrer.resolve()):(l.resolve.apply(null,e),c.cancelledDeferrer.reject())}).fail(function(){var e=N.getArray(arguments);l.reject.apply(null,e),c.cancelled?c.cancelledDeferrer.resolve():c.cancelledDeferrer.reject()}),L.each(c.tasks,function(e){(t=c.tasks[e]).run()}),l}for(i=function(e){L.when(e.deferred).fail(function(){n=!0,a.apply(null,arguments)}).always(a)},a=function(){var e=N.getArray(arguments);if(l.notify(e),s.push(e),c.cancelled)return l.reject.apply(null,s),void c.cancelledDeferrer.resolve();s.length===c.size()&&(n?l.reject:l.resolve).apply(null,s),o.length&&(t=o.shift(),i(t),t.run())};o.length&&r++<e;)t=o.shift(),i(t),t.run();return l},c.cancel=function(){return c.cancelled=!0,c.cancelledDeferrer}},Task:function(e,t){var i=this;i.id=e,i.deferred=L.Deferred(),i.logic=t,i.context=null,i.run=function(){var e=N.getArray(arguments);return e.unshift(i.deferred),t.apply(i.context,e),i.deferred},i.runWithContext=function(e){return i.context=e,i.run()}}};this.ajaxQueue=[],this.ajaxRequests=[],this.ajaxAborted=!1},_init:function(e,t){var i,a,r=this,n=r.$element;r.options=e,r.canOrientImage=N.canOrientImage(n),L.each(e,function(e,t){switch(e){case"minFileCount":case"maxFileCount":case"maxTotalFileCount":case"minFileSize":case"maxFileSize":case"maxFilePreviewSize":case"resizeImageQuality":case"resizeIfSizeMoreThan":case"progressUploadThreshold":case"initialPreviewCount":case"zoomModalHeight":case"minImageHeight":case"maxImageHeight":case"minImageWidth":case"maxImageWidth":r[e]=N.getNum(t);break;default:r[e]=t}}),0<r.maxTotalFileCount&&r.maxTotalFileCount<r.maxFileCount&&(r.maxTotalFileCount=r.maxFileCount),r.rtl&&(a=r.previewZoomButtonIcons.prev,r.previewZoomButtonIcons.prev=r.previewZoomButtonIcons.next,r.previewZoomButtonIcons.next=a),!isNaN(r.maxAjaxThreads)&&r.maxAjaxThreads<r.resumableUploadOptions.maxThreads&&(r.resumableUploadOptions.maxThreads=r.maxAjaxThreads),r._initFileManager(),"function"==typeof r.autoOrientImage&&(r.autoOrientImage=r.autoOrientImage()),"function"==typeof r.autoOrientImageInitial&&(r.autoOrientImageInitial=r.autoOrientImageInitial()),t||r._cleanup(),r.duplicateErrors=[],r.$form=n.closest("form"),r._initTemplateDefaults(),r.uploadFileAttr=N.isEmpty(n.attr("name"))?"file_data":n.attr("name"),i=r._getLayoutTemplate("progress"),r.progressTemplate=i.replace("{class}",r.progressClass),r.progressInfoTemplate=i.replace("{class}",r.progressInfoClass),r.progressPauseTemplate=i.replace("{class}",r.progressPauseClass),r.progressCompleteTemplate=i.replace("{class}",r.progressCompleteClass),r.progressErrorTemplate=i.replace("{class}",r.progressErrorClass),r.isDisabled=n.attr("disabled")||n.attr("readonly"),r.isDisabled&&n.attr("disabled",!0),r.isClickable=r.browseOnZoneClick&&r.showPreview&&(r.dropZoneEnabled||!N.isEmpty(r.defaultPreviewContent)),r.isAjaxUpload=N.hasFileUploadSupport()&&!N.isEmpty(r.uploadUrl),r.dropZoneEnabled=N.hasDragDropSupport()&&r.dropZoneEnabled,r.isAjaxUpload||(r.dropZoneEnabled=r.dropZoneEnabled&&N.canAssignFilesToInput()),r.slug="function"==typeof e.slugCallback?e.slugCallback:r._slugDefault,r.mainTemplate=r.showCaption?r._getLayoutTemplate("main1"):r._getLayoutTemplate("main2"),r.captionTemplate=r._getLayoutTemplate("caption"),r.previewGenericTemplate=r._getPreviewTemplate("generic"),!r.imageCanvas&&r.resizeImage&&(r.maxImageWidth||r.maxImageHeight)&&(r.imageCanvas=document.createElement("canvas"),r.imageCanvasContext=r.imageCanvas.getContext("2d")),N.isEmpty(n.attr("id"))&&n.attr("id",N.uniqId()),r.namespace=".fileinput_"+n.attr("id").replace(/-/g,"_"),void 0===r.$container?r.$container=r._createContainer():r._refreshContainer(),a=r.$container,r.$dropZone=a.find(".file-drop-zone"),r.$progress=a.find(".kv-upload-progress"),r.$btnUpload=a.find(".fileinput-upload"),r.$captionContainer=N.getElement(e,"elCaptionContainer",a.find(".file-caption")),r.$caption=N.getElement(e,"elCaptionText",a.find(".file-caption-name")),N.isEmpty(r.msgPlaceholder)||(i=n.attr("multiple")?r.filePlural:r.fileSingle,r.$caption.attr("placeholder",r.msgPlaceholder.replace("{files}",i))),r.$captionIcon=r.$captionContainer.find(".file-caption-icon"),r.$previewContainer=N.getElement(e,"elPreviewContainer",a.find(".file-preview")),r.$preview=N.getElement(e,"elPreviewImage",a.find(".file-preview-thumbnails")),r.$previewStatus=N.getElement(e,"elPreviewStatus",a.find(".file-preview-status")),r.$errorContainer=N.getElement(e,"elErrorContainer",r.$previewContainer.find(".kv-fileinput-error")),r._validateDisabled(),N.isEmpty(r.msgErrorClass)||N.addCss(r.$errorContainer,r.msgErrorClass),t?r._errorsExist()||r.$errorContainer.hide():(r._resetErrors(),r.$errorContainer.hide(),r.previewInitId="thumb-"+n.attr("id"),r._initPreviewCache(),r._initPreview(!0),r._initPreviewActions(),r.$parent.hasClass("file-loading")&&(r.$container.insertBefore(r.$parent),r.$parent.remove())),r._setFileDropZoneTitle(),n.attr("disabled")&&r.disable(),r._initZoom(),r.hideThumbnailContent&&N.addCss(r.$preview,"hide-content")},_initFileManager:function(){var d=this;d.uploadStartTime=N.now(),d.fileManager={stack:{},filesProcessed:[],errors:[],loadedImages:{},totalImages:0,totalFiles:null,totalSize:null,uploadedSize:0,stats:{},bpsLog:[],bps:0,initStats:function(e){var t={started:N.now()};e?d.fileManager.stats[e]=t:d.fileManager.stats=t},getUploadStats:function(e,t,i){var n=d.fileManager,a=e?n.stats[e]&&n.stats[e].started||N.now():d.uploadStartTime,r=(N.now()-a)/1e3,o=Math.ceil(r?t/r:0),s=i-t,l=n.bpsLog.length?d.bitrateUpdateDelay:0;return setTimeout(function(){var e,t,i,a=0,r=0;for(n.bpsLog.push(o),n.bpsLog.sort(function(e,t){return e-t}),i=10<(t=n.bpsLog.length)?t-10:Math.ceil(t/2),e=t;i<e;e--)r=parseFloat(n.bpsLog[e]),a++;n.bps=64*(0<a?r/a:0)},l),s={fileId:e,started:a,elapsed:r,loaded:t,total:i,bps:n.bps,bitrate:d._getSize(n.bps,["B/s","KB/s","MB/s","GB/s","TB/s","PB/s","EB/s","ZB/s","YB/s"]),pendingBytes:s},e?n.stats[e]=s:n.stats=s,s},exists:function(e){return-1!==L.inArray(e,d.fileManager.getIdList())},count:function(){return d.fileManager.getIdList().length},total:function(){var e=d.fileManager;return e.totalFiles||(e.totalFiles=e.count()),e.totalFiles},getTotalSize:function(){var i=d.fileManager;return i.totalSize||(i.totalSize=0,L.each(d.getFileStack(),function(e,t){t=parseFloat(t.size);i.totalSize+=isNaN(t)?0:t}),i.totalSize)},add:function(e,t){(t=t||d.fileManager.getId(e))&&(d.fileManager.stack[t]={file:e,name:N.getFileName(e),relativePath:N.getFileRelativePath(e),size:e.size,nameFmt:d._getFileName(e,""),sizeFmt:d._getSize(e.size)})},remove:function(e){e=d._getThumbFileId(e);e&&d.fileManager.removeFile(e)},removeFile:function(e){delete d.fileManager.stack[e],delete d.fileManager.loadedImages[e]},move:function(i,a){var r={},n=d.fileManager.stack;(i||a)&&i!==a&&(L.each(n,function(e,t){e!==i&&(r[e]=t),e===a&&(r[i]=n[i])}),d.fileManager.stack=r)},list:function(){var i=[];return L.each(d.getFileStack(),function(e,t){t&&t.file&&i.push(t.file)}),i},isPending:function(e){return-1===L.inArray(e,d.fileManager.filesProcessed)&&d.fileManager.exists(e)},isProcessed:function(){var t=!0,i=d.fileManager;return L.each(d.getFileStack(),function(e){i.isPending(e)&&(t=!1)}),t},clear:function(){var e=d.fileManager;d.isDuplicateError=!1,d.isPersistentError=!1,e.totalFiles=null,e.totalSize=null,e.uploadedSize=0,e.stack={},e.errors=[],e.filesProcessed=[],e.stats={},e.bpsLog=[],e.bps=0,e.clearImages()},clearImages:function(){d.fileManager.loadedImages={},d.fileManager.totalImages=0},addImage:function(e,t){d.fileManager.loadedImages[e]=t},removeImage:function(e){delete d.fileManager.loadedImages[e]},getImageIdList:function(){return N.getObjectKeys(d.fileManager.loadedImages)},getImageCount:function(){return d.fileManager.getImageIdList().length},getId:function(e){return d._getFileId(e)},getIndex:function(e){return d.fileManager.getIdList().indexOf(e)},getThumb:function(t){var i=null;return d._getThumbs().each(function(){var e=L(this);d._getThumbFileId(e)===t&&(i=e)}),i},getThumbIndex:function(e){e=d._getThumbFileId(e);return d.fileManager.getIndex(e)},getIdList:function(){return N.getObjectKeys(d.fileManager.stack)},getFile:function(e){return d.fileManager.stack[e]||null},getFileName:function(e,t){e=d.fileManager.getFile(e);return e?t?e.nameFmt||"":e.name||"":""},getFirstFile:function(){var e=d.fileManager.getIdList(),e=e&&e.length?e[0]:null;return d.fileManager.getFile(e)},setFile:function(e,t){d.fileManager.getFile(e)?d.fileManager.stack[e].file=t:d.fileManager.add(t,e)},setProcessed:function(e){d.fileManager.filesProcessed.push(e)},getProgress:function(){var e=d.fileManager.total(),t=d.fileManager.filesProcessed.length;return e?Math.ceil(t/e*100):0},setProgress:function(e,t){e=d.fileManager.getFile(e);!isNaN(t)&&e&&(e.progress=t)}}},_setUploadData:function(i,e){var a=this;L.each(e,function(e,t){e=a.uploadParamNames[e]||e;N.isArray(t)?i.append(e,t[0],t[1]):i.append(e,t)})},_initResumableUpload:function(){var g,m=this,h=m.resumableUploadOptions,v=N.logMessages,w=m.fileManager;if(m.enableResumableUpload){if(!1!==h.fallback&&"function"!=typeof h.fallback&&(h.fallback=function(e){e._log(v.noResumableSupport),e.enableResumableUpload=!1}),N.hasResumableUploadSupport()||!1===h.fallback)return!m.uploadUrl&&m.enableResumableUpload?(m._log(v.noUploadUrl),void(m.enableResumableUpload=!1)):(h.chunkSize=parseFloat(h.chunkSize),h.chunkSize<=0||isNaN(h.chunkSize)?(m._log(v.invalidChunkSize,{chunkSize:h.chunkSize}),void(m.enableResumableUpload=!1)):void(g=m.resumableManager={init:function(e,t,i){g.logs=[],g.stack=[],g.error="",g.id=e,g.file=t.file,g.fileName=t.name,g.fileIndex=i,g.completed=!1,g.lastProgress=0,m.showPreview&&(g.$thumb=w.getThumb(e)||null,g.$progress=g.$btnDelete=null,g.$thumb&&g.$thumb.length&&(g.$progress=g.$thumb.find(".file-thumb-progress"),g.$btnDelete=g.$thumb.find(".kv-file-remove"))),g.chunkSize=1024*h.chunkSize,g.chunkCount=g.getTotalChunks()},setAjaxError:function(e,t,i,a){e.responseJSON&&e.responseJSON.error&&(i=e.responseJSON.error.toString()),a||(g.error=i),h.showErrorLog&&m._log(v.ajaxError,{status:e.status,error:i,text:e.responseText||""})},reset:function(){g.stack=[],g.chunksProcessed={}},setProcessed:function(e){var t=g.id,i=g.$thumb,a=g.$progress,r=i&&i.length,n={id:r?i.attr("id"):"",index:w.getIndex(t),fileId:t},o=m.resumableUploadOptions.skipErrorsAndProceed;g.completed=!0,g.lastProgress=0,r&&i.removeClass("file-uploading"),"success"===e?(w.uploadedSize+=g.file.size,m.showPreview&&(m._setProgress(101,a),m._setThumbStatus(i,"Success"),m._initUploadSuccess(g.chunksProcessed[t].data,i)),w.removeFile(t),delete g.chunksProcessed[t],m._raise("fileuploaded",[n.id,n.index,n.fileId]),w.isProcessed()&&m._setProgress(101)):"cancel"!==e&&(m.showPreview&&(m._setThumbStatus(i,"Error"),m._setPreviewError(i,!0),m._setProgress(101,a,m.msgProgressError),m._setProgress(101,m.$progress,m.msgProgressError),m.cancelling=!o),m.$errorContainer.find('li[data-file-id="'+n.fileId+'"]').length||(i={file:g.fileName,max:h.maxRetries,error:g.error},a=m.msgResumableUploadRetriesExceeded.setTokens(i),L.extend(n,i),m._showFileError(a,n,"filemaxretries"),o&&(w.removeFile(t),delete g.chunksProcessed[t],w.isProcessed()&&m._setProgress(101)))),w.isProcessed()&&g.reset()},check:function(){L.each(g.logs,function(e,t){if(!t)return!1})},processedResumables:function(){var e,t=g.logs,i=0;if(!t||!t.length)return 0;for(e=0;e<t.length;e++)!0===t[e]&&i++;return i},getUploadedSize:function(){var e=g.processedResumables()*g.chunkSize;return e>g.file.size?g.file.size:e},getTotalChunks:function(){var e=parseFloat(g.chunkSize);return!isNaN(e)&&0<e?Math.ceil(g.file.size/e):0},getProgress:function(){var e=g.processedResumables(),t=g.chunkCount;return 0===t?0:Math.ceil(e/t*100)},checkAborted:function(e){m._isAborted()&&(clearInterval(e),m.unlock())},upload:function(){var t=w.getIdList(),i="new",a=setInterval(function(){var e;g.checkAborted(a),"new"===i&&(m.lock(),i="processing",e=t.shift(),w.initStats(e),w.stack[e]&&(g.init(e,w.stack[e],w.getIndex(e)),g.processUpload())),!w.isPending(e)&&g.completed&&(i="new"),w.isProcessed()&&((e=m.$preview.find(".file-preview-initial")).length&&(N.addCss(e,N.SORT_CSS),m._initSortable()),clearInterval(a),m._clearFileInput(),m.unlock(),setTimeout(function(){var e=m.previewCache.data;e&&(m.initialPreview=e.content,m.initialPreviewConfig=e.config,m.initialPreviewThumbTags=e.tags),m._raise("filebatchuploadcomplete",[m.initialPreview,m.initialPreviewConfig,m.initialPreviewThumbTags,m._getExtraData()])},m.processDelay))},m.processDelay)},uploadResumable:function(){for(var e=m.taskManager,t=g.chunkCount,i=e.addPool(g.id),a=0;a<t;a++)g.logs[a]=!(!g.chunksProcessed[g.id]||!g.chunksProcessed[g.id][a]),g.logs[a]||g.pushAjax(a,0);i.run(h.maxThreads).done(function(){g.setProcessed("success")}).fail(function(){g.setProcessed(i.cancelled?"cancel":"error")})},processUpload:function(){var r,e,t,i,a,n,o=g.id;h.testUrl?(r=new FormData,a=w.stack[o],m._setUploadData(r,{fileId:o,fileName:a.fileName,fileSize:a.size,fileRelativePath:a.relativePath,chunkSize:g.chunkSize,chunkCount:g.chunkCount}),e=function(e){n=m._getOutData(r,e),m._raise("filetestbeforesend",[o,w,g,n])},t=function(e,t,i){n=m._getOutData(r,i,e);var a=m.uploadParamNames.chunksUploaded||"chunksUploaded",i=[o,w,g,n];e[a]&&N.isArray(e[a])?(g.chunksProcessed[o]||(g.chunksProcessed[o]={}),L.each(e[a],function(e,t){g.logs[t]=!0,g.chunksProcessed[o][t]=!0}),g.chunksProcessed[o].data=e,m._raise("filetestsuccess",i)):m._raise("filetesterror",i),g.uploadResumable()},i=function(e,t,i){n=m._getOutData(r,e),m._raise("filetestajaxerror",[o,w,g,n]),g.setAjaxError(e,t,i,!0),g.uploadResumable()},a=function(){m._raise("filetestcomplete",[o,w,g,m._getOutData(r)])},m._ajaxSubmit(e,t,a,i,r,o,g.fileIndex,h.testUrl)):g.uploadResumable()},pushAjax:function(e,t){var i=m.taskManager.getPool(g.id);i.addTask(i.size()+1,function(e){var t=g.stack.shift(),i=t[0];g.chunksProcessed[g.id]&&g.chunksProcessed[g.id][i]?m._log(v.chunkQueueError,{index:i}):g.sendAjax(i,t[1],e)}),g.stack.push([e,t])},sendAjax:function(r,n,o){function s(e,t){t&&(e=e.setTokens(t)),e=d.resumableRequestError.setTokens({msg:e}),m._log(e),o.reject(e)}var e=g.chunkSize,l=g.id,t=g.file,i=g.$thumb,d=N.logMessages,a=g.$btnDelete;if(!g.chunksProcessed[l]||!g.chunksProcessed[l][r]){if(n>h.maxRetries)return s(d.resumableMaxRetriesReached,{n:h.maxRetries}),void g.setProcessed("error");var c,u=t[t.slice?"slice":t.mozSlice?"mozSlice":t.webkitSlice?"webkitSlice":"slice"](e*r,e*(r+1)),p=new FormData,f=w.stack[l];m._setUploadData(p,{chunkCount:g.chunkCount,chunkIndex:r,chunkSize:e,chunkSizeStart:e*r,fileBlob:[u,g.fileName],fileId:l,fileName:g.fileName,fileRelativePath:f.relativePath,fileSize:t.size,retryCount:n}),g.$progress&&g.$progress.length&&g.$progress.show(),e=function(e){c=m._getOutData(p,e),m.showPreview&&(i.hasClass("file-preview-success")||(m._setThumbStatus(i,"Loading"),N.addCss(i,"file-uploading")),a.attr("disabled",!0)),m._raise("filechunkbeforesend",[l,r,n,w,g,c])},u=function(e,t,i){var a;m._isAborted()?s(d.resumableAborting):(c=m._getOutData(p,i,e),a=m.uploadParamNames.chunkIndex||"chunkIndex",i=[l,r,n,w,g,c],e.error?(h.showErrorLog&&m._log(v.retryStatus,{retry:n+1,filename:g.fileName,chunk:r}),m._raise("filechunkerror",i),g.pushAjax(r,n+1),g.error=e.error,s(e.error)):(g.logs[e[a]]=!0,g.chunksProcessed[l]||(g.chunksProcessed[l]={}),g.chunksProcessed[l][e[a]]=!0,g.chunksProcessed[l].data=e,o.resolve.call(null,e),m._raise("filechunksuccess",i),g.check()))},f=function(e,t,i){m._isAborted()?s(d.resumableAborting):(c=m._getOutData(p,e),g.setAjaxError(e,t,i),m._raise("filechunkajaxerror",[l,r,n,w,g,c]),g.pushAjax(r,n+1),s(d.resumableRetryError,{n:n-1}))},t=function(){m._isAborted()||m._raise("filechunkcomplete",[l,r,n,w,g,m._getOutData(p)])},m._ajaxSubmit(e,u,t,f,p,l,g.fileIndex)}}}).reset());h.fallback(m)}},_initTemplateDefaults:function(){var i=this,e=function(e,t){return'<object class="kv-preview-data file-preview-'+e+'" title="{caption}" data="{data}" type="'+t+'"'+o+">\n"+N.DEFAULT_PREVIEW+"\n</object>\n"},t=N.closeButton("fileinput-remove"),a=N.MODAL_ID+"Label",r='<div id="'+N.MODAL_ID+'" class="file-zoom-dialog modal fade" tabindex="-1" aria-labelledby="'+a+'"></div>',n='<div class="modal-dialog modal-lg{rtl}" role="document">\n  <div class="modal-content">\n    <div class="modal-header">\n      <h5 class="modal-title" id="'+a+'">{heading}</h5>\n      <span class="kv-zoom-title"></span>\n      <div class="kv-zoom-actions">{toggleheader}{fullscreen}{borderless}{close}</div>\n    </div>\n    <div class="modal-body">\n      <div class="floating-buttons"></div>\n      <div class="kv-zoom-body file-zoom-content {zoomFrameClass}"></div>\n{prev} {next}\n    </div>\n  </div>\n</div>\n',o=" {style}",s=e("html","text/html"),l=e("text","text/plain;charset=UTF-8"),d=e("pdf","application/pdf"),c='<img src="{data}" class="file-preview-image kv-preview-data" title="{title}" alt="{alt}"'+o+">\n",u='<iframe class="kv-preview-data file-preview-office" src="https://view.officeapps.live.com/op/embed.aspx?src={data}"'+o+"></iframe>",p='<iframe class="kv-preview-data file-preview-gdocs" src="https://docs.google.com/gview?url={data}&embedded=true"'+o+"></iframe>",f='<video class="kv-preview-data file-preview-video" controls'+o+'>\n<source src="{data}" type="{type}">\n'+N.DEFAULT_PREVIEW+"\n</video>\n",g='\x3c!--suppress ALL --\x3e<audio class="kv-preview-data file-preview-audio" controls'+o+'>\n<source src="{data}" type="{type}">\n'+N.DEFAULT_PREVIEW+"\n</audio>\n",m='<embed class="kv-preview-data file-preview-flash" src="{data}" type="application/x-shockwave-flash"'+o+">\n",a='<object class="kv-preview-data file-preview-object file-object {typeCss}" data="{data}" type="{type}"'+o+'>\n<param name="movie" value="{caption}" />\n'+N.OBJECT_PARAMS+" "+N.DEFAULT_PREVIEW+"\n</object>\n",h='<div class="kv-preview-data file-preview-other-frame"'+o+">\n"+N.DEFAULT_PREVIEW+"\n</div>\n",e={width:"100%",height:"100%","min-height":"480px"};i._isPdfRendered()&&(d=i.pdfRendererTemplate.replace("{renderer}",i._encodeURI(i.pdfRendererUrl))),i.defaults={layoutTemplates:{main1:'{preview}\n<div class="kv-upload-progress kv-hidden"></div><div class="clearfix"></div>\n<div class="input-group {class}">\n  {caption}\n<div class="input-group-btn input-group-append">\n      {remove}\n      {cancel}\n      {pause}\n      {upload}\n      {browse}\n    </div>\n</div>',main2:'{preview}\n<div class="kv-upload-progress kv-hidden"></div>\n<div class="clearfix"></div>\n{remove}\n{cancel}\n{upload}\n{browse}\n',preview:'<div class="file-preview {class}">\n  {close}  <div class="{dropClass} clearfix">\n    <div class="file-preview-thumbnails clearfix">\n    </div>\n    <div class="file-preview-status text-center text-success"></div>\n    <div class="kv-fileinput-error"></div>\n  </div>\n</div>',close:t,fileIcon:'<i class="glyphicon glyphicon-file"></i>',caption:'<div class="file-caption form-control {class}" tabindex="500">\n  <span class="file-caption-icon"></span>\n  <input class="file-caption-name">\n</div>',modalMain:r,modal:n,progress:'<div class="progress">\n    <div class="{class}" role="progressbar" aria-valuenow="{percent}" aria-valuemin="0" aria-valuemax="100" style="width:{percent}%;">\n        {status}\n     </div>\n</div>{stats}',stats:'<div class="text-info file-upload-stats"><span class="pending-time">{pendingTime}</span> <span class="upload-speed">{uploadSpeed}</span></div>',size:" <samp>({sizeText})</samp>",footer:'<div class="file-thumbnail-footer">\n    <div class="file-footer-caption" title="{caption}">\n        <div class="file-caption-info">{caption}</div>\n        <div class="file-size-info">{size}</div>\n    </div>\n    {progress}\n{indicator}\n{actions}\n</div>',indicator:'<div class="file-upload-indicator" title="{indicatorTitle}">{indicator}</div>',actions:'<div class="file-actions">\n    <div class="file-footer-buttons">\n        {download} {upload} {delete} {zoom} {other}    </div>\n</div>\n{drag}\n<div class="clearfix"></div>',actionDelete:'<button type="button" class="kv-file-remove {removeClass}" title="{removeTitle}" {dataUrl}{dataKey}>{removeIcon}</button>\n',actionUpload:'<button type="button" class="kv-file-upload {uploadClass}" title="{uploadTitle}">{uploadIcon}</button>',actionDownload:'<a class="kv-file-download {downloadClass}" title="{downloadTitle}" href="{downloadUrl}" download="{caption}" target="_blank">{downloadIcon}</a>',actionZoom:'<button type="button" class="kv-file-zoom {zoomClass}" title="{zoomTitle}">{zoomIcon}</button>',actionDrag:'<span class="file-drag-handle {dragClass}" title="{dragTitle}">{dragIcon}</span>',btnDefault:'<button type="{type}" tabindex="500" title="{title}" class="{css}" {status}>{icon} {label}</button>',btnLink:'<a href="{href}" tabindex="500" title="{title}" class="{css}" {status}>{icon} {label}</a>',btnBrowse:'<div tabindex="500" class="{css}" {status}>{icon} {label}</div>',zoomCache:'<div class="kv-zoom-cache">{zoomContent}</div>'},previewMarkupTags:{tagBefore1:'<div class="file-preview-frame {frameClass}" id="{previewId}" data-fileindex="{fileindex}" data-fileid="{fileid}" data-template="{template}"><div class="kv-file-content">\n',tagBefore2:'<div class="file-preview-frame {frameClass}" id="{previewId}" data-fileindex="{fileindex}" data-fileid="{fileid}" data-template="{template}" title="{caption}"><div class="kv-file-content">\n',tagAfter:"</div>{footer}\n{zoomCache}</div>\n"},previewContentTemplates:{generic:"{content}\n",html:s,image:c,text:l,office:u,gdocs:p,video:f,audio:g,flash:m,object:a,pdf:d,other:h},allowedPreviewTypes:["image","html","text","video","audio","flash","pdf","object"],previewTemplates:{},previewSettings:{image:{width:"auto",height:"auto","max-width":"100%","max-height":"100%"},html:{width:"213px",height:"160px"},text:{width:"213px",height:"160px"},office:{width:"213px",height:"160px"},gdocs:{width:"213px",height:"160px"},video:{width:"213px",height:"160px"},audio:{width:"100%",height:"30px"},flash:{width:"213px",height:"160px"},object:{width:"213px",height:"160px"},pdf:{width:"100%",height:"160px",position:"relative"},other:{width:"213px",height:"160px"}},previewSettingsSmall:{image:{width:"auto",height:"auto","max-width":"100%","max-height":"100%"},html:{width:"100%",height:"160px"},text:{width:"100%",height:"160px"},office:{width:"100%",height:"160px"},gdocs:{width:"100%",height:"160px"},video:{width:"100%",height:"auto"},audio:{width:"100%",height:"30px"},flash:{width:"100%",height:"auto"},object:{width:"100%",height:"auto"},pdf:{width:"100%",height:"160px"},other:{width:"100%",height:"160px"}},previewZoomSettings:{image:{width:"auto",height:"auto","max-width":"100%","max-height":"100%"},html:e,text:e,office:{width:"100%",height:"100%","max-width":"100%","min-height":"480px"},gdocs:{width:"100%",height:"100%","max-width":"100%","min-height":"480px"},video:{width:"auto",height:"100%","max-width":"100%"},audio:{width:"100%",height:"30px"},flash:{width:"auto",height:"480px"},object:{width:"auto",height:"100%","max-width":"100%","min-height":"480px"},pdf:e,other:{width:"auto",height:"100%","min-height":"480px"}},mimeTypeAliases:{"video/quicktime":"video/mp4"},fileTypeSettings:{image:function(e,t){return N.compare(e,"image.*")&&!N.compare(e,/(tiff?|wmf)$/i)||N.compare(t,/\.(gif|png|jpe?g)$/i)},html:function(e,t){return N.compare(e,"text/html")||N.compare(t,/\.(htm|html)$/i)},office:function(e,t){return N.compare(e,/(word|excel|powerpoint|office)$/i)||N.compare(t,/\.(docx?|xlsx?|pptx?|pps|potx?)$/i)},gdocs:function(e,t){return N.compare(e,/(word|excel|powerpoint|office|iwork-pages|tiff?)$/i)||N.compare(t,/\.(docx?|xlsx?|pptx?|pps|potx?|rtf|ods|odt|pages|ai|dxf|ttf|tiff?|wmf|e?ps)$/i)},text:function(e,t){return N.compare(e,"text.*")||N.compare(t,/\.(xml|javascript)$/i)||N.compare(t,/\.(txt|md|nfo|ini|json|php|js|css)$/i)},video:function(e,t){return N.compare(e,"video.*")&&(N.compare(e,/(ogg|mp4|mp?g|mov|webm|3gp)$/i)||N.compare(t,/\.(og?|mp4|webm|mp?g|mov|3gp)$/i))},audio:function(e,t){return N.compare(e,"audio.*")&&(N.compare(t,/(ogg|mp3|mp?g|wav)$/i)||N.compare(t,/\.(og?|mp3|mp?g|wav)$/i))},flash:function(e,t){return N.compare(e,"application/x-shockwave-flash",!0)||N.compare(t,/\.(swf)$/i)},pdf:function(e,t){return N.compare(e,"application/pdf",!0)||N.compare(t,/\.(pdf)$/i)},object:function(){return!0},other:function(){return!0}},fileActionSettings:{showRemove:!0,showUpload:!0,showDownload:!0,showZoom:!0,showDrag:!0,removeIcon:'<i class="glyphicon glyphicon-trash"></i>',removeClass:"btn btn-sm btn-kv btn-default btn-outline-secondary",removeErrorClass:"btn btn-sm btn-kv btn-danger",removeTitle:"Remove file",uploadIcon:'<i class="glyphicon glyphicon-upload"></i>',uploadClass:"btn btn-sm btn-kv btn-default btn-outline-secondary",uploadTitle:"Upload file",uploadRetryIcon:'<i class="glyphicon glyphicon-repeat"></i>',uploadRetryTitle:"Retry upload",downloadIcon:'<i class="glyphicon glyphicon-download"></i>',downloadClass:"btn btn-sm btn-kv btn-default btn-outline-secondary",downloadTitle:"Download file",zoomIcon:'<i class="glyphicon glyphicon-zoom-in"></i>',zoomClass:"btn btn-sm btn-kv btn-default btn-outline-secondary",zoomTitle:"View Details",dragIcon:'<i class="glyphicon glyphicon-move"></i>',dragClass:"text-info",dragTitle:"Move / Rearrange",dragSettings:{},indicatorNew:'<i class="glyphicon glyphicon-plus-sign text-warning"></i>',indicatorSuccess:'<i class="glyphicon glyphicon-ok-sign text-success"></i>',indicatorError:'<i class="glyphicon glyphicon-exclamation-sign text-danger"></i>',indicatorLoading:'<i class="glyphicon glyphicon-hourglass text-muted"></i>',indicatorPaused:'<i class="glyphicon glyphicon-pause text-primary"></i>',indicatorNewTitle:"Not uploaded yet",indicatorSuccessTitle:"Uploaded",indicatorErrorTitle:"Upload Error",indicatorLoadingTitle:"Uploading &hellip;",indicatorPausedTitle:"Upload Paused"}},L.each(i.defaults,function(e,t){"allowedPreviewTypes"!==e?i[e]=L.extend(!0,{},t,i[e]):void 0===i.allowedPreviewTypes&&(i.allowedPreviewTypes=t)}),i._initPreviewTemplates()},_initPreviewTemplates:function(){var i,a=this,r=a.previewMarkupTags,n=r.tagAfter;L.each(a.previewContentTemplates,function(e,t){N.isEmpty(a.previewTemplates[e])&&(i=r.tagBefore2,"generic"!==e&&"image"!==e||(i=r.tagBefore1),a._isPdfRendered()&&"pdf"===e&&(i=i.replace("kv-file-content","kv-file-content kv-pdf-rendered")),a.previewTemplates[e]=i+t+n)})},_initPreviewCache:function(){var f=this;f.previewCache={data:{},init:function(){var e=f.initialPreview;0<e.length&&!N.isArray(e)&&(e=e.split(f.initialPreviewDelimiter)),f.previewCache.data={content:e,config:f.initialPreviewConfig,tags:f.initialPreviewThumbTags}},count:function(e){return f.previewCache.data&&f.previewCache.data.content?(e?f.previewCache.data.content.filter(function(e){return null!==e}):f.previewCache.data.content).length:0},get:function(e,t){function i(e,t,i,a,r,n,o,s){var l=" file-preview-initial "+N.SORT_CSS+(o?" "+o:""),d=f.previewInitId+"-"+n,o=c&&c.fileId||d;return f._generatePreviewTemplate(e,t,i,a,d,o,!1,null,l,r,n,s,p,c&&c.zoomData||t)}var a,r,n,o,s=N.INIT_FLAG+e,l=f.previewCache.data,c=l.config[e],d=l.content[e],u=N.ifSet("previewAsData",c,f.initialPreviewAsData),p=c?{title:c.title||null,alt:c.alt||null}:{title:null,alt:null};return d&&d.length?(t=void 0===t||t,a=N.ifSet("type",c,f.initialPreviewFileType||"generic"),n=N.ifSet("filename",c,N.ifSet("caption",c)),o=N.ifSet("filetype",c,a),r=f.previewCache.footer(e,t,c&&c.size||null),t=N.ifSet("frameClass",c),a=u?i(a,d,n,o,r,s,t):i("generic",d,n,o,r,s,t,a).setTokens({content:l.content[e]}),l.tags.length&&l.tags[e]&&(a=N.replaceTags(a,l.tags[e])),N.isEmpty(c)||N.isEmpty(c.frameAttr)||((e=N.createElement(a)).find(".file-preview-initial").attr(c.frameAttr),a=e.html(),e.remove()),a):""},clean:function(e){e.content=N.cleanArray(e.content),e.config=N.cleanArray(e.config),e.tags=N.cleanArray(e.tags),f.previewCache.data=e},add:function(e,t,i,a){var r,n=f.previewCache.data;return e&&e.length?(r=e.length-1,N.isArray(e)||(e=e.split(f.initialPreviewDelimiter)),a&&n.content?(r=n.content.push(e[0])-1,n.config[r]=t,n.tags[r]=i):(n.content=e,n.config=t,n.tags=i),f.previewCache.clean(n),r):0},set:function(e,t,i,a){var r,n=f.previewCache.data;if(e&&e.length&&(N.isArray(e)||(e=e.split(f.initialPreviewDelimiter)),e.filter(function(e){return null!==e}).length)){if(void 0===n.content&&(n.content=[]),void 0===n.config&&(n.config=[]),void 0===n.tags&&(n.tags=[]),a){for(r=0;r<e.length;r++)e[r]&&n.content.push(e[r]);for(r=0;r<t.length;r++)t[r]&&n.config.push(t[r]);for(r=0;r<i.length;r++)i[r]&&n.tags.push(i[r])}else n.content=e,n.config=t,n.tags=i;f.previewCache.clean(n)}},unset:function(e){var t=f.previewCache.count(),i=f.reversePreviewOrder;if(t){if(1===t)return f.previewCache.data.content=[],f.previewCache.data.config=[],f.previewCache.data.tags=[],f.initialPreview=[],f.initialPreviewConfig=[],void(f.initialPreviewThumbTags=[]);f.previewCache.data.content=N.spliceArray(f.previewCache.data.content,e,i),f.previewCache.data.config=N.spliceArray(f.previewCache.data.config,e,i),f.previewCache.data.tags=N.spliceArray(f.previewCache.data.tags,e,i);i=L.extend(!0,{},f.previewCache.data);f.previewCache.clean(i)}},out:function(){var e,t,i="",a=f.previewCache.count();if(0===a)return{content:"",caption:""};for(e=0;e<a;e++)t=f.previewCache.get(e),i=f.reversePreviewOrder?t+i:i+t;return{content:i,caption:f._getMsgSelected(a)}},footer:function(e,t,i){var a=f.previewCache.data||{};if(N.isEmpty(a.content))return"";(N.isEmpty(a.config)||N.isEmpty(a.config[e]))&&(a.config[e]={}),t=void 0===t||t;var r=a.config[e],n=N.ifSet("caption",r),o=N.ifSet("width",r,"auto"),s=N.ifSet("url",r,!1),l=N.ifSet("key",r,null),d=N.ifSet("fileId",r,null),c=f.fileActionSettings,u=f.initialPreviewShowDelete||!1,p=f.initialPreviewDownloadUrl?f.initialPreviewDownloadUrl+"?key="+l+(d?"&fileId="+d:""):"",a=r.downloadUrl||p,e=r.filename||r.caption||"",d=!!a,p=N.ifSet("showRemove",r,u),u=N.ifSet("showDownload",r,N.ifSet("showDownload",c,d)),d=N.ifSet("showZoom",r,N.ifSet("showZoom",c,!0)),c=N.ifSet("showDrag",r,N.ifSet("showDrag",c,!0)),t=!1===s&&t,u=u&&!1!==r.downloadUrl&&!!a,e=f._renderFileActions(r,!1,u,p,d,c,t,s,l,!0,a,e);return f._getLayoutTemplate("footer").setTokens({progress:f._renderThumbProgress(),actions:e,caption:n,size:f._getSize(i),width:o,indicator:""})}},f.previewCache.init()},_isPdfRendered:function(){var e=this.usePdfRenderer;return("function"==typeof e?e():!!e)&&this.pdfRendererUrl},_handler:function(e,t,i){var a=this.namespace,a=t.split(" ").join(a+" ")+a;e&&e.length&&e.off(a).on(a,i)},_encodeURI:function(e){return this.encodeUrl?encodeURI(e):e},_log:function(e,t){var i=this.$element.attr("id");this.showConsoleLogs&&(i&&(e='"'+i+'": '+e),e="bootstrap-fileinput: "+e,"object"==typeof t&&(e=e.setTokens(t)),window.console&&void 0!==window.console.log?window.console.log(e):window.alert(e))},_validate:function(){var e="file"===this.$element.attr("type");return e||this._log(N.logMessages.badInputType),e},_errorsExist:function(){var e;return!!this.$errorContainer.find("li").length||((e=N.createElement(this.$errorContainer.html())).find(".kv-error-close").remove(),e.find("ul").remove(),!!L.trim(e.text()).length)},_errorHandler:function(e,t){var i=this,a=e.target.error,e=function(e){i._showError(e.replace("{name}",t))};a.code===a.NOT_FOUND_ERR?e(i.msgFileNotFound):a.code===a.SECURITY_ERR?e(i.msgFileSecured):a.code===a.NOT_READABLE_ERR?e(i.msgFileNotReadable):a.code===a.ABORT_ERR?e(i.msgFilePreviewAborted):e(i.msgFilePreviewError)},_addError:function(e){var t=this,i=t.$errorContainer;e&&i.length&&(N.setHtml(i,t.errorCloseButton+e),t._handler(i.find(".kv-error-close"),"click",function(){setTimeout(function(){t.showPreview&&!t.getFrames().length&&t.clear(),i.fadeOut("slow")},t.processDelay)}))},_setValidationError:function(e){e=(e?e+" ":"")+"has-error",this.$container.removeClass(e).addClass("has-error"),N.addCss(this.$captionContainer,"is-invalid")},_resetErrors:function(e){var t=this,i=t.$errorContainer,a=t.resumableUploadOptions.retainErrorHistory;t.isPersistentError||t.enableResumableUpload&&a||(t.isError=!1,t.$container.removeClass("has-error"),t.$captionContainer.removeClass("is-invalid"),i.html(""),e?i.fadeOut("slow"):i.hide())},_showFolderError:function(e){var t,i=this,a=i.$errorContainer;e&&(i.isAjaxUpload||i._clearFileInput(),t=i.msgFoldersNotAllowed.replace("{n}",e),i._addError(t),i._setValidationError(),a.fadeIn(i.fadeDelay),i._raise("filefoldererror",[e,t]))},_showFileError:function(e,t,i){var a=this,r=a.$errorContainer,n=i||"fileuploaderror",i=t&&t.fileId||"",i=t&&t.id?'<li data-thumb-id="'+t.id+'" data-file-id="'+i+'">'+e+"</li>":"<li>"+e+"</li>";return 0===r.find("ul").length?a._addError("<ul>"+i+"</ul>"):r.find("ul").append(i),r.fadeIn(a.fadeDelay),a._raise(n,[t,e]),a._setValidationError("file-input-new"),!0},_showError:function(e,t,i){var a=this,r=a.$errorContainer,i=i||"fileerror";return(t=t||{}).reader=a.reader,a._addError(e),r.fadeIn(a.fadeDelay),a._raise(i,[t,e]),a.isAjaxUpload||a._clearFileInput(),a._setValidationError("file-input-new"),a.$btnUpload.attr("disabled",!0),!0},_noFilesError:function(e){var t=this,i=1<t.minFileCount?t.filePlural:t.fileSingle,a=t.msgFilesTooLess.replace("{n}",t.minFileCount).replace("{files}",i),i=t.$errorContainer,a="<li>"+a+"</li>";0===i.find("ul").length?t._addError("<ul>"+a+"</ul>"):i.find("ul").append(a),t.isError=!0,t._updateFileDetails(0),i.fadeIn(t.fadeDelay),t._raise("fileerror",[e,a]),t._clearFileInput(),t._setValidationError()},_parseError:function(e,t,i,a){var r=this,n=L.trim(i+""),i=t.responseJSON&&t.responseJSON.error?t.responseJSON.error.toString():"",t=i||t.responseText;return r.cancelling&&r.msgUploadAborted&&(n=r.msgUploadAborted),r.showAjaxErrorDetails&&t&&(i?n=L.trim(i+""):(i=(t=L.trim(t.replace(/\n\s*\n/g,"\n"))).length?"<pre>"+t+"</pre>":"",n+=n?i:t)),n=n||r.msgAjaxError.replace("{operation}",e),r.cancelling=!1,a?"<b>"+a+": </b>"+n:n},_parseFileType:function(e,t){var i,a,r=this.allowedPreviewTypes||[];if("application/text-plain"===e)return"text";for(a=0;a<r.length;a++)if(i=r[a],i=(0,this.fileTypeSettings[i])(e,t)?i:"",!N.isEmpty(i))return i;return"other"},_getPreviewIcon:function(e){var i,a=this,r=null;return e&&-1<e.indexOf(".")&&(i=e.split(".").pop(),a.previewFileIconSettings&&(r=a.previewFileIconSettings[i]||a.previewFileIconSettings[i.toLowerCase()]||null),a.previewFileExtSettings&&L.each(a.previewFileExtSettings,function(e,t){a.previewFileIconSettings[e]&&t(i)&&(r=a.previewFileIconSettings[e])})),r||a.previewFileIcon},_parseFilePreviewIcon:function(e,t){t=this._getPreviewIcon(t);return-1<e.indexOf("{previewFileIcon}")&&(e=e.setTokens({previewFileIconClass:this.previewFileIconClass,previewFileIcon:t})),e},_raise:function(e,t){var i=L.Event(e);if(void 0!==t?this.$element.trigger(i,t):this.$element.trigger(i),i.isDefaultPrevented()||!1===i.result)return!1;switch(e){case"filebatchuploadcomplete":case"filebatchuploadsuccess":case"fileuploaded":case"fileclear":case"filecleared":case"filereset":case"fileerror":case"filefoldererror":case"fileuploaderror":case"filebatchuploaderror":case"filedeleteerror":case"filecustomerror":case"filesuccessremove":break;default:this.ajaxAborted||(this.ajaxAborted=i.result)}return!0},_listenFullScreen:function(e){var t,i,a=this.$modal;a&&a.length&&(t=a&&a.find(".btn-fullscreen"),i=a&&a.find(".btn-borderless"),t.length&&i.length&&(t.removeClass("active").attr("aria-pressed","false"),i.removeClass("active").attr("aria-pressed","false"),(e?t:i).addClass("active").attr("aria-pressed","true"),a.hasClass("file-zoom-fullscreen")||e?this._maximizeZoomDialog():i.removeClass("active").attr("aria-pressed","false")))},_listen:function(){var e,t,i=this,a=i.$element,r=i.$form,n=i.$container;i._handler(a,"click",function(e){a.hasClass("file-no-browse")&&(a.data("zoneClicked")?a.data("zoneClicked",!1):e.preventDefault())}),i._handler(a,"change",L.proxy(i._change,i)),i.showBrowse&&i._handler(i.$btnFile,"click",L.proxy(i._browse,i)),e=n.find(".file-caption-name"),t=function(){return!1},i._handler(n.find(".fileinput-remove:not([disabled])"),"click",L.proxy(i.clear,i)),i._handler(n.find(".fileinput-cancel"),"click",L.proxy(i.cancel,i)),i._handler(n.find(".fileinput-pause"),"click",L.proxy(i.pause,i)),i._handler(e,"keydown",t),i._handler(e,"paste",t),i._initDragDrop(),i._handler(r,"reset",L.proxy(i.clear,i)),i.isAjaxUpload||i._handler(r,"submit",L.proxy(i._submitForm,i)),i._handler(i.$container.find(".fileinput-upload"),"click",L.proxy(i._uploadClick,i)),i._handler(L(window),"resize",function(){i._listenFullScreen(screen.width===window.innerWidth&&screen.height===window.innerHeight)}),i._handler(L(document),"webkitfullscreenchange mozfullscreenchange fullscreenchange MSFullscreenChange",function(){i._listenFullScreen(N.checkFullScreen())}),i._autoFitContent(),i._initClickable(),i._refreshPreview()},_autoFitContent:function(){var i,e=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,a=this,e=e<400?a.previewSettingsSmall||a.defaults.previewSettingsSmall:a.previewSettings||a.defaults.previewSettings;L.each(e,function(e,t){i=".file-preview-frame .file-preview-"+e,a.$preview.find(i+".kv-preview-data,"+i+" .kv-preview-data").css(t)})},_scanDroppedItems:function(t,i,a){a=a||"";function e(e){s._log(N.logMessages.badDroppedFiles),s._log(e)}var r,n,o,s=this;t.isFile?t.file(function(e){a&&(e.newPath=a+e.name),i.push(e)},e):t.isDirectory&&(n=t.createReader(),(o=function(){n.readEntries(function(e){if(e&&0<e.length){for(r=0;r<e.length;r++)s._scanDroppedItems(e[r],i,a+t.name+"/");o()}return null},e)})())},_initDragDrop:function(){var e=this,t=e.$dropZone;e.dropZoneEnabled&&e.showPreview&&(e._handler(t,"dragenter dragover",L.proxy(e._zoneDragEnter,e)),e._handler(t,"dragleave",L.proxy(e._zoneDragLeave,e)),e._handler(t,"drop",L.proxy(e._zoneDrop,e)),e._handler(L(document),"dragenter dragover drop",e._zoneDragDropInit))},_zoneDragDropInit:function(e){e.stopPropagation(),e.preventDefault()},_zoneDragEnter:function(e){var t=e.originalEvent.dataTransfer,i=-1<L.inArray("Files",t.types);if(this._zoneDragDropInit(e),this.isDisabled||!i)return t.effectAllowed="none",void(t.dropEffect="none");t.dropEffect="copy",this._raise("fileDragEnter",{sourceEvent:e,files:t.types.Files})&&N.addCss(this.$dropZone,"file-highlighted")},_zoneDragLeave:function(e){this._zoneDragDropInit(e),this.isDisabled||this._raise("fileDragLeave",{sourceEvent:e})&&this.$dropZone.removeClass("file-highlighted")},_zoneDrop:function(e){function t(){a.isAjaxUpload?a._change(e,o):(a.changeTriggered=!0,r.get(0).files=o,setTimeout(function(){a.changeTriggered=!1,r.trigger("change"+a.namespace)},a.processDelay)),a.$dropZone.removeClass("file-highlighted")}var i,a=this,r=a.$element,n=e.originalEvent.dataTransfer,o=n.files,s=n.items,n=N.getDragDropFolders(s);if(e.preventDefault(),!a.isDisabled&&!N.isEmpty(o)&&a._raise("fileDragDrop",{sourceEvent:e,files:o}))if(0<n)if(a.isAjaxUpload){for(o=[],i=0;i<s.length;i++){var l=s[i].webkitGetAsEntry();l&&a._scanDroppedItems(l,o)}setTimeout(function(){t()},500)}else a._showFolderError(n);else t()},_uploadClick:function(e){var t=this.$container.find(".fileinput-upload"),i=!t.hasClass("disabled")&&N.isEmpty(t.attr("disabled"));e&&e.isDefaultPrevented()||(this.isAjaxUpload?(e.preventDefault(),i&&this.upload()):i&&"submit"!==t.attr("type")&&((t=t.closest("form")).length&&t.trigger("submit"),e.preventDefault()))},_submitForm:function(){return this._isFileSelectionValid()&&!this._abort({})},_clearPreview:function(){var e=this;(e.showUploadedThumbs?e.getFrames(":not(.file-preview-success)"):e.getFrames()).each(function(){L(this).remove()}),e.getFrames().length&&e.showPreview||e._resetUpload(),e._validateDefaultPreview()},_initSortable:function(){var e,l=this,t=l.$preview,i="."+N.SORT_CSS,a=L("body"),r=L("html"),d=l.reversePreviewOrder,n=window.Sortable;n&&0!==t.find(i).length&&(e=a.length?a:r.length?r:l.$container,r={handle:".drag-handle-init",dataIdAttr:"data-fileid",animation:600,draggable:i,scroll:!(r=function(){e.removeClass("file-grabbing")}),forceFallback:!0,onChoose:i=function(){e.addClass("file-grabbing")},onStart:i,onUnchoose:r,onEnd:r,onSort:function(e){var t,i=e.oldIndex,a=e.newIndex,r=0,n=l.initialPreviewConfig.length,o=0<n&&n<=a,s=L(e.item);o&&(a=n-1),l.initialPreview=N.moveArray(l.initialPreview,i,a,d),l.initialPreviewConfig=N.moveArray(l.initialPreviewConfig,i,a,d),l.previewCache.init(),l.getFrames(".file-preview-initial").each(function(){L(this).attr("data-fileindex",N.INIT_FLAG+r),r++}),o&&(t=l.getFrames(":not(.file-preview-initial):first")).length&&s.slideUp(function(){s.insertBefore(t).slideDown()}),l._raise("filesorted",{previewId:s.attr("id"),oldIndex:i,newIndex:a,stack:l.initialPreviewConfig})}},L.extend(!0,r,l.fileActionSettings.dragSettings),l.sortable&&l.sortable.destroy(),l.sortable=n.create(t[0],r))},_setPreviewContent:function(e){N.setHtml(this.$preview,e),this._autoFitContent()},_initPreviewImageOrientations:function(){var r=this,n=0,o=r.canOrientImage;(r.autoOrientImageInitial||o)&&r.getFrames(".file-preview-initial").each(function(){var e,t,i=L(this),a=r.initialPreviewConfig[n];a&&a.exif&&a.exif.Orientation&&(t=i.attr("id"),e=i.find(">.kv-file-content img"),t=r._getZoom(t," >.kv-file-content img"),o?e.css("image-orientation",r.autoOrientImageInitial?"from-image":"none"):r.setImageOrientation(e,t,a.exif.Orientation,i)),n++})},_initPreview:function(e){var t,i=this,a=i.initialCaption||"";if(!i.previewCache.count(!0))return i._clearPreview(),void(e?i._setCaption(a):i._initCaption());t=i.previewCache.out(),a=e&&i.initialCaption?i.initialCaption:t.caption,i._setPreviewContent(t.content),i._setInitThumbAttr(),i._setCaption(a),i._initSortable(),N.isEmpty(t.content)||i.$container.removeClass("file-input-new"),i._initPreviewImageOrientations()},_getZoomButton:function(e){var t=this.previewZoomButtonIcons[e],i=this.previewZoomButtonClasses[e],a=' title="'+(this.previewZoomButtonTitles[e]||"")+'" '+("close"===e?' data-dismiss="modal" aria-hidden="true"':"");return"fullscreen"!==e&&"borderless"!==e&&"toggleheader"!==e||(a+=' data-toggle="button" aria-pressed="false" autocomplete="off"'),'<button type="button" class="'+i+" btn-"+e+'"'+a+">"+t+"</button>"},_getModalContent:function(){var e=this;return e._getLayoutTemplate("modal").setTokens({rtl:e.rtl?" kv-rtl":"",zoomFrameClass:e.frameClass,heading:e.msgZoomModalHeading,prev:e._getZoomButton("prev"),next:e._getZoomButton("next"),toggleheader:e._getZoomButton("toggleheader"),fullscreen:e._getZoomButton("fullscreen"),borderless:e._getZoomButton("borderless"),close:e._getZoomButton("close")})},_listenModalEvent:function(a){var r=this,n=r.$modal;n.on(a+".bs.modal",function(e){var t,i;"bs.modal"===e.namespace&&(t=n.find(".btn-fullscreen"),i=n.find(".btn-borderless"),n.data("fileinputPluginId")===r.$element.attr("id")&&r._raise("filezoom"+a,{sourceEvent:e,previewId:n.data("previewId"),modal:n}),"shown"===a&&(i.removeClass("active").attr("aria-pressed","false"),t.removeClass("active").attr("aria-pressed","false"),n.hasClass("file-zoom-fullscreen")&&(r._maximizeZoomDialog(),(N.checkFullScreen()?t:i).addClass("active").attr("aria-pressed","true"))))})},_initZoom:function(){var i=this,e=i._getLayoutTemplate("modalMain"),t="#"+N.MODAL_ID;i.showPreview&&(i.$modal=L(t),i.$modal&&i.$modal.length||(e=N.createElement(N.cspBuffer.stash(e)).insertAfter(i.$container),i.$modal=L(t).insertBefore(e),N.cspBuffer.apply(i.$modal),e.remove()),N.initModal(i.$modal),i.$modal.html(N.cspBuffer.stash(i._getModalContent())),N.cspBuffer.apply(i.$modal),L.each(N.MODAL_EVENTS,function(e,t){i._listenModalEvent(t)}))},_initZoomButtons:function(){var e,t=this.$modal.data("previewId")||"",i=this.getFrames().toArray(),a=i.length,r=this.$modal.find(".btn-prev"),n=this.$modal.find(".btn-next");if(i.length<2)return r.hide(),void n.hide();r.show(),n.show(),a&&(e=L(i[0]),a=L(i[a-1]),r.removeAttr("disabled"),n.removeAttr("disabled"),e.length&&e.attr("id")===t&&r.attr("disabled",!0),a.length&&a.attr("id")===t&&n.attr("disabled",!0))},_maximizeZoomDialog:function(){var e=this.$modal,t=e.find(".modal-header:visible"),i=e.find(".modal-footer:visible"),a=e.find(".modal-body"),r=L(window).height();e.addClass("file-zoom-fullscreen"),t&&t.length&&(r-=t.outerHeight(!0)),i&&i.length&&(r-=i.outerHeight(!0)),a&&a.length&&(r-=a.outerHeight(!0)-a.height()),e.find(".kv-zoom-body").height(r)},_resizeZoomDialog:function(e){var t=this,i=t.$modal,a=i.find(".btn-fullscreen"),r=i.find(".btn-borderless");if(i.hasClass("file-zoom-fullscreen"))N.toggleFullScreen(!1),e?a.hasClass("active")||(i.removeClass("file-zoom-fullscreen"),t._resizeZoomDialog(!0),r.hasClass("active")&&r.removeClass("active").attr("aria-pressed","false")):a.hasClass("active")?a.removeClass("active").attr("aria-pressed","false"):(i.removeClass("file-zoom-fullscreen"),t.$modal.find(".kv-zoom-body").css("height",t.zoomModalHeight));else{if(!e)return void t._maximizeZoomDialog();N.toggleFullScreen(!0)}i.focus()},_setZoomContent:function(e,t){var i,a,r,o=this,n=e.attr("id"),s=o._getZoom(n),l=o.$modal,d=l.find(".btn-fullscreen"),c=l.find(".btn-borderless"),u=l.find(".btn-toggleheader"),p=s.attr("data-template")||"generic",s=s.find(".kv-file-content"),s=s.length?s.html():"",e=(e.data("caption")||"")+" "+(e.data("size")||"");l.find(".kv-zoom-title").attr("title",L("<div/>").html(e).text()).html(e),i=l.find(".kv-zoom-body"),l.removeClass("kv-single-content"),t?(r=i.addClass("file-thumb-loading").clone().insertAfter(i),N.setHtml(i,s).hide(),r.fadeOut("fast",function(){i.fadeIn("fast",function(){i.removeClass("file-thumb-loading")}),r.remove()})):N.setHtml(i,s),(p=o.previewZoomSettings[p])&&(a=i.find(".kv-preview-data"),N.addCss(a,"file-zoom-detail"),L.each(p,function(e,t){a.css(e,t),(a.attr("width")&&"width"===e||a.attr("height")&&"height"===e)&&a.removeAttr(e)})),l.data("previewId",n),o._handler(l.find(".btn-prev"),"click",function(){o._zoomSlideShow("prev",n)}),o._handler(l.find(".btn-next"),"click",function(){o._zoomSlideShow("next",n)}),o._handler(d,"click",function(){o._resizeZoomDialog(!0)}),o._handler(c,"click",function(){o._resizeZoomDialog(!1)}),o._handler(u,"click",function(){function e(e){var t=o.$modal.find(".kv-zoom-body"),i=o.zoomModalHeight;l.hasClass("file-zoom-fullscreen")&&(i=t.outerHeight(!0),e||(i-=a.outerHeight(!0))),t.css("height",e?i+e:i)}var t,a=l.find(".modal-header"),i=l.find(".modal-body .floating-buttons"),r=a.find(".kv-zoom-actions");a.is(":visible")?(t=a.outerHeight(!0),a.slideUp("slow",function(){r.find(".btn").appendTo(i),e(t)})):(i.find(".btn").appendTo(r),a.slideDown("slow",function(){e()})),l.focus()}),o._handler(l,"keydown",function(e){var t=e.which||e.keyCode,i=L(this).find(".btn-prev"),a=L(this).find(".btn-next"),r=L(this).data("previewId"),n=o.rtl?39:37,e=o.rtl?37:39;t===n&&i.length&&!i.attr("disabled")&&o._zoomSlideShow("prev",r),t===e&&a.length&&!a.attr("disabled")&&o._zoomSlideShow("next",r)})},_showModal:function(e){var t=this,i=t.$modal;e&&e.length&&(N.initModal(i),N.setHtml(i,t._getModalContent()),t._setZoomContent(e),i.data("fileinputPluginId",t.$element.attr("id")),i.modal("show"),t._initZoomButtons())},_zoomPreview:function(e){if(!e.length)throw"Cannot zoom to detailed preview!";e=e.closest(N.FRAMES),this._showModal(e)},_zoomSlideShow:function(e,t){var i,a,r,n=this,o=n.$modal.find(".kv-zoom-actions .btn-"+e),s=n.getFrames().toArray(),l=[],d=s.length;if(!o.attr("disabled")){for(i=0;i<d;i++)(a=L(s[i]))&&a.length&&a.find(".kv-file-zoom:visible").length&&l.push(s[i]);for(d=l.length,i=0;i<d;i++)if(L(l[i]).attr("id")===t){r="prev"===e?i-1:i+1;break}r<0||d<=r||!l[r]||((o=L(l[r])).length&&n._setZoomContent(o,!0),n._initZoomButtons(),n._raise("filezoom"+e,{previewId:t,modal:n.$modal}))}},_initZoomButton:function(){var t=this;t.$preview.find(".kv-file-zoom").each(function(){var e=L(this);t._handler(e,"click",function(){t._zoomPreview(e)})})},_inputFileCount:function(){return this.$element[0].files.length},_refreshPreview:function(){var e,t=this;(t._inputFileCount()||t.isAjaxUpload)&&t.showPreview&&t.isPreviewable&&(t.isAjaxUpload&&0<t.fileManager.count()?(e=L.extend(!0,{},t.getFileList()),t.fileManager.clear(),t._clearFileInput()):e=t.$element[0].files,e&&e.length&&(t.readFiles(e),t._setFileDropZoneTitle()))},_clearObjects:function(e){e.find("video audio").each(function(){this.pause(),L(this).remove()}),e.find("img object div").each(function(){L(this).remove()})},_clearFileInput:function(){var e,t,i,a=this.$element;this._inputFileCount()&&(e=a.closest("form"),t=L(document.createElement("form")),i=L(document.createElement("div")),a.before(i),(e.length?e:i).after(t),t.append(a).trigger("reset"),i.before(a).remove(),t.remove())},_resetUpload:function(){var e=this;e.uploadStartTime=N.now(),e.uploadCache=[],e.$btnUpload.removeAttr("disabled"),e._setProgress(0),e._hideProgress(),e._resetErrors(!1),e._initAjax(),e.fileManager.clearImages(),e._resetCanvas(),e.overwriteInitial&&(e.initialPreview=[],e.initialPreviewConfig=[],e.initialPreviewThumbTags=[],e.previewCache.data={content:[],config:[],tags:[]})},_resetCanvas:function(){var e=this;e.canvas&&e.imageCanvasContext&&e.imageCanvasContext.clearRect(0,0,e.canvas.width,e.canvas.height)},_hasInitialPreview:function(){return!this.overwriteInitial&&this.previewCache.count(!0)},_resetPreview:function(){var e,t,i=this,a=i.showUploadedThumbs,r=!i.removeFromPreviewOnError,n=(a||r)&&i.isDuplicateError;i.previewCache.count(!0)?(e=i.previewCache.out(),n&&(t=N.createElement("").insertAfter(i.$container),i.getFrames().each(function(){var e=L(this);(a&&e.hasClass("file-preview-success")||r&&e.hasClass("file-preview-error"))&&t.append(e)})),i._setPreviewContent(e.content),i._setInitThumbAttr(),e=i.initialCaption||e.caption,i._setCaption(e),n&&(t.contents().appendTo(i.$preview),t.remove())):(i._clearPreview(),i._initCaption()),i.showPreview&&(i._initZoom(),i._initSortable()),i.isDuplicateError=!1},_clearDefaultPreview:function(){this.$preview.find(".file-default-preview").remove()},_validateDefaultPreview:function(){var e=this;e.showPreview&&!N.isEmpty(e.defaultPreviewContent)&&(e._setPreviewContent('<div class="file-default-preview">'+e.defaultPreviewContent+"</div>"),e.$container.removeClass("file-input-new"),e._initClickable())},_resetPreviewThumbs:function(e){var t=this;if(e)return t._clearPreview(),void t.clearFileStack();t._hasInitialPreview()?(e=t.previewCache.out(),t._setPreviewContent(e.content),t._setInitThumbAttr(),t._setCaption(e.caption),t._initPreviewActions()):t._clearPreview()},_getLayoutTemplate:function(e){e=this.layoutTemplates[e];return N.isEmpty(this.customLayoutTags)?e:N.replaceTags(e,this.customLayoutTags)},_getPreviewTemplate:function(e){var t=this.previewTemplates,t=t[e]||t.other;return N.isEmpty(this.customPreviewTags)?t:N.replaceTags(t,this.customPreviewTags)},_getOutData:function(e,t,i,a){var r=this;return t=t||{},i=i||{},{formdata:e,files:a=a||r.fileManager.list(),filenames:r.filenames,filescount:r.getFilesCount(),extra:r._getExtraData(),response:i,reader:r.reader,jqXHR:t}},_getMsgSelected:function(e){var t=1===e?this.fileSingle:this.filePlural;return 0<e?this.msgSelected.replace("{n}",e).replace("{files}",t):this.msgNoFilesSelected},_getFrame:function(e,t){var i=N.getFrameElement(this.$preview,e);return!this.showPreview||t||i.length||this._log(N.logMessages.invalidThumb,{id:e}),i},_getZoom:function(e,t){t=N.getZoomElement(this.$preview,e,t);return this.showPreview&&!t.length&&this._log(N.logMessages.invalidThumb,{id:e}),t},_getThumbs:function(e){return e=e||"",this.getFrames(":not(.file-preview-initial)"+e)},_getThumbId:function(e){return this.previewInitId+"-"+e},_getExtraData:function(e,t){var i=this.uploadExtraData;return"function"==typeof this.uploadExtraData&&(i=this.uploadExtraData(e,t)),i},_initXhr:function(e,n){var o=this,s=o.fileManager,t=function(e){var t=0,i=e.total,a=e.loaded||e.position,r=s.getUploadStats(n,a,i);e.lengthComputable&&!o.enableResumableUpload&&(t=N.round(a/i*100)),n?o._setFileUploadStats(n,t,r):o._setProgress(t,null,null,o._getStats(r)),o._raise("fileajaxprogress",[r])};return e.upload&&(o.progressDelay&&(t=N.debounce(t,o.progressDelay)),e.upload.addEventListener("progress",t,!1)),e},_initAjaxSettings:function(){this._ajaxSettings=L.extend(!0,{},this.ajaxSettings),this._ajaxDeleteSettings=L.extend(!0,{},this.ajaxDeleteSettings)},_mergeAjaxCallback:function(e,t,i){var a,r=this._ajaxSettings,n=this.mergeAjaxCallbacks;"delete"===i&&(r=this._ajaxDeleteSettings,n=this.mergeAjaxDeleteCallbacks),a=r[e],r[e]=n&&"function"==typeof a?"before"===n?function(){a.apply(this,arguments),t.apply(this,arguments)}:function(){t.apply(this,arguments),a.apply(this,arguments)}:t},_ajaxSubmit:function(e,t,i,a,r,n,o,s){var l=this;l._raise("filepreajax",[r,n,o])&&(r.append("initialPreview",JSON.stringify(l.initialPreview)),r.append("initialPreviewConfig",JSON.stringify(l.initialPreviewConfig)),r.append("initialPreviewThumbTags",JSON.stringify(l.initialPreviewThumbTags)),l._initAjaxSettings(),l._mergeAjaxCallback("beforeSend",e),l._mergeAjaxCallback("success",t),l._mergeAjaxCallback("complete",i),l._mergeAjaxCallback("error",a),"function"==typeof(s=s||l.uploadUrlThumb||l.uploadUrl)&&(s=s()),"object"==typeof(a=l._getExtraData(n,o)||{})&&L.each(a,function(e,t){r.append(e,t)}),s={xhr:function(){var e=L.ajaxSettings.xhr();return l._initXhr(e,n)},url:l._encodeURI(s),type:"POST",dataType:"json",data:r,cache:!1,processData:!1,contentType:!1},s=L.extend(!0,{},s,l._ajaxSettings),o=l.taskManager.addTask(n+"-"+o,function(){var e=this.self,t=e.ajaxQueue.shift(),t=L.ajax(t);e.ajaxRequests.push(t)}),l.ajaxQueue.push(s),o.runWithContext({self:l}))},_mergeArray:function(e,t){var i=N.cleanArray(this[e]),t=N.cleanArray(t);this[e]=i.concat(t)},_initUploadSuccess:function(e,t,i){var a,r,n,o,s,l=this;l.showPreview&&"object"==typeof e&&!L.isEmptyObject(e)&&void 0!==e.initialPreview&&0<e.initialPreview.length&&(l.hasInitData=!0,r=e.initialPreview||[],n=e.initialPreviewConfig||[],o=e.initialPreviewThumbTags||[],e=void 0===e.append||e.append,0<r.length&&!N.isArray(r)&&(r=r.split(l.initialPreviewDelimiter)),r.length&&(l._mergeArray("initialPreview",r),l._mergeArray("initialPreviewConfig",n),l._mergeArray("initialPreviewThumbTags",o)),void 0!==t?i?(s=t.attr("id"),null!==(i=l._getUploadCacheIndex(s))&&(l.uploadCache[i]={id:s,content:r[0],config:n[0]||[],tags:o[0]||[],append:e})):(s=l.previewCache.add(r[0],n[0],o[0],e),s=l.previewCache.get(s,!1),(s=(a=N.createElement(s).hide().appendTo(t)).find(".kv-zoom-cache"))&&s.length&&s.appendTo(t),t.fadeOut("slow",function(){var e=a.find(".file-preview-frame");e&&e.length&&e.insertBefore(t).fadeIn("slow").css("display:inline-block"),l._initPreviewActions(),l._clearFileInput(),t.remove(),a.remove(),l._initSortable()})):(l.previewCache.set(r,n,o,e),l._initPreview(),l._initPreviewActions())),l._resetCaption()},_getUploadCacheIndex:function(e){for(var t=this.uploadCache.length,i=0;i<t;i++)if(this.uploadCache[i].id===e)return i;return null},_initSuccessThumbs:function(){var i=this;i.showPreview&&i._getThumbs(N.FRAMES+".file-preview-success").each(function(){var t=L(this),e=t.find(".kv-file-remove");e.removeAttr("disabled"),i._handler(e,"click",function(){var e=t.attr("id"),e=i._raise("filesuccessremove",[e,t.attr("data-fileindex")]);N.cleanMemory(t),!1!==e&&t.fadeOut("slow",function(){t.remove(),i.getFrames().length||i.reset()})})})},_updateInitialPreview:function(){var i=this,e=i.uploadCache;i.showPreview&&(L.each(e,function(e,t){i.previewCache.add(t.content,t.config,t.tags,t.append)}),i.hasInitData&&(i._initPreview(),i._initPreviewActions()))},_getThumbFileId:function(e){return this.showPreview&&void 0!==e?e.attr("data-fileid"):null},_getThumbFile:function(e){e=this._getThumbFileId(e);return e?this.fileManager.getFile(e):null},_uploadSingle:function(r,n,o){var s,l,e,d,t,c,u,i,p,a,f,g=this,m=g.fileManager,h=m.count(),v=new FormData,w=g._getThumbId(n),b=0<h||!L.isEmptyObject(g.uploadExtraData),_=g.ajaxOperations.uploadThumb,C=m.getFile(n),y={id:w,index:r,fileId:n},x=g.fileManager.getFileName(n,!0);g.enableResumableUpload||(g.showPreview&&(l=m.getThumb(n),u=l.find(".file-thumb-progress"),d=l.find(".kv-file-upload"),t=l.find(".kv-file-remove"),u.show()),0===h||!b||g.showPreview&&d&&d.hasClass("disabled")||g._abort(y)||(f=function(){c?m.errors.push(n):m.removeFile(n),m.setProcessed(n),m.isProcessed()&&(g.fileBatchCompleted=!0,e())},e=function(){var i;g.fileBatchCompleted&&setTimeout(function(){var e=0===m.count(),t=m.errors.length;g._updateInitialPreview(),g.unlock(e),e&&g._clearFileInput(),i=g.$preview.find(".file-preview-initial"),g.uploadAsync&&i.length&&(N.addCss(i,N.SORT_CSS),g._initSortable()),g._raise("filebatchuploadcomplete",[m.stack,g._getExtraData()]),g.retryErrorUploads&&0!==t||m.clear(),g._setProgress(101),g.ajaxAborted=!1},g.processDelay)},i=function(e){s=g._getOutData(v,e),m.initStats(n),g.fileBatchCompleted=!1,o||(g.ajaxAborted=!1),g.showPreview&&(l.hasClass("file-preview-success")||(g._setThumbStatus(l,"Loading"),N.addCss(l,"file-uploading")),d.attr("disabled",!0),t.attr("disabled",!0)),o||g.lock(),-1!==m.errors.indexOf(n)&&delete m.errors[n],g._raise("filepreupload",[s,w,r,g._getThumbFileId(l)]),L.extend(!0,y,s),g._abort(y)&&(e.abort(),o||(g._setThumbStatus(l,"New"),l.removeClass("file-uploading"),d.removeAttr("disabled"),t.removeAttr("disabled"),g.unlock()),g._setProgressCancelled())},a=function(e,t,i){var a=g.showPreview&&l.attr("id")?l.attr("id"):w;s=g._getOutData(v,i,e),L.extend(!0,y,s),setTimeout(function(){N.isEmpty(e)||N.isEmpty(e.error)?(g.showPreview&&(g._setThumbStatus(l,"Success"),d.hide(),g._initUploadSuccess(e,l,o),g._setProgress(101,u)),g._raise("fileuploaded",[s,a,r,g._getThumbFileId(l)]),o?f():g.fileManager.remove(l)):(c=!0,p=g._parseError(_,i,g.msgUploadError,g.fileManager.getFileName(n)),g._showFileError(p,y),g._setPreviewError(l,!0),g.retryErrorUploads||d.hide(),o&&f(),g._setProgress(101,g._getFrame(a).find(".file-thumb-progress"),g.msgUploadError))},g.processDelay)},h=function(){g.showPreview&&(d.removeAttr("disabled"),t.removeAttr("disabled"),l.removeClass("file-uploading")),o?e():(g.unlock(!1),g._clearFileInput()),g._initSuccessThumbs()},b=function(t,e,i){p=g._parseError(_,t,i,g.fileManager.getFileName(n)),c=!0,setTimeout(function(){var e;o&&f(),g.fileManager.setProgress(n,100),g._setPreviewError(l,!0),g.retryErrorUploads||d.hide(),L.extend(!0,y,g._getOutData(v,t)),g._setProgress(101,g.$progress,g.msgAjaxProgressError.replace("{operation}",_)),e=g.showPreview&&l?l.find(".file-thumb-progress"):"",g._setProgress(101,e,g.msgUploadError),g._showFileError(p,y)},g.processDelay)},g._setFileData(v,C.file,x,n),g._setUploadData(v,{fileId:n}),g._ajaxSubmit(i,a,h,b,v,n,r)))},_setFileData:function(e,t,i,a){var r=this.preProcessUpload;r&&"function"==typeof r?e.append(this.uploadFileAttr,r(a,t)):e.append(this.uploadFileAttr,t,i)},_uploadBatch:function(){var e,t,i,s,l,a,d=this,r=d.fileManager,n=r.total(),o=0<n||!L.isEmptyObject(d.uploadExtraData),c=new FormData,u=d.ajaxOperations.uploadBatch;0!==n&&o&&!d._abort({})&&(l=function(){d.fileManager.clear(),d._clearFileInput()},e=function(e){d.lock(),r.initStats();var t=d._getOutData(c,e);d.ajaxAborted=!1,d.showPreview&&d._getThumbs().each(function(){var e=L(this),t=e.find(".kv-file-upload"),i=e.find(".kv-file-remove");e.hasClass("file-preview-success")||(d._setThumbStatus(e,"Loading"),N.addCss(e,"file-uploading")),t.attr("disabled",!0),i.attr("disabled",!0)}),d._raise("filebatchpreupload",[t]),d._abort(t)&&(e.abort(),d._getThumbs().each(function(){var e=L(this),t=e.find(".kv-file-upload"),i=e.find(".kv-file-remove");e.hasClass("file-preview-loading")&&(d._setThumbStatus(e,"New"),e.removeClass("file-uploading")),t.removeAttr("disabled"),i.removeAttr("disabled")}),d._setProgressCancelled())},t=function(e,t,i){var a=d._getOutData(c,i,e),r=0,n=d._getThumbs(":not(.file-preview-success)"),o=N.isEmpty(e)||N.isEmpty(e.errorkeys)?[]:e.errorkeys;N.isEmpty(e)||N.isEmpty(e.error)?(d._raise("filebatchuploadsuccess",[a]),l(),d.showPreview?(n.each(function(){var e=L(this);d._setThumbStatus(e,"Success"),e.removeClass("file-uploading"),e.find(".kv-file-upload").hide().removeAttr("disabled")}),d._initUploadSuccess(e)):d.reset(),d._setProgress(101)):(d.showPreview&&(n.each(function(){var e=L(this);e.removeClass("file-uploading"),e.find(".kv-file-upload").removeAttr("disabled"),e.find(".kv-file-remove").removeAttr("disabled"),0===o.length||-1!==L.inArray(r,o)?(d._setPreviewError(e,!0),d.retryErrorUploads||(e.find(".kv-file-upload").hide(),d.fileManager.remove(e))):(e.find(".kv-file-upload").hide(),d._setThumbStatus(e,"Success"),d.fileManager.remove(e)),e.hasClass("file-preview-error")&&!d.retryErrorUploads||r++}),d._initUploadSuccess(e)),s=d._parseError(u,i,d.msgUploadError),d._showFileError(s,a,"filebatchuploaderror"),d._setProgress(101,d.$progress,d.msgUploadError))},i=function(){d.unlock(),d._initSuccessThumbs(),d._clearFileInput(),d._raise("filebatchuploadcomplete",[d.fileManager.stack,d._getExtraData()])},o=function(e,t,i){var a=d._getOutData(c,e);s=d._parseError(u,e,i),d._showFileError(s,a,"filebatchuploaderror"),d.uploadFileCount=n-1,d.showPreview&&(d._getThumbs().each(function(){var e=L(this);e.removeClass("file-uploading"),d._getThumbFile(e)&&d._setPreviewError(e)}),d._getThumbs().removeClass("file-uploading"),d._getThumbs(" .kv-file-upload").removeAttr("disabled"),d._getThumbs(" .kv-file-delete").removeAttr("disabled"),d._setProgress(101,d.$progress,d.msgAjaxProgressError.replace("{operation}",u)))},a=0,L.each(d.fileManager.stack,function(e,t){N.isEmpty(t.file)||d._setFileData(c,t.file,t.nameFmt||"untitled_"+a,e),a++}),d._ajaxSubmit(e,t,i,o,c))},_uploadExtraOnly:function(){var e,t,i,a,r,n=this,o={},s=new FormData,l=n.ajaxOperations.uploadExtra;n._abort(o)||(e=function(e){n.lock();var t=n._getOutData(s,e);n._raise("filebatchpreupload",[t]),n._setProgress(50),o.data=t,o.xhr=e,n._abort(o)&&(e.abort(),n._setProgressCancelled())},t=function(e,t,i){var a=n._getOutData(s,i,e);N.isEmpty(e)||N.isEmpty(e.error)?(n._raise("filebatchuploadsuccess",[a]),n._clearFileInput(),n._initUploadSuccess(e),n._setProgress(101)):(r=n._parseError(l,i,n.msgUploadError),n._showFileError(r,a,"filebatchuploaderror"))},i=function(){n.unlock(),n._clearFileInput(),n._raise("filebatchuploadcomplete",[n.fileManager.stack,n._getExtraData()])},a=function(e,t,i){var a=n._getOutData(s,e);r=n._parseError(l,e,i),o.data=a,n._showFileError(r,a,"filebatchuploaderror"),n._setProgress(101,n.$progress,n.msgAjaxProgressError.replace("{operation}",l))},n._ajaxSubmit(e,t,i,a,s))},_deleteFileIndex:function(e){var t=this,i=e.attr("data-fileindex"),e=t.reversePreviewOrder;i.substring(0,5)===N.INIT_FLAG&&(i=parseInt(i.replace(N.INIT_FLAG,"")),t.initialPreview=N.spliceArray(t.initialPreview,i,e),t.initialPreviewConfig=N.spliceArray(t.initialPreviewConfig,i,e),t.initialPreviewThumbTags=N.spliceArray(t.initialPreviewThumbTags,i,e),t.getFrames().each(function(){var e=L(this),t=e.attr("data-fileindex");t.substring(0,5)===N.INIT_FLAG&&(t=parseInt(t.replace(N.INIT_FLAG,"")),i<t&&(t--,e.attr("data-fileindex",N.INIT_FLAG+t)))}))},_resetCaption:function(){var r=this;setTimeout(function(){var e,t=r.previewCache.count(!0),i=r.fileManager.count(),a=r.showPreview&&r.getFrames(":not(.file-preview-success):not(.file-preview-error)").length;0!==i||0!==t||a?(e=1<(i=t+i)?r._getMsgSelected(i):(e=r.fileManager.getFirstFile())?e.nameFmt:"_",r._setCaption(e)):r.reset()},r.processDelay)},_initFileActions:function(){var n=this;n.showPreview&&(n._initZoomButton(),n.getFrames(" .kv-file-remove").each(function(){var e,t=L(this),i=t.closest(N.FRAMES),a=i.attr("id"),r=i.attr("data-fileindex");n._handler(t,"click",function(){return!(!1===n._raise("filepreremove",[a,r])||!n._validateMinCount())&&(e=i.hasClass("file-preview-error"),N.cleanMemory(i),void i.fadeOut("slow",function(){n.fileManager.remove(i),n._clearObjects(i),i.remove(),a&&e&&n.$errorContainer.find('li[data-thumb-id="'+a+'"]').fadeOut("fast",function(){L(this).remove(),n._errorsExist()||n._resetErrors()}),n._clearFileInput(),n._resetCaption(),n._raise("fileremoved",[a,r])}))})}),n.getFrames(" .kv-file-upload").each(function(){var i=L(this);n._handler(i,"click",function(){var e=i.closest(N.FRAMES),t=n._getThumbFileId(e);n._hideProgress(),e.hasClass("file-preview-error")&&!n.retryErrorUploads||n._uploadSingle(n.fileManager.getIndex(t),t,!1)})}))},_initPreviewActions:function(){function g(){var e=m.isAjaxUpload?m.previewCache.count(!0):m._inputFileCount();m.getFrames().length||e||(m._setCaption(""),m.reset(),m.initialCaption="")}var m=this,e=m.$preview,h=m.deleteExtraData||{},t=N.FRAMES+" .kv-file-remove",i=m.fileActionSettings,v=i.removeClass,w=i.removeErrorClass;m._initZoomButton(),e.find(t).each(function(){var r,e,n,t,i,o,s,a,l,d,c=L(this),u=c.data("url")||m.deleteUrl,p=c.data("key"),f=m.ajaxOperations.deleteThumb;N.isEmpty(u)||void 0===p||("function"==typeof u&&(u=u()),n=c.closest(N.FRAMES),t=m.previewCache.data,s=n.attr("data-fileindex"),s=parseInt(s.replace(N.INIT_FLAG,"")),a=N.isEmpty(t.config)&&N.isEmpty(t.config[s])?null:t.config[s],l=N.isEmpty(a)||N.isEmpty(a.extra)?h:a.extra,d=a&&(a.filename||a.caption)||"","function"==typeof l&&(l=l()),o={id:c.attr("id"),key:p,extra:l},e=function(e){m.ajaxAborted=!1,m._raise("filepredelete",[p,e,l]),m._abort()?e.abort():(c.removeClass(w),N.addCss(n,"file-uploading"),N.addCss(c,"disabled "+v))},t=function(e,t,i){var a;if(!N.isEmpty(e)&&!N.isEmpty(e.error))return o.jqXHR=i,o.response=e,r=m._parseError(f,i,m.msgDeleteError,d),m._showFileError(r,o,"filedeleteerror"),n.removeClass("file-uploading"),c.removeClass("disabled "+v).addClass(w),void g();n.removeClass("file-uploading").addClass("file-deleted"),n.fadeOut("slow",function(){s=parseInt(n.attr("data-fileindex").replace(N.INIT_FLAG,"")),m.previewCache.unset(s),m._deleteFileIndex(n),a=m.previewCache.count(!0),a=0<a?m._getMsgSelected(a):"",m._setCaption(a),m._raise("filedeleted",[p,i,l]),m._clearObjects(n),n.remove(),g()})},a=function(e,t,i){i=m._parseError(f,e,i,d);o.jqXHR=e,o.response={},m._showFileError(i,o,"filedeleteerror"),n.removeClass("file-uploading"),c.removeClass("disabled "+v).addClass(w),g()},m._initAjaxSettings(),m._mergeAjaxCallback("beforeSend",e,"delete"),m._mergeAjaxCallback("success",t,"delete"),m._mergeAjaxCallback("error",a,"delete"),i=L.extend(!0,{},{url:m._encodeURI(u),type:"POST",dataType:"json",data:L.extend(!0,{},{key:p},l)},m._ajaxDeleteSettings),m._handler(c,"click",function(){return!!m._validateMinCount()&&(m.ajaxAborted=!1,m._raise("filebeforedelete",[p,l]),void(m.ajaxAborted instanceof Promise?m.ajaxAborted.then(function(e){e||L.ajax(i)}):m.ajaxAborted||L.ajax(i)))}))})},_hideFileIcon:function(){this.overwriteInitial&&this.$captionContainer.removeClass("icon-visible")},_showFileIcon:function(){N.addCss(this.$captionContainer,"icon-visible")},_getSize:function(e,t){var i,a=parseFloat(e),r=this.fileSizeGetter;return L.isNumeric(e)&&L.isNumeric(a)?(i="function"==typeof r?r(a):0===a?"0.00 B":(t=t||["B","KB","MB","GB","TB","PB","EB","ZB","YB"],i=Math.floor(Math.log(a)/Math.log(1024)),(a/Math.pow(1024,i)).toFixed(2)+" "+t[i]),this._getLayoutTemplate("size").replace("{sizeText}",i)):""},_getFileType:function(e){return this.mimeTypeAliases[e]||e},_generatePreviewTemplate:function(n,e,o,s,l,d,t,i,c,a,u,p,f,r){var g=this,m=g.slug(o),h="",v="",w=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,b=m,_=m,C="type-default",y=a||g._renderFileFooter(n,m,i,"auto",t),a=g.preferIconicPreview,i=g.preferIconicZoomPreview,t=a?"other":n,t=w<400?g.previewSettingsSmall[t]||g.defaults.previewSettingsSmall[t]:g.previewSettings[t]||g.defaults.previewSettings[t];return t&&L.each(t,function(e,t){v+=e+":"+t+";"}),t=function(e,t,i,a){var r=i?"zoom-"+l:l,e=g._getPreviewTemplate(e),a=(c||"")+" "+a;return g.frameClass&&(a=g.frameClass+" "+a),i&&(a=a.replace(" "+N.SORT_CSS,"")),e=g._parseFilePreviewIcon(e,o),"object"!==n||s||L.each(g.defaults.fileTypeSettings,function(e,t){"object"!==e&&"other"!==e&&t(o,s)&&(C="type-"+e)}),N.isEmpty(f)||(void 0!==f.title&&null!==f.title&&(b=f.title),void 0!==f.alt&&null!==f.alt&&(b=f.alt)),e.setTokens({previewId:r,caption:m,title:b,alt:_,frameClass:a,type:g._getFileType(s),fileindex:u,fileid:d||"",typeCss:C,footer:y,data:t,template:p||n,style:v?'style="'+v+'"':""})},u=u||l.slice(l.lastIndexOf("-")+1),g.fileActionSettings.showZoom&&(h=t(i?"other":n,r||e,!0,"kv-zoom-thumb")),h="\n"+g._getLayoutTemplate("zoomCache").replace("{zoomContent}",h),"function"==typeof g.sanitizeZoomCache&&(h=g.sanitizeZoomCache(h)),t(a?"other":n,e,!1,"kv-preview-thumb").setTokens({zoomCache:h})},_addToPreview:function(e,t){return t=N.cspBuffer.stash(t),t=this.reversePreviewOrder?e.prepend(t):e.append(t),N.cspBuffer.apply(e),t},_previewDefault:function(e,t){var i,a,r,n,o,s,l,d=this,c=d.$preview;d.showPreview&&(i=N.getFileName(e),a=e?e.type:"",r=e.size||0,n=d._getFileName(e,""),o=!0===t&&!d.isAjaxUpload,s=N.createObjectURL(e),l=d.fileManager.getId(e),e=d._getThumbId(l),d._clearDefaultPreview(),o=d._generatePreviewTemplate("other",s,i,a,e,l,o,r),d._addToPreview(c,o),d._setThumbAttr(e,n,r),!0===t&&d.isAjaxUpload&&d._setThumbStatus(d._getFrame(e),"Error"))},_previewFile:function(e,t,i,a,r){var n,o,s,l,d,c,u;this.showPreview&&(n=this,u=N.getFileName(t),o=r.type,s=r.name,l=n._parseFileType(o,u),d=n.$preview,c=t.size||0,r="image"===l?i.target.result:a,i=n.fileManager.getId(t),a=n._getThumbId(i),u=n._generatePreviewTemplate(l,r,u,o,a,i,!1,c),n._clearDefaultPreview(),n._addToPreview(d,u),u=n._getFrame(a),n._validateImageOrientation(u.find("img"),t,a,i,s,o,c,r),n._setThumbAttr(a,s,c),n._initSortable())},_setThumbAttr:function(e,t,i){e=this._getFrame(e);e.length&&(i=i&&0<i?this._getSize(i):"",e.data({caption:t,size:i}))},_setInitThumbAttr:function(){var e,t,i,a=this.previewCache.data,r=this.previewCache.count(!0);if(0!==r)for(var n=0;n<r;n++)t=a.config[n],i=this.previewInitId+"-"+N.INIT_FLAG+n,e=N.ifSet("caption",t,N.ifSet("filename",t)),t=N.ifSet("size",t),this._setThumbAttr(i,e,t)},_slugDefault:function(e){return N.isEmpty(e,!0)?"":String(e).replace(/[\[\]\/\{}:;#%=\(\)\*\+\?\\\^\$\|<>&"']/g,"_")},_updateFileDetails:function(e,t){var i=this,a=i.$element,r=N.isIE(9)&&N.findFileName(a.val())||a[0].files[0]&&a[0].files[0].name,n=!r&&0<i.fileManager.count()?i.fileManager.getFirstFile().nameFmt:r?i.slug(r):"_",a=i.isAjaxUpload?i.fileManager.count():e,r=i.previewCache.count(!0)+a,r=1===a?n:i._getMsgSelected(r);i.isError?(i.$previewContainer.removeClass("file-thumb-loading"),i.$previewStatus.html(""),i.$captionContainer.removeClass("icon-visible")):i._showFileIcon(),i._setCaption(r,i.isError),i.$container.removeClass("file-input-new file-input-ajax-new"),t||i._raise("fileselect",[e,n]),i.previewCache.count(!0)&&i._initPreviewActions()},_setThumbStatus:function(e,t){var i,a,r,n,o;this.showPreview&&(a=(i="indicator"+t)+"Title",r="file-preview-"+t.toLowerCase(),n=e.find(".file-upload-indicator"),o=this.fileActionSettings,e.removeClass("file-preview-success file-preview-error file-preview-paused file-preview-loading"),"Success"===t&&e.find(".file-drag-handle").remove(),N.setHtml(n,o[i]),n.attr("title",o[a]),e.addClass(r),"Error"!==t||this.retryErrorUploads||e.find(".kv-file-upload").attr("disabled",!0))},_setProgressCancelled:function(){this._setProgress(101,this.$progress,this.msgCancelled)},_setProgress:function(e,t,i,a){var r,n,o,s=this;(t=t||s.$progress).length&&(r=Math.min(e,100),n=s.progressUploadThreshold,o=e<=100?s.progressTemplate:s.progressCompleteTemplate,o=r<100?s.progressTemplate:i?s.paused?s.progressPauseTemplate:s.progressErrorTemplate:o,100<=e&&(a=""),N.isEmpty(o)||(a=a||"",r=(r=n&&n<r&&e<=100?o.setTokens({percent:n,status:s.msgUploadThreshold}):o.setTokens({percent:r,status:100<e?s.msgUploadEnd:r+"%"})).setTokens({stats:a}),N.setHtml(t,r),i&&N.setHtml(t.find('[role="progressbar"]'),i)))},_hasFiles:function(){var e=this.$element[0];return!!(e&&e.files&&e.files.length)},_setFileDropZoneTitle:function(){var e,t=this,i=t.$container.find(".file-drop-zone"),a=t.dropZoneTitle;t.isClickable&&(e=N.isEmpty(t.$element.attr("multiple"))?t.fileSingle:t.filePlural,a+=t.dropZoneClickTitle.replace("{files}",e)),i.find("."+t.dropZoneTitleClass).remove(),!t.showPreview||0===i.length||0<t.fileManager.count()||!t.dropZoneEnabled||0<t.previewCache.count()||!t.isAjaxUpload&&t._hasFiles()||(0===i.find(N.FRAMES).length&&N.isEmpty(t.defaultPreviewContent)&&i.prepend('<div class="'+t.dropZoneTitleClass+'">'+a+"</div>"),t.$container.removeClass("file-input-new"),N.addCss(t.$container,"file-input-ajax-new"))},_getStats:function(e){var t,i;return this.showUploadStats&&e&&e.bitrate?(i=this._getLayoutTemplate("stats"),t=e.elapsed&&e.bps?this.msgPendingTime.setTokens({time:N.getElapsed(Math.ceil(e.pendingBytes/e.bps))}):this.msgCalculatingTime,i.setTokens({uploadSpeed:e.bitrate,pendingTime:t})):""},_setResumableProgress:function(e,t,i){var a=this.resumableManager,a=i?a:this,i=i?i.find(".file-thumb-progress"):null;0===a.lastProgress&&(a.lastProgress=e),e<a.lastProgress&&(e=a.lastProgress),this._setProgress(e,i,null,this._getStats(t)),a.lastProgress=e},_toggleResumableProgress:function(e,t){var i=this.$progress;i&&i.length&&N.setHtml(i,e.setTokens({percent:101,status:t,stats:""}))},_setFileUploadStats:function(e,t,i){var a,r,n,o,s,l,d,c,u,p=this,f=p.$progress;(p.showPreview||f&&f.length)&&(a=p.fileManager,u=p.resumableManager,r=a.getThumb(e),n=0,o=a.getTotalSize(),s=L.extend(!0,{},i),p.enableResumableUpload?(l=i.loaded,d=u.getUploadedSize(),c=u.file.size,l+=d,u=a.uploadedSize+l,t=N.round(100*l/c),i.pendingBytes=c-d,p._setResumableProgress(t,i,r),d=Math.floor(100*u/o),s.pendingBytes=o-u,p._setResumableProgress(d,s)):(a.setProgress(e,t),f=r&&r.length?r.find(".file-thumb-progress"):null,p._setProgress(t,f,null,p._getStats(i)),L.each(a.stats,function(e,t){n+=t.loaded}),s.pendingBytes=o-n,d=N.round(n/o*100),p._setProgress(d,null,null,p._getStats(s))))},_validateMinCount:function(){var e=this,t=e.isAjaxUpload?e.fileManager.count():e._inputFileCount();return!(e.validateInitialCount&&0<e.minFileCount&&e._getFileCount(t-1)<e.minFileCount)||(e._noFilesError({}),!1)},_getFileCount:function(e,t){return void 0===t&&(t=this.validateInitialCount&&!this.overwriteInitial),t&&(e+=this.previewCache.count(!0)),e},_getFileId:function(e){return N.getFileId(e,this.generateFileId)},_getFileName:function(e,t){e=N.getFileName(e);return e?this.slug(e):t},_getFileNames:function(t){return this.filenames.filter(function(e){return t?void 0!==e:null!=e})},_setPreviewError:function(e,t){var i=this,a=i.removeFromPreviewOnError&&!i.retryErrorUploads;t&&!a||i.fileManager.remove(e),i.showPreview&&(a?e.remove():(i._setThumbStatus(e,"Error"),i._refreshUploadButton(e)))},_refreshUploadButton:function(e){var t=e.find(".kv-file-upload"),i=this.fileActionSettings,a=i.uploadIcon,e=i.uploadTitle;t.length&&(this.retryErrorUploads&&(a=i.uploadRetryIcon,e=i.uploadRetryTitle),t.attr("title",e),N.setHtml(t,a))},_checkDimensions:function(e,t,i,a,r,n,o){var s=this[("Small"===t?"min":"max")+"Image"+n];!N.isEmpty(s)&&i.length&&(i=i[0],i="Width"===n?i.naturalWidth||i.width:i.naturalHeight||i.height,("Small"===t?s<=i:i<=s)||(s=this["msgImage"+n+t].setTokens({name:r,size:s}),this._showFileError(s,o),this._setPreviewError(a)))},_getExifObj:function(e){var t,i=N.logMessages.exifWarning;if("data:image/jpeg;base64,"===e.slice(0,23)||"data:image/jpg;base64,"===e.slice(0,22)){try{t=window.piexif?window.piexif.load(e):null}catch(e){t=null,i=e&&e.message||""}return t||this._log(N.logMessages.badExifParser,{details:i}),t}t=null},setImageOrientation:function(o,s,l,d){var c,u,p=this,e=!o||!o.length,t=!s||!s.length,f=!1,g=e&&d&&"image"===d.attr("data-template");e&&t||(t="load.fileinputimageorient",g?(o=s,s=null,o.css(p.previewSettings.image),u=L(document.createElement("div")).appendTo(d.find(".kv-file-content")),c=L(document.createElement("span")).insertBefore(o),o.css("visibility","hidden").removeClass("file-zoom-detail").appendTo(u)):f=!o.is(":visible"),o.off(t).on(t,function(){f&&(p.$preview.removeClass("hide-content"),d.find(".kv-file-content").css("visibility","hidden"));var e=o[0],t=s&&s.length?s[0]:null,i=e.offsetHeight,a=e.offsetWidth,r=N.getRotation(l);if(f&&(d.find(".kv-file-content").css("visibility","visible"),p.$preview.addClass("hide-content")),o.data("orientation",l),t&&s.data("orientation",l),l<5)return N.setTransform(e,r),void N.setTransform(t,r);var n=Math.atan(a/i),a=Math.sqrt(Math.pow(i,2)+Math.pow(a,2)),a=a?i/Math.cos(Math.PI/2+n)/a:1,a=" scale("+Math.abs(a)+")";N.setTransform(e,r+a),N.setTransform(t,r+a),g&&(o.css("visibility","visible").insertAfter(c).addClass("file-zoom-detail"),c.remove(),u.remove())}))},_validateImageOrientation:function(e,t,i,a,r,n,o,s){var l,d=this,c=null,u=d.autoOrientImage;if(d.canOrientImage)return e.css("image-orientation",u?"from-image":"none"),void d._validateImage(i,a,r,n,o,s,c);l=N.getZoomSelector(i," img"),(u=(c=u?d._getExifObj(s):null)?c["0th"][piexif.ImageIFD.Orientation]:null)&&(d.setImageOrientation(e,L(l),u,d._getFrame(i)),d._raise("fileimageoriented",{$img:e,file:t})),d._validateImage(i,a,r,n,o,s,c)},_validateImage:function(e,t,i,a,r,n,o){var s,l=this,d=l.$preview,c=l._getFrame(e),u=c.attr("data-fileindex"),p=c.find("img");i=i||"Untitled",p.one("load",function(){s=c.width(),d.width()<s&&p.css("width","100%"),s={ind:u,id:e,fileId:t},l._checkDimensions(u,"Small",p,c,i,"Width",s),l._checkDimensions(u,"Small",p,c,i,"Height",s),l.resizeImage||(l._checkDimensions(u,"Large",p,c,i,"Width",s),l._checkDimensions(u,"Large",p,c,i,"Height",s)),l._raise("fileimageloaded",[e]),l.fileManager.addImage(t,{ind:u,img:p,thumb:c,pid:e,typ:a,siz:r,validated:!1,imgData:n,exifObj:o}),c.data("exif",o),l._validateAllImages()}).one("error",function(){l._raise("fileimageloaderror",[e])}).each(function(){this.complete?L(this).trigger("load"):this.error&&L(this).trigger("error")})},_validateAllImages:function(){var i,a=this,r={val:0},n=a.fileManager.getImageCount(),o=a.resizeIfSizeMoreThan;n===a.fileManager.totalImages&&(a._raise("fileimagesloaded"),a.resizeImage&&L.each(a.fileManager.loadedImages,function(e,t){t.validated||((i=t.siz)&&1e3*o<i&&a._getResizedImage(e,t,r,n),t.validated=!0)}))},_getResizedImage:function(e,t,i,a){var r,n,o=this,s=L(t.img)[0],l=s.naturalWidth,d=s.naturalHeight,c=1,u=o.maxImageWidth||l,p=o.maxImageHeight||d,f=!(!l||!d),g=o.imageCanvas,m=o.imageCanvasContext,h=t.typ,v=t.pid,w=t.ind,b=t.thumb,_=t.exifObj,C=function(e,t,i){o.isAjaxUpload?o._showFileError(e,t,i):o._showError(e,t,i),o._setPreviewError(b)},t=o.fileManager.getFile(e),y={id:v,index:w,fileId:e},x=[e,v,w];if(t&&f&&!(l<=u&&d<=p)||(f&&t&&o._raise("fileimageresized",x),i.val++,i.val===a&&o._raise("fileimagesresized"),f)){h=h||o.resizeDefaultImageType,t=u<l,f=p<d,c="width"===o.resizePreference?t?u/l:f?p/d:1:f?p/d:t?u/l:1,o._resetCanvas(),l*=c,d*=c,g.width=l,g.height=d;try{m.drawImage(s,0,0,l,d),r=g.toDataURL(h,o.resizeQuality),_&&(n=window.piexif.dump(_),r=window.piexif.insert(n,r)),r=N.dataURI2Blob(r),o.fileManager.setFile(e,r),o._raise("fileimageresized",x),i.val++,i.val===a&&o._raise("fileimagesresized",[void 0,void 0]),r instanceof Blob||C(o.msgImageResizeError,y,"fileimageresizeerror")}catch(e){i.val++,i.val===a&&o._raise("fileimagesresized",[void 0,void 0]),C(o.msgImageResizeException.replace("{errors}",e.message),y,"fileimageresizeexception")}}else C(o.msgImageResizeError,y,"fileimageresizeerror")},_showProgress:function(){this.$progress&&this.$progress.length&&this.$progress.show()},_hideProgress:function(){this.$progress&&this.$progress.length&&this.$progress.hide()},_initBrowse:function(e){var t=this.$element;this.showBrowse?this.$btnFile=e.find(".btn-file").append(t):(t.appendTo(e).attr("tabindex",-1),N.addCss(t,"file-no-browse"))},_initClickable:function(){var t,e,i=this;i.isClickable&&(t=i.$dropZone,i.isAjaxUpload||(e=i.$preview.find(".file-default-preview")).length&&(t=e),N.addCss(t,"clickable"),t.attr("tabindex",-1),i._handler(t,"click",function(e){e=L(e.target);i.$errorContainer.is(":visible")||e.parents(".file-preview-thumbnails").length&&!e.parents(".file-default-preview").length||(i.$element.data("zoneClicked",!0).trigger("click"),t.blur())}))},_initCaption:function(){var e=this.initialCaption||"";return this.overwriteInitial||N.isEmpty(e)?(this.$caption.val(""),!1):(this._setCaption(e),!0)},_setCaption:function(e,t){var i,a,r,n=this;if(n.$caption.length){if(n.$captionContainer.removeClass("icon-visible"),t)i=L("<div>"+n.msgValidationError+"</div>").text(),r=(a=n.fileManager.count())?(r=n.fileManager.getFirstFile(),1===a&&r?r.nameFmt:n._getMsgSelected(a)):n._getMsgSelected(n.msgNo),a=N.isEmpty(e)?r:e,r='<span class="'+n.msgValidationErrorClass+'">'+n.msgValidationErrorIcon+"</span>";else{if(N.isEmpty(e))return;a=i=L("<div>"+e+"</div>").text(),r=n._getLayoutTemplate("fileIcon")}n.$captionContainer.addClass("icon-visible"),n.$caption.attr("title",i).val(a),N.setHtml(n.$captionIcon,r)}},_createContainer:function(){var e=this,t={class:"file-input file-input-new"+(e.rtl?" kv-rtl":"")},i=N.createElement(N.cspBuffer.stash(e._renderMain()));return N.cspBuffer.apply(i),i.insertBefore(e.$element).attr(t),e._initBrowse(i),e.theme&&i.addClass("theme-"+e.theme),i},_refreshContainer:function(){var e=this,t=e.$container;e.$element.insertAfter(t),N.setHtml(t,e._renderMain()),e._initBrowse(t),e._validateDisabled()},_validateDisabled:function(){this.$caption.attr({readonly:this.isDisabled})},_renderMain:function(){var e=this,t=e.dropZoneEnabled?" file-drop-zone":"file-drop-disabled",i=e.showClose?e._getLayoutTemplate("close"):"",a=e.showPreview?e._getLayoutTemplate("preview").setTokens({class:e.previewClass,dropClass:t}):"",t=e.isDisabled?e.captionClass+" file-caption-disabled":e.captionClass,t=e.captionTemplate.setTokens({class:t+" kv-fileinput-caption"});return e.mainTemplate.setTokens({class:e.mainClass+(!e.showBrowse&&e.showCaption?" no-browse":""),preview:a,close:i,caption:t,upload:e._renderButton("upload"),remove:e._renderButton("remove"),cancel:e._renderButton("cancel"),pause:e._renderButton("pause"),browse:e._renderButton("browse")})},_renderButton:function(e){var t=this,i=t._getLayoutTemplate("btnDefault"),a=t[e+"Class"],r=t[e+"Title"],n=t[e+"Icon"],o=t[e+"Label"],s=t.isDisabled?" disabled":"",l="button";switch(e){case"remove":if(!t.showRemove)return"";break;case"cancel":if(!t.showCancel)return"";a+=" kv-hidden";break;case"pause":if(!t.showPause)return"";a+=" kv-hidden";break;case"upload":if(!t.showUpload)return"";t.isAjaxUpload&&!t.isDisabled?i=t._getLayoutTemplate("btnLink").replace("{href}",t.uploadUrl):l="submit";break;case"browse":if(!t.showBrowse)return"";i=t._getLayoutTemplate("btnBrowse");break;default:return""}return a+="browse"===e?" btn-file":" fileinput-"+e+" fileinput-"+e+"-button",N.isEmpty(o)||(o=' <span class="'+t.buttonLabelClass+'">'+o+"</span>"),i.setTokens({type:l,css:a,title:r,status:s,icon:n,label:o})},_renderThumbProgress:function(){return'<div class="file-thumb-progress kv-hidden">'+this.progressInfoTemplate.setTokens({percent:101,status:this.msgUploadBegin,stats:""})+"</div>"},_renderFileFooter:function(e,t,i,a,r){var n=this,o=n.fileActionSettings,s=o.showRemove,l=o.showDrag,d=o.showUpload,c=o.showZoom,u=n._getLayoutTemplate("footer"),p=n._getLayoutTemplate("indicator"),f=r?o.indicatorError:o.indicatorNew,o=r?o.indicatorErrorTitle:o.indicatorNewTitle,o=p.setTokens({indicator:f,indicatorTitle:o}),o={type:e,caption:t,size:i=n._getSize(i),width:a,progress:"",indicator:o};return n.isAjaxUpload?(o.progress=n._renderThumbProgress(),o.actions=n._renderFileActions(o,d,!1,s,c,l,!1,!1,!1)):o.actions=n._renderFileActions(o,!1,!1,!1,c,l,!1,!1,!1),o=u.setTokens(o),o=N.replaceTags(o,n.previewThumbTags)},_renderFileActions:function(e,t,i,a,r,n,o,s,l,d,c,u){var p=this;if(!e.type&&d&&(e.type="image"),p.enableResumableUpload?t=!1:"function"==typeof t&&(t=t(e)),"function"==typeof i&&(i=i(e)),"function"==typeof a&&(a=a(e)),"function"==typeof r&&(r=r(e)),"function"==typeof n&&(n=n(e)),!(t||i||a||r||n))return"";var f=!1===s?"":' data-url="'+s+'"',g="",m="",h=!1===l?"":' data-key="'+l+'"',v="",w="",b="",_=p._getLayoutTemplate("actions"),e=p.fileActionSettings,s=p.otherActionButtons.setTokens({dataKey:h,key:l}),o=o?e.removeClass+" disabled":e.removeClass;return a&&(v=p._getLayoutTemplate("actionDelete").setTokens({removeClass:o,removeIcon:e.removeIcon,removeTitle:e.removeTitle,dataUrl:f,dataKey:h,key:l})),t&&(w=p._getLayoutTemplate("actionUpload").setTokens({uploadClass:e.uploadClass,uploadIcon:e.uploadIcon,uploadTitle:e.uploadTitle})),i&&(b=(b=p._getLayoutTemplate("actionDownload").setTokens({downloadClass:e.downloadClass,downloadIcon:e.downloadIcon,downloadTitle:e.downloadTitle,downloadUrl:c||p.initialPreviewDownloadUrl})).setTokens({filename:u,key:l})),r&&(g=p._getLayoutTemplate("actionZoom").setTokens({zoomClass:e.zoomClass,zoomIcon:e.zoomIcon,zoomTitle:e.zoomTitle})),n&&d&&(d="drag-handle-init "+e.dragClass,m=p._getLayoutTemplate("actionDrag").setTokens({dragClass:d,dragTitle:e.dragTitle,dragIcon:e.dragIcon})),_.setTokens({delete:v,upload:w,download:b,zoom:g,drag:m,other:s})},_browse:function(e){var t=this;e&&e.isDefaultPrevented()||!t._raise("filebrowse")||(t.isError&&!t.isAjaxUpload&&t.clear(),t.focusCaptionOnBrowse&&t.$captionContainer.focus())},_change:function(e){var n=this;if(!n.changeTriggered){var t=n.$element,i=1<arguments.length,o=n.isAjaxUpload,s=i?arguments[1]:t[0].files,a=n.fileManager.count(),r=N.isEmpty(t.attr("multiple")),l=!o&&r?1:n.maxFileCount,d=n.maxTotalFileCount,c=0<d&&l<d,u=r&&0<a,p=function(e,t,i){var a,r=(r=i?n.msgTotalFilesTooMany:n.msgFilesTooMany).replace("{m}",t).replace("{n}",e);n.isError=(a=r,e=t=i=null,r=L.extend(!0,{},n._getOutData(null,{},{},s),{id:t,index:e}),i={id:t,index:e,file:i,files:s},n.isPersistentError=!0,o?n._showFileError(a,r):n._showError(a,i)),n.$captionContainer.removeClass("icon-visible"),n._setCaption("",!0),n.$container.removeClass("file-input-new file-input-ajax-new")};if(n.reader=null,n._resetUpload(),n._hideFileIcon(),n.dropZoneEnabled&&n.$container.find(".file-drop-zone ."+n.dropZoneTitleClass).remove(),o||(s=e.target&&void 0===e.target.files?e.target.value?[{name:e.target.value.replace(/^.+\\/,"")}]:[]:e.target.files||{}),i=s,N.isEmpty(i)||0===i.length)return o||n.clear(),void n._raise("fileselectnone");if(n._resetErrors(),t=i.length,r=o?n.fileManager.count()+t:t,e=n._getFileCount(r,!c&&void 0),0<l&&l<e){if(!n.autoReplace||l<t)return void p(n.autoReplace&&l<t?t:e,l);l<e&&n._resetPreviewThumbs(o)}else{if(c&&(e=n._getFileCount(r,!0),0<d&&d<e)){if(!n.autoReplace||l<t)return void p(n.autoReplace&&d<t?t:e,d,!0);l<e&&n._resetPreviewThumbs(o)}!o||u?(n._resetPreviewThumbs(!1),u&&n.clearFileStack()):!o||0!==a||n.previewCache.count(!0)&&!n.overwriteInitial||n._resetPreviewThumbs(!0)}n.readFiles(i)}},_abort:function(e){var t=this;return t.ajaxAborted&&"object"==typeof t.ajaxAborted&&void 0!==t.ajaxAborted.message?((e=L.extend(!0,{},t._getOutData(null),e)).abortData=t.ajaxAborted.data||{},e.abortMessage=t.ajaxAborted.message,t._setProgress(101,t.$progress,t.msgCancelled),t._showFileError(t.ajaxAborted.message,e,"filecustomerror"),t.cancel(),!0):!!t.ajaxAborted},_resetFileStack:function(){var a=this,r=0;a._getThumbs().each(function(){var e=L(this),t=e.attr("data-fileindex"),i=e.attr("id");"-1"!==t&&-1!==t&&(a._getThumbFile(e)?e.attr({"data-fileindex":"-1"}):(e.attr({"data-fileindex":r}),r++),a._getZoom(i).attr({"data-fileindex":e.attr("data-fileindex")}))})},_isFileSelectionValid:function(e){var t=this;return e=e||0,t.required&&!t.getFilesCount()?(t.$errorContainer.html(""),t._showFileError(t.msgFileRequired),!1):!(0<t.minFileCount&&t._getFileCount(e)<t.minFileCount)||(t._noFilesError({}),!1)},_canPreview:function(e){var t=this;if(!(e&&t.showPreview&&t.$preview&&t.$preview.length))return!1;var i=e.name||"",a=e.type||"",r=(e.size||0)/1e3,n=t._parseFileType(a,i),o=t.allowedPreviewTypes,s=t.allowedPreviewMimeTypes,l=t.allowedPreviewExtensions||[],d=t.disabledPreviewTypes,c=t.disabledPreviewMimeTypes,u=t.disabledPreviewExtensions||[],p=t.maxFilePreviewSize&&parseFloat(t.maxFilePreviewSize)||0,e=new RegExp("\\.("+l.join("|")+")$","i"),t=new RegExp("\\.("+u.join("|")+")$","i"),o=!o||-1!==o.indexOf(n),s=!s||-1!==s.indexOf(a),e=!l.length||N.compare(i,e);return!(d&&-1!==d.indexOf(n)||c&&-1!==c.indexOf(a)||u.length&&N.compare(i,t)||p&&!isNaN(p)&&p<r)&&(o||s||e)},addToStack:function(e,t){this.fileManager.add(e,t)},clearFileStack:function(){var e=this;return e.fileManager.clear(),e._initResumableUpload(),e.enableResumableUpload?(null===e.showPause&&(e.showPause=!0),null===e.showCancel&&(e.showCancel=!1)):(e.showPause=!1,null===e.showCancel&&(e.showCancel=!0)),e.$element},getFileStack:function(){return this.fileManager.stack},getFileList:function(){return this.fileManager.list()},getFilesSize:function(){return this.fileManager.getTotalSize()},getFilesCount:function(e){var t=this,i=t.isAjaxUpload?t.fileManager.count():t._inputFileCount();return e&&(i+=t.previewCache.count(!0)),t._getFileCount(i)},readFiles:function(P){this.reader=new FileReader;function F(e,t,i,a,r){var n=L.extend(!0,{},E._getOutData(null,{},{},P),{id:i,index:a,fileId:r}),r={id:i,index:a,fileId:r,file:t,files:P};E._previewDefault(t,!0),i=E._getFrame(i,!0),E.isAjaxUpload?setTimeout(function(){k(a+1)},E.processDelay):(E.unlock(),U=0),E.removeFromPreviewOnError&&i.length?i.remove():(E._initFileActions(),i.find(".kv-file-upload").remove()),E.isPersistentError=!0,E.isError=E.isAjaxUpload?E._showFileError(e,n):E._showError(e,r),E._updateFileDetails(U)}var k,E=this,S=E.reader,I=E.$previewContainer,A=E.$previewStatus,D=E.msgLoading,z=E.msgProgress,j=E.previewInitId,U=P.length,M=E.fileTypeSettings,$=E.allowedFileTypes,R=$?$.length:0,O=E.allowedFileExtensions,B=N.isEmpty(O)?"":O.join(", ");E.fileManager.clearImages(),L.each(P,function(e,t){var i=E.fileTypeSettings.image;i&&i(t.type)&&E.fileManager.totalImages++}),(k=function(d){var e=E.$errorContainer,t=E.fileManager;if(U<=d)return E.unlock(),E.duplicateErrors.length&&(x="<li>"+E.duplicateErrors.join("</li><li>")+"</li>",0===e.find("ul").length?N.setHtml(e,E.errorCloseButton+"<ul>"+x+"</ul>"):e.find("ul").append(x),e.fadeIn(E.fadeDelay),E._handler(e.find(".kv-error-close"),"click",function(){e.fadeOut(E.fadeDelay)}),E.duplicateErrors=[]),E.isAjaxUpload?(E._raise("filebatchselected",[t.stack]),0!==t.count()||E.isError||E.reset()):E._raise("filebatchselected",[P]),I.removeClass("file-thumb-loading"),void A.html("");E.lock(!0);function c(){var e=z.setTokens({index:d+1,files:U,percent:50,name:m});setTimeout(function(){A.html(e),E._updateFileDetails(U),k(d+1)},E.processDelay),E._raise("fileloaded",[f,l,s,d,S])&&E.isAjaxUpload&&t.add(f)}var i,u,a,r,n,o,p,f=P[d],s=E._getFileId(f),l=j+"-"+s,g=M.image,m=E._getFileName(f,""),h=(f&&f.size||0)/1e3,v="",w=N.createObjectURL(f),b=0,_="",C=!1,y=0;if(f){if(r=t.getId(f),0<R)for(u=0;u<R;u++)a=$[u],a=E.msgFileTypes[a]||a,_+=0===u?a:", "+a;if(!1!==m){if(0===m.length)return T=E.msgInvalidFileName.replace("{name}",N.htmlEncode(N.getFileName(f),"[unknown]")),void F(T,f,l,d,r);if(N.isEmpty(O)||(v=new RegExp("\\.("+O.join("|")+")$","i")),i=h.toFixed(2),E.isAjaxUpload&&t.exists(r)||E._getFrame(l,!0).length){var x={id:l,index:d,fileId:r,file:f,files:P},T=E.msgDuplicateFile.setTokens({name:m,size:i});return E.isAjaxUpload?(E.duplicateErrors.push(T),E.isDuplicateError=!0,E._raise("fileduplicateerror",[f,r,m,i,l,d]),k(d+1)):(E._showError(T,x),E.unlock(),U=0,E._clearFileInput(),E.reset()),void E._updateFileDetails(U)}if(0<E.maxFileSize&&h>E.maxFileSize)return T=E.msgSizeTooLarge.setTokens({name:m,size:i,maxSize:E.maxFileSize}),void F(T,f,l,d,r);if(null!==E.minFileSize&&h<=N.getNum(E.minFileSize))return T=E.msgSizeTooSmall.setTokens({name:m,size:i,minSize:E.minFileSize}),void F(T,f,l,d,r);if(!N.isEmpty($)&&N.isArray($)){for(u=0;u<$.length;u+=1)o=$[u],b+=(o=M[o])&&"function"==typeof o&&o(f.type,N.getFileName(f))?1:0;if(0===b)return T=E.msgInvalidFileType.setTokens({name:m,types:_}),void F(T,f,l,d,r)}return 0!==b||N.isEmpty(O)||!N.isArray(O)||N.isEmpty(v)||(v=N.compare(m,v),0!==(b+=N.isEmpty(v)?0:v.length))?E._canPreview(f)?(p=g(f.type,m),A.html(D.replace("{index}",d+1).replace("{files}",U)),I.addClass("file-thumb-loading"),S.onerror=function(e){E._errorHandler(e,m)},S.onload=function(e){var t,i,a,r,n,o,s=[],l={name:m,type:f.type};if(L.each(M,function(e,t){"object"!==e&&"other"!==e&&"function"==typeof t&&t(f.type,m)&&y++}),0===y){for(i=new Uint8Array(e.target.result),u=0;u<i.length;u++)a=i[u].toString(16),s.push(a);if(t=s.join("").toLowerCase().substring(0,8),n=N.getMimeType(t,"",""),N.isEmpty(n)&&(r=N.arrayBuffer2String(S.result),n=N.isSvg(r)?"image/svg+xml":N.getMimeType(t,r,f.type)),l={name:m,type:n},p=g(n,""))return(o=new FileReader).onerror=function(e){E._errorHandler(e,m)},o.onload=function(e){if(E.isAjaxUpload&&!E._raise("filebeforeload",[f,d,S]))return C=!0,E._resetCaption(),S.abort(),A.html(""),I.removeClass("file-thumb-loading"),void E.enable();E._previewFile(d,f,e,w,l),E._initFileActions(),c()},void o.readAsDataURL(f)}if(E.isAjaxUpload&&!E._raise("filebeforeload",[f,d,S]))return C=!0,E._resetCaption(),S.abort(),A.html(""),I.removeClass("file-thumb-loading"),void E.enable();E._previewFile(d,f,e,w,l),E._initFileActions(),c()},S.onprogress=function(e){e.lengthComputable&&(e=e.loaded/e.total*100,e=Math.ceil(e),T=z.setTokens({index:d+1,files:U,percent:e,name:m}),setTimeout(function(){C||A.html(T)},E.processDelay))},void(p?S.readAsDataURL(f):S.readAsArrayBuffer(f))):(n=E.isAjaxUpload&&E._raise("filebeforeload",[f,d,S]),E.isAjaxUpload&&n&&t.add(f),E.showPreview&&n&&(I.addClass("file-thumb-loading"),E._previewDefault(f),E._initFileActions()),void setTimeout(function(){n&&E._updateFileDetails(U),k(d+1),E._raise("fileloaded",[f,l,s,d])},10)):(T=E.msgInvalidFileExtension.setTokens({name:m,extensions:B}),void F(T,f,l,d,r))}k(d+1)}})(0),E._updateFileDetails(U,!0)},lock:function(e){var t=this,i=t.$container;return t._resetErrors(),t.disable(),!e&&t.showCancel&&i.find(".fileinput-cancel").show(),!e&&t.showPause&&i.find(".fileinput-pause").show(),t._raise("filelock",[t.fileManager.stack,t._getExtraData()]),t.$element},unlock:function(e){var t=this,i=t.$container;return void 0===e&&(e=!0),t.enable(),i.removeClass("is-locked"),t.showCancel&&i.find(".fileinput-cancel").hide(),t.showPause&&i.find(".fileinput-pause").hide(),e&&t._resetFileStack(),t._raise("fileunlock",[t.fileManager.stack,t._getExtraData()]),t.$element},resume:function(){var e=this,t=e.fileManager,i=!1,a=e.resumableManager;return t.bpsLog=[],t.bps=0,e.enableResumableUpload&&(e.paused?e._toggleResumableProgress(e.progressPauseTemplate,e.msgUploadResume):i=!0,e.paused=!1,i&&e._toggleResumableProgress(e.progressInfoTemplate,e.msgUploadBegin),setTimeout(function(){a.upload()},e.processDelay)),e.$element},pause:function(){var e,a=this,t=a.resumableManager,i=a.ajaxRequests,r=i.length,n=t.getProgress(),o=a.fileActionSettings,s=a.taskManager.getPool(t.id);if(!a.enableResumableUpload)return a.$element;if(s&&s.cancel(),a._raise("fileuploadpaused",[a.fileManager,t]),0<r)for(e=0;e<r;e+=1)a.paused=!0,i[e].abort();return a.showPreview&&a._getThumbs().each(function(){var e=L(this),t=a._getLayoutTemplate("stats"),i=e.find(".file-upload-indicator");e.removeClass("file-uploading"),i.attr("title")===o.indicatorLoadingTitle&&(a._setThumbStatus(e,"Paused"),t=t.setTokens({pendingTime:a.msgPaused,uploadSpeed:""}),a.paused=!0,a._setProgress(n,e.find(".file-thumb-progress"),n+"%",t)),a._getThumbFile(e)||e.find(".kv-file-remove").removeClass("disabled").removeAttr("disabled")}),a._setProgress(101,a.$progress,a.msgPaused),a.$element},cancel:function(){var e,i=this,t=i.ajaxRequests,a=i.resumableManager,r=i.taskManager,r=a?r.getPool(a.id):void 0,n=t.length;if(i.enableResumableUpload&&r?(r.cancel().done(function(){i._setProgressCancelled()}),a.reset(),i._raise("fileuploadcancelled",[i.fileManager,a])):i._raise("fileuploadcancelled",[i.fileManager]),i._initAjax(),0<n)for(e=0;e<n;e+=1)i.cancelling=!0,t[e].abort();return i._getThumbs().each(function(){var e=L(this),t=e.find(".file-thumb-progress");e.removeClass("file-uploading"),i._setProgress(0,t),t.hide(),i._getThumbFile(e)||(e.find(".kv-file-upload").removeClass("disabled").removeAttr("disabled"),e.find(".kv-file-remove").removeClass("disabled").removeAttr("disabled")),i.unlock()}),setTimeout(function(){i._setProgressCancelled()},i.processDelay),i.$element},clear:function(){var e,t=this;if(t._raise("fileclear"))return t.$btnUpload.removeAttr("disabled"),t._getThumbs().find("video,audio,img").each(function(){N.cleanMemory(L(this))}),t._clearFileInput(),t._resetUpload(),t.clearFileStack(),t.isDuplicateError=!1,t.isPersistentError=!1,t._resetErrors(!0),t._hasInitialPreview()?(t._showFileIcon(),t._resetPreview(),t._initPreviewActions(),t.$container.removeClass("file-input-new")):(t._getThumbs().each(function(){t._clearObjects(L(this))}),t.isAjaxUpload&&(t.previewCache.data={}),t.$preview.html(""),e=!t.overwriteInitial&&0<t.initialCaption.length?t.initialCaption:"",t.$caption.attr("title","").val(e),N.addCss(t.$container,"file-input-new"),t._validateDefaultPreview()),0===t.$container.find(N.FRAMES).length&&(t._initCaption()||t.$captionContainer.removeClass("icon-visible")),t._hideFileIcon(),t.focusCaptionOnClear&&t.$captionContainer.focus(),t._setFileDropZoneTitle(),t._raise("filecleared"),t.$element},reset:function(){var e=this;if(e._raise("filereset"))return e.lastProgress=0,e._resetPreview(),e.$container.find(".fileinput-filename").text(""),N.addCss(e.$container,"file-input-new"),e.getFrames().length&&e.$container.removeClass("file-input-new"),e.clearFileStack(),e._setFileDropZoneTitle(),e.$element},disable:function(){var e=this,t=e.$container;return e.isDisabled=!0,e._raise("filedisabled"),e.$element.attr("disabled","disabled"),t.addClass("is-locked"),N.addCss(t.find(".btn-file"),"disabled"),t.find(".kv-fileinput-caption").addClass("file-caption-disabled"),t.find(".fileinput-remove, .fileinput-upload, .file-preview-frame button").attr("disabled",!0),e._initDragDrop(),e.$element},enable:function(){var e=this,t=e.$container;return e.isDisabled=!1,e._raise("fileenabled"),e.$element.removeAttr("disabled"),t.removeClass("is-locked"),t.find(".kv-fileinput-caption").removeClass("file-caption-disabled"),t.find(".fileinput-remove, .fileinput-upload, .file-preview-frame button").removeAttr("disabled"),t.find(".btn-file").removeClass("disabled"),e._initDragDrop(),e.$element},upload:function(){var t,i=this,e=i.fileManager,a=e.count(),r=!L.isEmptyObject(i._getExtraData());if(e.bpsLog=[],e.bps=0,i.isAjaxUpload&&!i.isDisabled&&i._isFileSelectionValid(a)){if(i.lastProgress=0,i._resetUpload(),0!==a||r)return i.cancelling=!1,i._showProgress(),i.lock(),0===a&&r?(i._setProgress(2),void i._uploadExtraOnly()):i.enableResumableUpload?i.resume():((i.uploadAsync||i.enableResumableUpload)&&(r=i._getOutData(null),i._raise("filebatchpreupload",[r]),i.fileBatchCompleted=!1,i.uploadCache=[],L.each(i.getFileStack(),function(e){e=i._getThumbId(e);i.uploadCache.push({id:e,content:null,config:null,tags:null,append:!0})}),i.$preview.find(".file-preview-initial").removeClass(N.SORT_CSS),i._initSortable()),i._setProgress(2),i.hasInitData=!1,i.uploadAsync?(t=0,void L.each(i.getFileStack(),function(e){i._uploadSingle(t,e,!0),t++})):(i._uploadBatch(),i.$element));i._showFileError(i.msgUploadEmpty)}},destroy:function(){var e=this,t=e.$form,i=e.$container,a=e.$element,r=e.namespace;return L(document).off(r),L(window).off(r),t&&t.length&&t.off(r),e.isAjaxUpload&&e._clearFileInput(),e._cleanup(),e._initPreviewCache(),a.insertBefore(i).off(r).removeData(),i.off().remove(),a},refresh:function(e){var t=this,i=t.$element;return e="object"!=typeof e||N.isEmpty(e)?t.options:L.extend(!0,{},t.options,e),t._init(e,!0),t._listen(),i},zoom:function(e){e=this._getFrame(e);this._showModal(e)},getExif:function(e){e=this._getFrame(e);return e&&e.data("exif")||null},getFrames:function(e){return e=e||"",e=this.$preview.find(N.FRAMES+e),this.reversePreviewOrder&&(e=L(e.get().reverse())),e},getPreview:function(){return{content:this.initialPreview,config:this.initialPreviewConfig,tags:this.initialPreviewThumbTags}}},L.fn.fileinput=function(s){if(N.hasFileAPISupport()||N.isIE(9)){var l=Array.apply(null,arguments),d=[];switch(l.shift(),this.each(function(){var e=L(this),t=e.data("fileinput"),i="object"==typeof s&&s,a=i.theme||e.data("theme"),r={},n={},o=i.language||e.data("language")||L.fn.fileinput.defaults.language||"en";t||(a&&(n=L.fn.fileinputThemes[a]||{}),"en"===o||N.isEmpty(L.fn.fileinputLocales[o])||(r=L.fn.fileinputLocales[o]||{}),i=L.extend(!0,{},L.fn.fileinput.defaults,n,L.fn.fileinputLocales.en,r,i,e.data()),t=new c(this,i),e.data("fileinput",t)),"string"==typeof s&&d.push(t[s].apply(t,l))}),d.length){case 0:return this;case 1:return d[0];default:return d}}};L.fn.fileinput.defaults={language:"en",showCaption:!0,showBrowse:!0,showPreview:!0,showRemove:!0,showUpload:!0,showUploadStats:!0,showCancel:null,showPause:null,showClose:!0,showUploadedThumbs:!0,showConsoleLogs:!1,browseOnZoneClick:!1,autoReplace:!1,autoOrientImage:function(){var e=window.navigator.userAgent,t=!!e.match(/WebKit/i);return!(!!e.match(/iP(od|ad|hone)/i)&&t&&!e.match(/CriOS/i))},autoOrientImageInitial:!0,required:!1,rtl:!1,hideThumbnailContent:!1,encodeUrl:!0,focusCaptionOnBrowse:!0,focusCaptionOnClear:!0,generateFileId:null,previewClass:"",captionClass:"",frameClass:"krajee-default",mainClass:"file-caption-main",mainTemplate:null,fileSizeGetter:null,initialCaption:"",initialPreview:[],initialPreviewDelimiter:"*$$*",initialPreviewAsData:!1,initialPreviewFileType:"image",initialPreviewConfig:[],initialPreviewThumbTags:[],previewThumbTags:{},initialPreviewShowDelete:!0,initialPreviewDownloadUrl:"",removeFromPreviewOnError:!1,deleteUrl:"",deleteExtraData:{},overwriteInitial:!0,sanitizeZoomCache:function(e){e=N.createElement(e);return e.find("input,textarea,select,datalist,form,.file-thumbnail-footer").remove(),e.html()},previewZoomButtonIcons:{prev:'<i class="glyphicon glyphicon-triangle-left"></i>',next:'<i class="glyphicon glyphicon-triangle-right"></i>',toggleheader:'<i class="glyphicon glyphicon-resize-vertical"></i>',fullscreen:'<i class="glyphicon glyphicon-fullscreen"></i>',borderless:'<i class="glyphicon glyphicon-resize-full"></i>',close:'<i class="glyphicon glyphicon-remove"></i>'},previewZoomButtonClasses:{prev:"btn btn-navigate",next:"btn btn-navigate",toggleheader:"btn btn-sm btn-kv btn-default btn-outline-secondary",fullscreen:"btn btn-sm btn-kv btn-default btn-outline-secondary",borderless:"btn btn-sm btn-kv btn-default btn-outline-secondary",close:"btn btn-sm btn-kv btn-default btn-outline-secondary"},previewTemplates:{},previewContentTemplates:{},preferIconicPreview:!1,preferIconicZoomPreview:!1,allowedFileTypes:null,allowedFileExtensions:null,allowedPreviewTypes:void 0,allowedPreviewMimeTypes:null,allowedPreviewExtensions:null,disabledPreviewTypes:void 0,disabledPreviewExtensions:["msi","exe","com","zip","rar","app","vb","scr"],disabledPreviewMimeTypes:null,defaultPreviewContent:null,customLayoutTags:{},customPreviewTags:{},previewFileIcon:'<i class="glyphicon glyphicon-file"></i>',previewFileIconClass:"file-other-icon",previewFileIconSettings:{},previewFileExtSettings:{},buttonLabelClass:"hidden-xs",browseIcon:'<i class="glyphicon glyphicon-folder-open"></i>&nbsp;',browseClass:"btn btn-primary",removeIcon:'<i class="glyphicon glyphicon-trash"></i>',removeClass:"btn btn-default btn-secondary",cancelIcon:'<i class="glyphicon glyphicon-ban-circle"></i>',cancelClass:"btn btn-default btn-secondary",pauseIcon:'<i class="glyphicon glyphicon-pause"></i>',pauseClass:"btn btn-default btn-secondary",uploadIcon:'<i class="glyphicon glyphicon-upload"></i>',uploadClass:"btn btn-default btn-secondary",uploadUrl:null,uploadUrlThumb:null,uploadAsync:!0,uploadParamNames:{chunkCount:"chunkCount",chunkIndex:"chunkIndex",chunkSize:"chunkSize",chunkSizeStart:"chunkSizeStart",chunksUploaded:"chunksUploaded",fileBlob:"fileBlob",fileId:"fileId",fileName:"fileName",fileRelativePath:"fileRelativePath",fileSize:"fileSize",retryCount:"retryCount"},maxAjaxThreads:5,fadeDelay:800,processDelay:100,bitrateUpdateDelay:500,queueDelay:10,progressDelay:0,enableResumableUpload:!1,resumableUploadOptions:{fallback:null,testUrl:null,chunkSize:2048,maxThreads:4,maxRetries:3,showErrorLog:!0,retainErrorHistory:!0,skipErrorsAndProceed:!1},uploadExtraData:{},zoomModalHeight:480,minImageWidth:null,minImageHeight:null,maxImageWidth:null,maxImageHeight:null,resizeImage:!1,resizePreference:"width",resizeQuality:.92,resizeDefaultImageType:"image/jpeg",resizeIfSizeMoreThan:0,minFileSize:-1,maxFileSize:0,maxFilePreviewSize:25600,minFileCount:0,maxFileCount:0,maxTotalFileCount:0,validateInitialCount:!1,msgValidationErrorClass:"text-danger",msgValidationErrorIcon:'<i class="glyphicon glyphicon-exclamation-sign"></i> ',msgErrorClass:"file-error-message",progressThumbClass:"progress-bar progress-bar-striped active progress-bar-animated",progressClass:"progress-bar bg-success progress-bar-success progress-bar-striped active progress-bar-animated",progressInfoClass:"progress-bar bg-info progress-bar-info progress-bar-striped active progress-bar-animated",progressCompleteClass:"progress-bar bg-success progress-bar-success",progressPauseClass:"progress-bar bg-primary progress-bar-primary progress-bar-striped active progress-bar-animated",progressErrorClass:"progress-bar bg-danger progress-bar-danger",progressUploadThreshold:99,previewFileType:"image",elCaptionContainer:null,elCaptionText:null,elPreviewContainer:null,elPreviewImage:null,elPreviewStatus:null,elErrorContainer:null,errorCloseButton:N.closeButton("kv-error-close"),slugCallback:null,dropZoneEnabled:!0,dropZoneTitleClass:"file-drop-zone-title",fileActionSettings:{},otherActionButtons:"",textEncoding:"UTF-8",preProcessUpload:null,ajaxSettings:{},ajaxDeleteSettings:{},showAjaxErrorDetails:!0,mergeAjaxCallbacks:!1,mergeAjaxDeleteCallbacks:!1,retryErrorUploads:!0,reversePreviewOrder:!1,usePdfRenderer:function(){var e=!!window.MSInputMethodContext&&!!document.documentMode;return!!navigator.userAgent.match(/(iPod|iPhone|iPad|Android)/i)||e},pdfRendererUrl:"",pdfRendererTemplate:'<iframe class="kv-preview-data file-preview-pdf" src="{renderer}?file={data}" {style}></iframe>'},L.fn.fileinputLocales.en={fileSingle:"file",filePlural:"files",browseLabel:"Browse &hellip;",removeLabel:"Remove",removeTitle:"Clear all unprocessed files",cancelLabel:"Cancel",cancelTitle:"Abort ongoing upload",pauseLabel:"Pause",pauseTitle:"Pause ongoing upload",uploadLabel:"Upload",uploadTitle:"Upload selected files",msgNo:"No",msgNoFilesSelected:"No files selected",msgCancelled:"Cancelled",msgPaused:"Paused",msgPlaceholder:"Select {files} ...",msgZoomModalHeading:"Detailed Preview",msgFileRequired:"You must select a file to upload.",msgSizeTooSmall:'File "{name}" (<b>{size} KB</b>) is too small and must be larger than <b>{minSize} KB</b>.',msgSizeTooLarge:'File "{name}" (<b>{size} KB</b>) exceeds maximum allowed upload size of <b>{maxSize} KB</b>.',msgFilesTooLess:"You must select at least <b>{n}</b> {files} to upload.",msgFilesTooMany:"Number of files selected for upload <b>({n})</b> exceeds maximum allowed limit of <b>{m}</b>.",msgTotalFilesTooMany:"You can upload a maximum of <b>{m}</b> files (<b>{n}</b> files detected).",msgFileNotFound:'File "{name}" not found!',msgFileSecured:'Security restrictions prevent reading the file "{name}".',msgFileNotReadable:'File "{name}" is not readable.',msgFilePreviewAborted:'File preview aborted for "{name}".',msgFilePreviewError:'An error occurred while reading the file "{name}".',msgInvalidFileName:'Invalid or unsupported characters in file name "{name}".',msgInvalidFileType:'Invalid type for file "{name}". Only "{types}" files are supported.',msgInvalidFileExtension:'Invalid extension for file "{name}". Only "{extensions}" files are supported.',msgFileTypes:{image:"image",html:"HTML",text:"text",video:"video",audio:"audio",flash:"flash",pdf:"PDF",object:"object"},msgUploadAborted:"The file upload was aborted",msgUploadThreshold:"Processing &hellip;",msgUploadBegin:"Initializing &hellip;",msgUploadEnd:"Done",msgUploadResume:"Resuming upload &hellip;",msgUploadEmpty:"No valid data available for upload.",msgUploadError:"Upload Error",msgDeleteError:"Delete Error",msgProgressError:"Error",msgValidationError:"Validation Error",msgLoading:"Loading file {index} of {files} &hellip;",msgProgress:"Loading file {index} of {files} - {name} - {percent}% completed.",msgSelected:"{n} {files} selected",msgFoldersNotAllowed:"Drag & drop files only! {n} folder(s) dropped were skipped.",msgImageWidthSmall:'Width of image file "{name}" must be at least {size} px.',msgImageHeightSmall:'Height of image file "{name}" must be at least {size} px.',msgImageWidthLarge:'Width of image file "{name}" cannot exceed {size} px.',msgImageHeightLarge:'Height of image file "{name}" cannot exceed {size} px.',msgImageResizeError:"Could not get the image dimensions to resize.",msgImageResizeException:"Error while resizing the image.<pre>{errors}</pre>",msgAjaxError:"Something went wrong with the {operation} operation. Please try again later!",msgAjaxProgressError:"{operation} failed",msgDuplicateFile:'File "{name}" of same size "{size} KB" has already been selected earlier. Skipping duplicate selection.',msgResumableUploadRetriesExceeded:"Upload aborted beyond <b>{max}</b> retries for file <b>{file}</b>! Error Details: <pre>{error}</pre>",msgPendingTime:"{time} remaining",msgCalculatingTime:"calculating time remaining",ajaxOperations:{deleteThumb:"file delete",uploadThumb:"file upload",uploadBatch:"batch file upload",uploadExtra:"form data upload"},dropZoneTitle:"Drag & drop files here &hellip;",dropZoneClickTitle:"<br>(or click to select {files})",previewZoomButtonTitles:{prev:"View previous file",next:"View next file",toggleheader:"Toggle header",fullscreen:"Toggle full screen",borderless:"Toggle borderless mode",close:"Close detailed preview"}},L.fn.fileinput.Constructor=c,L(document).ready(function(){var e=L("input.file[type=file]");e.length&&e.fileinput()})});
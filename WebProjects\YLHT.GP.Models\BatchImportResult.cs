using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YLHT.GP.Models
{
    /// <summary>
    /// 批量导入结果
    /// </summary>
    public class BatchImportResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 成功添加数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 跳过数（重复）
        /// </summary>
        public int SkippedCount { get; set; }

        /// <summary>
        /// 失败数
        /// </summary>
        public int ErrorCount { get; set; }

        /// <summary>
        /// 错误信息列表
        /// </summary>
        public List<string> Errors { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public BatchImportResult()
        {
            Errors = new List<string>();
        }
    }
}

﻿@using YLHT.GP.Models;
@model YLHT.GP.Models.Book
@{
    Layout = null;
}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="renderer" content="webkit">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <title>修改联系人</title>
    <link href="~/Client/css/bootstrap.min.css" rel="stylesheet">
    <link href="~/Client/css/font-awesome.min.css" rel="stylesheet" />
    <link href="~/Client/css/animate.min.css" rel="stylesheet" />
    <script src="~/Client/js/jquery.min.js"></script>
    <script src="~/Client/js/bootstrap.min.js"></script>
    <script src="~/Client/js/layer/layer.js"></script>
    <script src="~/Client/js/plugins/layer/laydate/laydate.js"></script>
    <link href="~/Client/css/addEditContacts.css" rel="stylesheet" />
    <script src="~/Client/js/addEditContacts.js"></script>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-xs-12 col-title">
                <div class="form-group">
                    <label for="contacts-group">分组：</label>
                    <div id="contacts-group" class="form-control dropdown">
                        @{
                            var groups = (List<BookGroup>)ViewBag.Groups;
                            var showmenu = groups.Where(x => x.GroupId == Model.GroupId).FirstOrDefault();
                            var groupid = showmenu.GroupId;
                        }
                        <button class="btn dropdown-toggle form-control" type="button" id="dropdownMenu1"
                                data-info="@groupid"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">

                            @(showmenu == null ? "全部联系人" : showmenu.GroupName)
                            <span class="caret"></span>
                        </button>
                        <ul id="group-list" class="dropdown-menu" aria-labelledby="dropdownMenu1">
                            <li><a href="#" data-info="0">全部联系人</a></li>
                            @if (ViewBag.Groups != null)
                            {
                                foreach (var item in groups)
                                {
                                    <li><a href="#" data-info="@item.GroupId">@item.GroupName</a></li>
                                }
                            }
                        </ul>
                    </div>
                </div>
                <div class="form-group">
                    <label for="name">姓名：</label>
                    <input id="name" type="text" class="form-control" value="@Model.UserName">
                    <input id="bookid" type="hidden" class="form-control" name="BookId" value="@Model.BookId">
                </div>
                <div class="form-group">
                    <label for="phone">手机：</label>
                    <input id="phone" type="number" class="form-control" value="@Model.Msisdn">
                </div>
                <div class="form-group">
                    <label for="sex">性别：</label>
                    <input id="sex" type="radio" name="Sex" @(Model.Sex==0 ? "checked" : "") onclick="sexclick(0)" value="0">男
                    <input id="sex" type="radio" name="Sex" @(Model.Sex==1 ? "checked" : "") onclick="sexclick(1)" value="1">女
                </div>
                <div class="form-group">
                    <label for="note">备注：</label>
                    <textarea id="note"
                              class="form-control" style="resize:none;">@Model.Remark</textarea>
                </div>
                <div class="form-group">
                    <label for="birthday">生日：</label>
                    <input id="birthday" type="birthday" class="form-control"
                           onclick="laydate({istime: false, format: 'YYYY-MM-DD',max:laydate.now()})"
                           value="@Model.BirthdayTime">
                </div>

                <button class=" btn btn-primary" id="edit">确认修改</button>
            </div>
        </div>
    </div>
</body>
</html>
<script>
    var sexs = 0;
    function sexclick(sex) {
        sexs = sex;
    }
</script>
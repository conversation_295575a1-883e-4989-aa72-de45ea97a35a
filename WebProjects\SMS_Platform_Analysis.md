# 短信平台功能分析文档

> **文档目的**: 为短信平台重构提供全面的功能分析和技术参考
> **生成时间**: 2025-08-01
> **版本**: v1.0

## 📋 目录
1. [项目整体架构](#1-项目整体架构)
2. [核心业务功能模块](#2-核心业务功能模块)
3. [管理功能模块](#3-管理功能模块)
4. [数据模型设计](#4-数据模型设计)
5. [系统配置与部署](#5-系统配置与部署)
6. [重构建议](#6-重构建议)
7. [功能总结](#7-功能总结)

---

## 1. 项目整体架构

### 1.1 项目模块结构
```
WebProjects/
├── YLHT.GP.Client/          # 客户端Web应用（客户使用）
├── YLHT.GP.Manager/         # 管理端Web应用（管理员使用）
├── SalesManClient/          # 销售人员客户端
├── YLHT.GP.Models/          # 数据模型层
├── YLHT.GP.DataSource/      # 数据访问层
├── YLHT.GP.Common/          # 公共组件库
├── YLHT_GP_Business/        # 业务逻辑层
├── SignalRSelfHost/         # SignalR实时通信服务
└── SQL/                     # 数据库脚本
```

### 1.2 技术栈
- **前端**: ASP.NET MVC 5, jQuery, Bootstrap, SignalR
- **后端**: .NET Framework 4.5, C#
- **数据库**: Oracle Database
- **ORM**: SqlSugar, Dapper
- **缓存**: Redis (StackExchange.Redis)
- **日志**: System.Diagnostics.Trace + 自定义FileTraceListener
- **实时通信**: SignalR
- **Web服务**: ASMX Web Services

### 1.3 服务架构
```
客户端应用 (8080) ←→ 内部服务 (5001) ←→ 数据服务 (5002)
管理端应用 (管理端口) ←→ 计费服务 (5002) ←→ Oracle数据库
                    ↓
                SignalR服务 ←→ Redis缓存
```

## 2. 核心业务功能模块

> **核心价值**: 提供完整的短信、彩信、语音短信发送服务

### 2.1 📱 短信发送功能
#### 2.1.1 普通短信发送
- **功能描述**: 支持单条和批量短信发送
- **核心实现**: `SmsOperationController.SendSms()`
- **主要特性**:
  - 支持定时发送
  - 批量号码处理（自动分包）
  - 多种业务类型（SMS、FMS闪信）
  - 扩展号码支持
  - 签名验证

#### 2.1.2 个性化短信发送
- **功能描述**: 支持个性化内容的批量短信发送
- **核心实现**: `SmsOperationController.SendIndivSms()`
- **主要特性**:
  - 文件上传处理
  - 内容压缩传输
  - 分批发送机制
  - 错误号码收集

#### 2.1.3 定时短信管理
- **功能描述**: 短信定时发送的管理
- **核心实现**: `MsgPlanData`相关功能
- **主要特性**:
  - 定时任务创建
  - 通道切换
  - 发送状态监控

### 2.2 🖼️ 彩信发送功能
#### 2.2.1 彩信制作与发送
- **功能描述**: 支持多媒体彩信的制作和发送
- **核心实现**: `MmsOperationController`
- **主要特性**:
  - TMS格式彩信制作
  - 多媒体元素支持（图片、文本、音频）
  - Base64编码处理
  - 彩信模板管理

#### 2.2.2 彩信模板管理
- **功能描述**: 彩信模板的创建、审核、分配
- **核心实现**: `MmsTemplateData`
- **主要特性**:
  - 模板报备
  - 审核流程
  - 模板分配给用户

### 2.3 🔊 语音短信功能
#### 2.3.1 语音短信发送
- **功能描述**: 语音短信的发送功能
- **核心实现**: `VoiceController.SendVoice()`
- **主要特性**:
  - 语音模板选择
  - 短信模板关联
  - 触发短信设置

#### 2.3.2 语音模板管理
- **功能描述**: 语音文件和短信模板的管理
- **核心实现**: `VoiceController`
- **主要特性**:
  - 语音文件上传（支持格式转换）
  - FFmpeg音频处理
  - 远程文件报备
  - 模板审核

## 3. 管理功能模块

> **管理价值**: 提供完整的平台管理和运营支持功能

### 3.1 👥 用户管理
#### 3.1.1 客户管理
- **功能描述**: 客户账号的全生命周期管理
- **核心实现**: `ClientController`
- **主要特性**:
  - 客户注册与认证
  - 权限配置
  - 价格设置
  - 接收方式配置
  - IP白名单管理

#### 3.1.2 员工管理
- **功能描述**: 内部员工账号管理
- **核心实现**: `UserBll`
- **主要特性**:
  - 员工账号创建
  - 角色权限分配
  - 部门管理

#### 3.1.3 权限管理
- **功能描述**: 基于角色的权限控制
- **核心实现**: `PermissionController`
- **主要特性**:
  - 角色定义
  - 菜单权限配置
  - 三级权限体系

### 3.2 🔗 通道管理
#### 3.2.1 通道配置
- **功能描述**: 短信通道的配置和管理
- **核心实现**: `ChannelBLL`
- **主要特性**:
  - 通道参数配置
  - 运营商类型设置
  - 业务类型支持
  - 计费方式配置
  - 流量控制

#### 3.2.2 通道监控
- **功能描述**: 通道状态实时监控
- **核心实现**: `ChannelMonitorController`
- **主要特性**:
  - 通道状态监控
  - 余额监控
  - 成功率统计

#### 3.2.3 通道限速策略
- **功能描述**: 基于时间段的通道限速
- **核心实现**: `ChannelSpeedPolicyService`
- **主要特性**:
  - 定时限速策略
  - 自动策略执行
  - 策略状态管理

### 3.3 ✍️ 签名管理
#### 3.3.1 签名报备
- **功能描述**: 短信签名的报备和管理
- **核心实现**: `SignBll`
- **主要特性**:
  - 签名报备
  - 签名审核
  - 签名路由规则

#### 3.3.2 移动签名报备
- **功能描述**: 移动运营商专用签名报备
- **核心实现**: `T_MOBILE_SIGN_REPORT`表
- **主要特性**:
  - 企业信息管理
  - 法人信息验证
  - 签名材料上传

### 3.4 📝 内容管理
#### 3.4.1 内容模板管理
- **功能描述**: 短信内容模板的管理
- **核心实现**: `ContentPreparationController`
- **主要特性**:
  - 模板创建与审核
  - 敏感词过滤
  - 内容合规检查

#### 3.4.2 敏感词管理
- **功能描述**: 敏感词库的维护
- **核心实现**: `SensitiveWords`
- **主要特性**:
  - 敏感词添加/删除
  - 匹配模式配置
  - 处理类型设置

## 4. 数据模型设计

> **设计原则**: 基于Oracle数据库的关系型数据模型，支持高并发和大数据量处理

### 4.1 📊 核心业务表
| 表名 | 用途 | 关键字段 |
|------|------|----------|
| **MSG_CHANNEL_DETAILS** | 短信发送明细表 | 消息ID、通道ID、手机号、内容、状态 |
| **MSG_USER_DETAILS** | 用户短信明细表 | 用户ID、消息ID、发送时间、费用 |
| **MSG_CHANNEL_REPORT** | 短信状态报告表 | 消息ID、状态码、回执时间 |
| **MSG_USER_REPORT** | 用户状态报告表 | 用户ID、消息ID、最终状态 |
| **MSG_PLAN** | 定时短信表 | 计划ID、执行时间、任务状态 |

### 4.2 👤 用户管理表
| 表名 | 用途 | 关键字段 |
|------|------|----------|
| **BASE_USER** | 客户用户表 | 用户ID、用户名、余额、状态 |
| **BASE_ADMIN** | 管理员表 | 管理员ID、用户名、权限级别 |
| **BASE_ADMIN_ROLE** | 管理员角色表 | 角色ID、角色名、权限配置 |
| **BASE_USER_ROLE** | 用户角色表 | 用户ID、角色ID、分配时间 |

### 4.3 🔗 通道管理表
| 表名 | 用途 | 关键字段 |
|------|------|----------|
| **BASE_CHANNEL** | 通道基础信息表 | 通道ID、通道名、运营商、状态 |
| **BASE_CHANNEL_PROPERTY** | 通道属性表 | 通道ID、属性名、属性值 |
| **BASE_CHANNEL_PRICE** | 通道价格表 | 通道ID、业务类型、价格 |

### 4.4 📝 内容管理表
| 表名 | 用途 | 关键字段 |
|------|------|----------|
| **BASE_TEXTTEMPLATE** | 内容模板表 | 模板ID、模板内容、审核状态 |
| **BASE_KEYWORDS** | 敏感词表 | 关键词、匹配类型、处理方式 |
| **BASE_SIGN** | 签名表 | 签名ID、签名内容、审核状态 |
| **T_MOBILE_SIGN_REPORT** | 移动签名报备表 | 企业信息、签名内容、报备状态 |

## 5. 系统配置与部署

> **部署架构**: 分布式多服务架构，支持高可用和负载均衡

### 5.1 🗄️ 数据库配置
```yaml
数据库类型: Oracle Database
连接池配置: 最大512连接
ORM框架: SqlSugar + Dapper
数据分区: 按时间分区存储历史数据
```

### 5.2 ⚡ 缓存配置
```yaml
Redis服务: 192.168.0.100:6379
主要用途:
  - 用户会话缓存
  - 业务数据缓存
  - 实时数据存储
  - 消息队列缓存
```

### 5.3 🔗 外部服务依赖
```yaml
服务架构:
  - InternalService: 内部业务服务 (5001端口)
  - DataService: 数据服务 (5002端口)
  - BillingService: 计费服务 (5002端口)
  - SignalR服务: 实时通信服务
```

### 5.4 📋 日志配置
```yaml
日志框架: System.Diagnostics.Trace
存储位置: D:\YLHTTraceListener\
轮转策略: 按日期和大小轮转 (300MB)
日志级别: Debug/Info/Warn/Error
```

### 5.5 📁 文件存储
```yaml
目录结构:
  - 上传目录: D:\YLHT.GP\WebProjects\YLHT.GP.Client\UploadFile\
  - 导出目录: D:\MSGDETAILEXPORTDIR
  - 语音文件: D:\VoiceFiles
  - 模板文件: Template目录
```

## 6. 重构建议

> **重构目标**: 现代化架构升级，提升系统性能、可维护性和扩展性

### 6.1 🏗️ 架构优化
| 优化项 | 现状 | 建议方案 | 预期收益 |
|--------|------|----------|----------|
| **微服务化** | 单体应用 | 按业务域拆分微服务 | 提升可维护性、独立部署 |
| **API标准化** | ASMX Web Services | RESTful API + OpenAPI | 提升集成效率、标准化 |
| **容器化部署** | 传统部署 | Docker + Kubernetes | 提升部署效率、资源利用率 |

### 6.2 💻 技术升级
| 升级项 | 现状 | 建议方案 | 优势 |
|--------|------|----------|------|
| **框架升级** | .NET Framework 4.5 | .NET 6/8 | 性能提升、跨平台支持 |
| **前端现代化** | MVC + jQuery | Vue.js/React SPA | 用户体验提升、开发效率 |
| **数据库优化** | 单一Oracle | 分库分表 + 多数据库支持 | 性能提升、数据隔离 |

### 6.3 🚀 功能增强
| 增强项 | 目标 | 实现方案 | 价值 |
|--------|------|----------|------|
| **监控告警** | 全链路监控 | APM + Prometheus + Grafana | 提升运维效率 |
| **配置中心** | 统一配置管理 | Apollo/Nacos | 配置热更新、环境隔离 |
| **消息队列** | 异步处理 | RabbitMQ/Kafka | 系统解耦、性能提升 |
| **API网关** | 统一入口 | Kong/Zuul | 安全控制、流量管理 |

## 7. 功能总结

### 7.1 📋 核心功能清单
```mermaid
graph TD
    A[短信平台] --> B[核心业务功能]
    A --> C[管理功能]
    A --> D[系统功能]

    B --> B1[短信发送]
    B --> B2[彩信发送]
    B --> B3[语音短信]

    C --> C1[用户管理]
    C --> C2[通道管理]
    C --> C3[内容管理]

    D --> D1[数据统计]
    D --> D2[系统监控]
    D --> D3[日志管理]
```

| 功能模块 | 子功能 | 实现状态 | 重要程度 |
|----------|--------|----------|----------|
| **📱 短信发送** | 普通短信、个性化短信、定时短信 | ✅ 完整 | ⭐⭐⭐⭐⭐ |
| **🖼️ 彩信发送** | 彩信制作、发送、模板管理 | ✅ 完整 | ⭐⭐⭐⭐ |
| **🔊 语音短信** | 语音发送、模板管理 | ✅ 完整 | ⭐⭐⭐ |
| **👥 用户管理** | 客户管理、员工管理、权限管理 | ✅ 完整 | ⭐⭐⭐⭐⭐ |
| **🔗 通道管理** | 通道配置、监控、限速策略 | ✅ 完整 | ⭐⭐⭐⭐⭐ |
| **📝 内容管理** | 签名管理、模板管理、敏感词管理 | ✅ 完整 | ⭐⭐⭐⭐ |
| **📊 数据统计** | 发送统计、成功率统计、费用统计 | ✅ 完整 | ⭐⭐⭐⭐ |
| **🔍 系统监控** | 实时监控、告警通知、日志管理 | ✅ 完整 | ⭐⭐⭐⭐ |

### 7.2 🔧 技术特点
| 特点 | 描述 | 技术实现 |
|------|------|----------|
| **高并发处理** | 支持大量并发短信发送 | 多线程 + 消息队列 |
| **多通道支持** | 支持多个运营商通道 | 通道抽象 + 策略模式 |
| **实时通信** | 实时状态更新 | SignalR |
| **数据缓存** | 提升系统性能 | Redis缓存 |
| **日志追踪** | 完整操作记录 | 自定义日志框架 |
| **安全控制** | 多层次权限控制 | RBAC + IP白名单 |

### 7.3 💼 业务价值
| 价值维度 | 具体体现 | 量化指标 |
|----------|----------|----------|
| **提升效率** | 自动化短信发送和管理 | 人工操作减少80% |
| **降低成本** | 智能通道选择和成本控制 | 发送成本降低15-20% |
| **保证质量** | 内容审核和合规检查 | 合规率99.9% |
| **数据洞察** | 详细的统计分析和报表 | 实时数据分析 |

---

## 📞 联系信息
- **技术支持**: 开发团队
- **文档维护**: 系统架构师
- **更新频率**: 根据系统升级同步更新

---
*📅 文档生成时间: 2025-08-01*
*🔍 分析范围: WebProjects目录下所有项目模块*
*📋 版本: v1.0*
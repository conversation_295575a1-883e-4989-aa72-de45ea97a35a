# 短信平台功能分析文档

## 1. 项目整体架构

### 1.1 项目模块结构
```
WebProjects/
├── YLHT.GP.Client/          # 客户端Web应用（客户使用）
├── YLHT.GP.Manager/         # 管理端Web应用（管理员使用）
├── SalesManClient/          # 销售人员客户端
├── YLHT.GP.Models/          # 数据模型层
├── YLHT.GP.DataSource/      # 数据访问层
├── YLHT.GP.Common/          # 公共组件库
├── YLHT_GP_Business/        # 业务逻辑层
├── SignalRSelfHost/         # SignalR实时通信服务
└── SQL/                     # 数据库脚本
```

### 1.2 技术栈
- **前端**: ASP.NET MVC 5, jQuery, Bootstrap, SignalR
- **后端**: .NET Framework 4.5, C#
- **数据库**: Oracle Database
- **ORM**: SqlSugar, Dapper
- **缓存**: Redis (StackExchange.Redis)
- **日志**: System.Diagnostics.Trace + 自定义FileTraceListener
- **实时通信**: SignalR
- **Web服务**: ASMX Web Services

### 1.3 服务架构
```
客户端应用 (8080) ←→ 内部服务 (5001) ←→ 数据服务 (5002)
管理端应用 (管理端口) ←→ 计费服务 (5002) ←→ Oracle数据库
                    ↓
                SignalR服务 ←→ Redis缓存
```

## 2. 核心业务功能模块

### 2.1 短信发送功能
#### 2.1.1 普通短信发送
- **功能描述**: 支持单条和批量短信发送
- **核心实现**: `SmsOperationController.SendSms()`
- **主要特性**:
  - 支持定时发送
  - 批量号码处理（自动分包）
  - 多种业务类型（SMS、FMS闪信）
  - 扩展号码支持
  - 签名验证

#### 2.1.2 个性化短信发送
- **功能描述**: 支持个性化内容的批量短信发送
- **核心实现**: `SmsOperationController.SendIndivSms()`
- **主要特性**:
  - 文件上传处理
  - 内容压缩传输
  - 分批发送机制
  - 错误号码收集

#### 2.1.3 定时短信管理
- **功能描述**: 短信定时发送的管理
- **核心实现**: `MsgPlanData`相关功能
- **主要特性**:
  - 定时任务创建
  - 通道切换
  - 发送状态监控

### 2.2 彩信发送功能
#### 2.2.1 彩信制作与发送
- **功能描述**: 支持多媒体彩信的制作和发送
- **核心实现**: `MmsOperationController`
- **主要特性**:
  - TMS格式彩信制作
  - 多媒体元素支持（图片、文本、音频）
  - Base64编码处理
  - 彩信模板管理

#### 2.2.2 彩信模板管理
- **功能描述**: 彩信模板的创建、审核、分配
- **核心实现**: `MmsTemplateData`
- **主要特性**:
  - 模板报备
  - 审核流程
  - 模板分配给用户

### 2.3 语音短信功能
#### 2.3.1 语音短信发送
- **功能描述**: 语音短信的发送功能
- **核心实现**: `VoiceController.SendVoice()`
- **主要特性**:
  - 语音模板选择
  - 短信模板关联
  - 触发短信设置

#### 2.3.2 语音模板管理
- **功能描述**: 语音文件和短信模板的管理
- **核心实现**: `VoiceController`
- **主要特性**:
  - 语音文件上传（支持格式转换）
  - FFmpeg音频处理
  - 远程文件报备
  - 模板审核

## 3. 管理功能模块

### 3.1 用户管理
#### 3.1.1 客户管理
- **功能描述**: 客户账号的全生命周期管理
- **核心实现**: `ClientController`
- **主要特性**:
  - 客户注册与认证
  - 权限配置
  - 价格设置
  - 接收方式配置
  - IP白名单管理

#### 3.1.2 员工管理
- **功能描述**: 内部员工账号管理
- **核心实现**: `UserBll`
- **主要特性**:
  - 员工账号创建
  - 角色权限分配
  - 部门管理

#### 3.1.3 权限管理
- **功能描述**: 基于角色的权限控制
- **核心实现**: `PermissionController`
- **主要特性**:
  - 角色定义
  - 菜单权限配置
  - 三级权限体系

### 3.2 通道管理
#### 3.2.1 通道配置
- **功能描述**: 短信通道的配置和管理
- **核心实现**: `ChannelBLL`
- **主要特性**:
  - 通道参数配置
  - 运营商类型设置
  - 业务类型支持
  - 计费方式配置
  - 流量控制

#### 3.2.2 通道监控
- **功能描述**: 通道状态实时监控
- **核心实现**: `ChannelMonitorController`
- **主要特性**:
  - 通道状态监控
  - 余额监控
  - 成功率统计

#### 3.2.3 通道限速策略
- **功能描述**: 基于时间段的通道限速
- **核心实现**: `ChannelSpeedPolicyService`
- **主要特性**:
  - 定时限速策略
  - 自动策略执行
  - 策略状态管理

### 3.3 签名管理
#### 3.3.1 签名报备
- **功能描述**: 短信签名的报备和管理
- **核心实现**: `SignBll`
- **主要特性**:
  - 签名报备
  - 签名审核
  - 签名路由规则

#### 3.3.2 移动签名报备
- **功能描述**: 移动运营商专用签名报备
- **核心实现**: `T_MOBILE_SIGN_REPORT`表
- **主要特性**:
  - 企业信息管理
  - 法人信息验证
  - 签名材料上传

### 3.4 内容管理
#### 3.4.1 内容模板管理
- **功能描述**: 短信内容模板的管理
- **核心实现**: `ContentPreparationController`
- **主要特性**:
  - 模板创建与审核
  - 敏感词过滤
  - 内容合规检查

#### 3.4.2 敏感词管理
- **功能描述**: 敏感词库的维护
- **核心实现**: `SensitiveWords`
- **主要特性**:
  - 敏感词添加/删除
  - 匹配模式配置
  - 处理类型设置

## 4. 数据模型设计

### 4.1 核心业务表
- **MSG_CHANNEL_DETAILS**: 短信发送明细表
- **MSG_USER_DETAILS**: 用户短信明细表
- **MSG_CHANNEL_REPORT**: 短信状态报告表
- **MSG_USER_REPORT**: 用户状态报告表
- **MSG_PLAN**: 定时短信表

### 4.2 用户管理表
- **BASE_USER**: 客户用户表
- **BASE_ADMIN**: 管理员表
- **BASE_ADMIN_ROLE**: 管理员角色表
- **BASE_USER_ROLE**: 用户角色表

### 4.3 通道管理表
- **BASE_CHANNEL**: 通道基础信息表
- **BASE_CHANNEL_PROPERTY**: 通道属性表
- **BASE_CHANNEL_PRICE**: 通道价格表

### 4.4 内容管理表
- **BASE_TEXTTEMPLATE**: 内容模板表
- **BASE_KEYWORDS**: 敏感词表
- **BASE_SIGN**: 签名表
- **T_MOBILE_SIGN_REPORT**: 移动签名报备表

## 5. 系统配置与部署

### 5.1 数据库配置
- **数据库类型**: Oracle Database
- **连接池**: 最大512连接
- **ORM框架**: SqlSugar + Dapper

### 5.2 缓存配置
- **Redis服务**: 192.168.0.100:6379
- **用途**:
  - 用户会话缓存
  - 业务数据缓存
  - 实时数据存储

### 5.3 外部服务依赖
- **InternalService**: 内部业务服务 (5001端口)
- **DataService**: 数据服务 (5002端口)
- **BillingService**: 计费服务 (5002端口)
- **SignalR服务**: 实时通信服务

### 5.4 日志配置
- **日志框架**: System.Diagnostics.Trace
- **日志存储**: 文件系统 (D:\YLHTTraceListener\)
- **日志轮转**: 按日期和大小轮转 (300MB)

### 5.5 文件存储
- **上传目录**: D:\YLHT.GP\WebProjects\YLHT.GP.Client\UploadFile\
- **导出目录**: D:\MSGDETAILEXPORTDIR
- **语音文件**: D:\VoiceFiles

## 6. 重构建议

### 6.1 架构优化
1. **微服务化**: 将单体应用拆分为独立的微服务
2. **API标准化**: 使用RESTful API替代ASMX Web Services
3. **容器化部署**: 使用Docker进行容器化部署

### 6.2 技术升级
1. **框架升级**: 升级到.NET Core/.NET 5+
2. **前端现代化**: 使用Vue.js/React替代传统MVC视图
3. **数据库优化**: 考虑分库分表，支持多数据库

### 6.3 功能增强
1. **监控告警**: 集成APM监控系统
2. **配置中心**: 统一配置管理
3. **消息队列**: 引入消息队列处理异步任务
4. **API网关**: 统一API入口和安全控制

## 7. 功能总结

### 7.1 核心功能清单
1. **短信发送**: 普通短信、个性化短信、定时短信
2. **彩信发送**: 彩信制作、彩信发送、彩信模板管理
3. **语音短信**: 语音发送、语音模板管理
4. **用户管理**: 客户管理、员工管理、权限管理
5. **通道管理**: 通道配置、通道监控、限速策略
6. **内容管理**: 签名管理、模板管理、敏感词管理
7. **数据统计**: 发送统计、成功率统计、费用统计
8. **系统监控**: 实时监控、告警通知、日志管理

### 7.2 技术特点
1. **高并发处理**: 支持大量并发短信发送
2. **多通道支持**: 支持多个运营商通道
3. **实时通信**: SignalR实现实时状态更新
4. **数据缓存**: Redis缓存提升性能
5. **日志追踪**: 完整的操作日志记录
6. **安全控制**: 多层次权限控制和IP白名单

### 7.3 业务价值
1. **提升效率**: 自动化短信发送和管理
2. **降低成本**: 智能通道选择和成本控制
3. **保证质量**: 内容审核和合规检查
4. **数据洞察**: 详细的统计分析和报表

---
*文档生成时间: 2025-08-01*
*分析范围: WebProjects目录下所有项目模块*
*版本: v1.0*
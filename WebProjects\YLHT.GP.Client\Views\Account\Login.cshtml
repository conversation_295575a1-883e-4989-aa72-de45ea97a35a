﻿@model YLHT.GP.Models.LoginModel
@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta http-equiv="X-UA-Compatible" content="IE=11,10,9" />
    <title>嬴联互通短信平台客户 - 登录</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link rel="shortcut icon" href="../favicon.ico">
    <link href="~/Client/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="~/Client/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="~/Client/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="~/Client/css/animate.css" rel="stylesheet">
    <link href="~/Client/css/style.css?v=4.1.0" rel="stylesheet">

    <!--[if lt IE 9]>
            <script language="javascript" type="text/javascript">
                // 以下方式直接跳转
                window.location.href='/Account/Ie';
            </script>
        <![endif]-->
    <script>if (window.top !== window.self) { window.top.location = window.location; }</script>
</head>
<body class="gray-bg">
    <div class="middle-box text-center loginscreen  animated fadeInDown">
        <div>
            <div>
                <h1 class="logo-name">h</h1>
            </div>
            <h3>欢迎使用 短信客户平台</h3>
            @*<form class="m-t" role="form" action="index.html">*@
            
            @using (Html.BeginForm("Login", "Account", FormMethod.Post))
            {
                @Html.AntiForgeryToken();
                if (ViewBag.InfoError != null)
                {
                    <div class="alert alert-warning">

                        <a href="#" class="close" data-dismiss="alert">
                            &times;
                        </a>
                        <strong>错误！</strong>@ViewBag.InfoError
                    </div>
                }
                else if (ViewBag.ImgCode != null)
                {
                    <div class="alert alert-warning">
                        <a href="#" class="close" data-dismiss="alert">
                            &times;
                        </a>
                        <strong>验证码错误！</strong>@ViewBag.ImgCode
                    </div>
                }

                <div class="form-group">
                    <input type="text" name="UserName" autocomplete="off" value="@Model.UserName" class="form-control" placeholder="用户名" required="">
                </div>
                <div class="form-group">
                    <input type="password" name="Password" autocomplete="off" value="@Model.PassWord" class="form-control" placeholder="密码" required="">
                </div>
                if (Model.ImgCode == "Code")
                {
                    <div class="form-inline">
                        <input class="form-control" style="width:250px;" id="ImgCode" name="ImgCode" type="text" placeholder="验证码" ><span><img id="valiCode" src="../Account/GetValidateCode"></span>
                    </div>
                }

                <div class="form-group text-left">
                    <div class="checkbox i-checks">
                        <label class="no-padding">
                            <div class="icheckbox_square-green" style="position: relative;">@Html.CheckBoxFor(x => x.RememberMe, new { style = "position: absolute; opacity: 0;" })<ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255) none repeat scroll 0% 0%; border: 0px none; opacity: 0;"></ins></div><i></i> 使我保持登录状态
                        </label>
                    </div>
                </div>



                <button type="submit" class="btn btn-primary block full-width m-b">登 录</button>
                
                <p class="text-muted text-center">
                    您的ip为：@ViewBag.ip
                </p>
                @*</form>*@
            }
        </div>
    </div>
    <!-- 全局js -->
    <script src="~/Client/js/jquery.min.js?v=2.1.4"></script>
    <script src="~/Client/js/bootstrap.min.js?v=3.3.6"></script>
    <script src="~/Client/js/plugins/iCheck/icheck.min.js"></script>
    <script>
        $(document).ready(function () {
            $('.i-checks').iCheck({
                checkboxClass: 'icheckbox_square-green',
                radioClass: 'iradio_square-green',
            });
        });
        $("#valiCode").bind("click", function () {
            this.src = "/Account/GetValidateCode?time=" + (new Date()).getTime();
        });
    </script>
</body>
</html>

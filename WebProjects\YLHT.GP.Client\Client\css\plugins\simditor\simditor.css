.simditor {
  position: relative;
  border: 1px solid #c9d8db;
}
.simditor .simditor-wrapper {
  position: relative;
  background: #ffffff;
  overflow: hidden;
}
.simditor .simditor-wrapper .simditor-placeholder {
  display: none;
  position: absolute;
  left: 0;
  z-index: 0;
  padding: 22px 15px;
  font-size: 16px;
  font-family: arial, sans-serif;
  line-height: 1.5;
  color: #999999;
  background: transparent;
}
.simditor .simditor-wrapper.toolbar-floating .simditor-toolbar {
  position: fixed;
  top: 0;
  z-index: 10;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.1);
}
.simditor .simditor-wrapper .simditor-image-loading {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
}
.simditor .simditor-wrapper .simditor-image-loading span {
  width: 30px;
  height: 30px;
  background: #ffffff url(../../../img/loading-upload.gif) no-repeat center center;
  border-radius: 30px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -15px 0 0 -15px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.4);
}
.simditor .simditor-wrapper .simditor-image-loading.uploading span {
  background: #ffffff;
  color: #333333;
  font-size: 14px;
  line-height: 30px;
  text-align: center;
}
.simditor .simditor-body {
  padding: 22px 15px 40px;
  min-height: 300px;
  outline: none;
  cursor: text;
  position: relative;
  z-index: 1;
  background: transparent;
}
.simditor .simditor-body a.selected {
  background: #b3d4fd;
}
.simditor .simditor-body a.simditor-mention {
  cursor: pointer;
}
.simditor .simditor-body .simditor-table {
  position: relative;
}
.simditor .simditor-body .simditor-table.resizing {
  cursor: col-resize;
}
.simditor .simditor-body .simditor-table .simditor-resize-handle {
  position: absolute;
  left: 0;
  top: 0;
  width: 10px;
  height: 100%;
  cursor: col-resize;
}
.simditor .simditor-body pre {
  /*min-height: 28px;*/
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  word-wrap: break-word !important;
  white-space: pre-wrap !important;
}
.simditor .simditor-body img {
  cursor: pointer;
}
.simditor .simditor-body img.selected {
  box-shadow: 0 0 0 4px #cccccc;
}
.simditor .simditor-paste-area,
.simditor .simditor-clean-paste-area {
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  padding: 0;
  margin: 0;
}
.simditor .simditor-toolbar {
  border-bottom: 1px solid #eeeeee;
  background: #ffffff;
  width: 100%;
}
.simditor .simditor-toolbar > ul {
  margin: 0;
  padding: 0 0 0 6px;
  list-style: none;
}
.simditor .simditor-toolbar > ul:after {
  content: "";
  display: table;
  clear: both;
}
.simditor .simditor-toolbar > ul > li {
  position: relative;
  float: left;
}
.simditor .simditor-toolbar > ul > li > span.separator {
  display: block;
  float: left;
  background: #cfcfcf;
  width: 1px;
  height: 18px;
  margin: 11px 15px;
}
.simditor .simditor-toolbar > ul > li > .toolbar-item {
  display: block;
  float: left;
  width: 50px;
  height: 40px;
  outline: none;
  color: #333333;
  font-size: 15px;
  line-height: 40px;
  text-align: center;
  text-decoration: none;
}
.simditor .simditor-toolbar > ul > li > .toolbar-item span {
  opacity: 0.6;
}
.simditor .simditor-toolbar > ul > li > .toolbar-item span.fa {
  display: inline;
  line-height: normal;
}
.simditor .simditor-toolbar > ul > li > .toolbar-item:hover span {
  opacity: 1;
}
.simditor .simditor-toolbar > ul > li > .toolbar-item.active {
  background: #eeeeee;
}
.simditor .simditor-toolbar > ul > li > .toolbar-item.active span {
  opacity: 1;
}
.simditor .simditor-toolbar > ul > li > .toolbar-item.disabled {
  cursor: default;
}
.simditor .simditor-toolbar > ul > li > .toolbar-item.disabled span {
  opacity: 0.3;
}
.simditor .simditor-toolbar > ul > li > .toolbar-item.toolbar-item-title span:before {
  content: "T";
  font-size: 19px;
  font-weight: bold;
  font-family: 'Times New Roman';
}
.simditor .simditor-toolbar > ul > li > .toolbar-item.toolbar-item-title.active-h1 span:before {
  content: 'H1';
  font-size: 18px;
}
.simditor .simditor-toolbar > ul > li > .toolbar-item.toolbar-item-title.active-h2 span:before {
  content: 'H2';
  font-size: 18px;
}
.simditor .simditor-toolbar > ul > li > .toolbar-item.toolbar-item-title.active-h3 span:before {
  content: 'H3';
  font-size: 18px;
}
.simditor .simditor-toolbar > ul > li > .toolbar-item.toolbar-item-color {
  font-size: 14px;
  position: relative;
}
.simditor .simditor-toolbar > ul > li > .toolbar-item.toolbar-item-color span:before {
  position: relative;
  top: -2px;
}
.simditor .simditor-toolbar > ul > li > .toolbar-item.toolbar-item-color:after {
  content: '';
  display: block;
  width: 14px;
  height: 4px;
  background: #cccccc;
  position: absolute;
  top: 26px;
  left: 50%;
  margin: 0 0 0 -7px;
}
.simditor .simditor-toolbar > ul > li > .toolbar-item.toolbar-item-color:hover:after {
  background: #999999;
}
.simditor .simditor-toolbar > ul > li > .toolbar-item.toolbar-item-color.disabled:after {
  background: #dfdfdf;
}
.simditor .simditor-toolbar > ul > li.menu-on .toolbar-item {
  position: relative;
  z-index: 21;
  background: #ffffff;
  box-shadow: 0 -3px 3px rgba(0, 0, 0, 0.2);
}
.simditor .simditor-toolbar > ul > li.menu-on .toolbar-item span {
  opacity: 1;
}
.simditor .simditor-toolbar > ul > li.menu-on .toolbar-item.toolbar-item-color:after {
  background: #999999;
}
.simditor .simditor-toolbar > ul > li.menu-on .toolbar-menu {
  display: block;
}
.simditor .simditor-toolbar .toolbar-menu {
  display: none;
  position: absolute;
  top: 40px;
  left: 0;
  z-index: 20;
  background: #ffffff;
  text-align: left;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
}
.simditor .simditor-toolbar .toolbar-menu ul {
  min-width: 160px;
  list-style: none;
  margin: 0;
  padding: 10px 1px;
}
.simditor .simditor-toolbar .toolbar-menu ul > li .menu-item {
  display: block;
  font-size: 16px;
  line-height: 2em;
  padding: 0 10px;
  text-decoration: none;
  color: #666666;
}
.simditor .simditor-toolbar .toolbar-menu ul > li .menu-item:hover {
  background: #f6f6f6;
}
.simditor .simditor-toolbar .toolbar-menu ul > li .menu-item.menu-item-h1 {
  font-size: 24px;
  color: #333333;
}
.simditor .simditor-toolbar .toolbar-menu ul > li .menu-item.menu-item-h2 {
  font-size: 22px;
  color: #333333;
}
.simditor .simditor-toolbar .toolbar-menu ul > li .menu-item.menu-item-h3 {
  font-size: 20px;
  color: #333333;
}
.simditor .simditor-toolbar .toolbar-menu ul > li .menu-item.menu-item-h4 {
  font-size: 18px;
  color: #333333;
}
.simditor .simditor-toolbar .toolbar-menu ul > li .menu-item.menu-item-h5 {
  font-size: 16px;
  color: #333333;
}
.simditor .simditor-toolbar .toolbar-menu ul > li .separator {
  display: block;
  border-top: 1px solid #cccccc;
  height: 0;
  line-height: 0;
  font-size: 0;
  margin: 6px 0;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-color {
  width: 96px;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-color .color-list {
  height: 40px;
  margin: 10px 6px 6px 10px;
  padding: 0;
  min-width: 0;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-color .color-list li {
  float: left;
  margin: 0 4px 4px 0;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-color .color-list li .font-color {
  display: block;
  width: 16px;
  height: 16px;
  background: #dfdfdf;
  border-radius: 2px;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-color .color-list li .font-color:hover {
  opacity: 0.8;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-color .color-list li .font-color.font-color-default {
  background: #333333;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-color .color-list li .font-color-1 {
  background: #E33737;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-color .color-list li .font-color-2 {
  background: #e28b41;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-color .color-list li .font-color-3 {
  background: #c8a732;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-color .color-list li .font-color-4 {
  background: #209361;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-color .color-list li .font-color-5 {
  background: #418caf;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-color .color-list li .font-color-6 {
  background: #aa8773;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-color .color-list li .font-color-7 {
  background: #999999;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-table .menu-create-table {
  background: #ffffff;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-table .menu-create-table table {
  border: none;
  border-collapse: collapse;
  border-spacing: 0;
  table-layout: fixed;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-table .menu-create-table table td {
  height: 16px;
  padding: 0;
  border: 2px solid #ffffff;
  background: #f3f3f3;
  cursor: pointer;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-table .menu-create-table table td:before {
  width: 16px;
  display: block;
  content: "";
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-table .menu-create-table table td.selected {
  background: #cfcfcf;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-table .menu-edit-table {
  display: none;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-table .menu-edit-table ul {
  min-width: 240px;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-image .menu-item-upload-image {
  position: relative;
  overflow: hidden;
}
.simditor .simditor-toolbar .toolbar-menu.toolbar-menu-image .menu-item-upload-image input[type=file] {
  position: absolute;
  right: 0px;
  top: 0px;
  opacity: 0;
  font-size: 100px;
  cursor: pointer;
}
.simditor .simditor-popover {
  display: none;
  padding: 5px 8px 0;
  background: #ffffff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.4);
  border-radius: 2px;
  position: absolute;
  z-index: 2;
}
.simditor .simditor-popover .settings-field {
  margin: 0 0 5px 0;
  font-size: 12px;
  height: 25px;
  line-height: 25px;
}
.simditor .simditor-popover .settings-field label {
  margin: 0 8px 0 0;
  float: left;
}
.simditor .simditor-popover .settings-field input[type=text] {
  float: left;
  width: 200px;
  box-sizing: border-box;
  font-size: 12px;
}
.simditor .simditor-popover .settings-field input[type=text].image-size {
  width: 87px;
}
.simditor .simditor-popover .settings-field .times {
  float: left;
  width: 26px;
  font-size: 12px;
  text-align: center;
}
.simditor .simditor-popover.link-popover .btn-unlink, .simditor .simditor-popover.image-popover .btn-upload, .simditor .simditor-popover.image-popover .btn-restore {
  float: left;
  margin: 0 0 0 8px;
  color: #333333;
  font-size: 14px;
  outline: 0;
}
.simditor .simditor-popover.link-popover .btn-unlink span, .simditor .simditor-popover.image-popover .btn-upload span, .simditor .simditor-popover.image-popover .btn-restore span {
  opacity: 0.6;
}
.simditor .simditor-popover.link-popover .btn-unlink:hover span, .simditor .simditor-popover.image-popover .btn-upload:hover span, .simditor .simditor-popover.image-popover .btn-restore:hover span {
  opacity: 1;
}
.simditor .simditor-popover.image-popover .btn-upload {
  position: relative;
  display: inline-block;
  overflow: hidden;
}
.simditor .simditor-popover.image-popover .btn-upload input[type=file] {
  position: absolute;
  right: 0px;
  top: 0px;
  opacity: 0;
  height: 100%;
  width: 28px;
}
.simditor.simditor-mobile .simditor-toolbar > ul > li > .toolbar-item {
  width: 46px;
}
.simditor.simditor-mobile .simditor-wrapper.toolbar-floating .simditor-toolbar {
  position: absolute;
  top: 0;
  z-index: 10;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.1);
}

.simditor .simditor-body, .editor-style {
  font-size: 16px;
  font-family: arial, sans-serif;
  line-height: 1.6;
  color: #333;
  outline: none;
  word-wrap: break-word;
}
.simditor .simditor-body > :first-child, .editor-style > :first-child {
  margin-top: 0 !important;
}
.simditor .simditor-body a, .editor-style a {
  color: #4298BA;
  text-decoration: none;
  word-break: break-all;
}
.simditor .simditor-body a:visited, .editor-style a:visited {
  color: #4298BA;
}
.simditor .simditor-body a:hover, .editor-style a:hover {
  color: #0F769F;
}
.simditor .simditor-body a:active, .editor-style a:active {
  color: #9E792E;
}
.simditor .simditor-body a:hover, .simditor .simditor-body a:active, .editor-style a:hover, .editor-style a:active {
  outline: 0;
}
.simditor .simditor-body h1, .simditor .simditor-body h2, .simditor .simditor-body h3, .simditor .simditor-body h4, .simditor .simditor-body h5, .simditor .simditor-body h6, .editor-style h1, .editor-style h2, .editor-style h3, .editor-style h4, .editor-style h5, .editor-style h6 {
  font-weight: normal;
  margin: 40px 0 20px;
  color: #000000;
}
.simditor .simditor-body h1, .editor-style h1 {
  font-size: 24px;
}
.simditor .simditor-body h2, .editor-style h2 {
  font-size: 22px;
}
.simditor .simditor-body h3, .editor-style h3 {
  font-size: 20px;
}
.simditor .simditor-body h4, .editor-style h4 {
  font-size: 18px;
}
.simditor .simditor-body h5, .editor-style h5 {
  font-size: 16px;
}
.simditor .simditor-body h6, .editor-style h6 {
  font-size: 16px;
}
.simditor .simditor-body p, .simditor .simditor-body div, .editor-style p, .editor-style div {
  word-wrap: break-word;
  margin: 0 0 15px 0;
  color: #333;
  word-wrap: break-word;
}
.simditor .simditor-body b, .simditor .simditor-body strong, .editor-style b, .editor-style strong {
  font-weight: bold;
}
.simditor .simditor-body i, .simditor .simditor-body em, .editor-style i, .editor-style em {
  font-style: italic;
}
.simditor .simditor-body u, .editor-style u {
  text-decoration: underline;
}
.simditor .simditor-body strike, .simditor .simditor-body del, .editor-style strike, .editor-style del {
  text-decoration: line-through;
}
.simditor .simditor-body ul, .simditor .simditor-body ol, .editor-style ul, .editor-style ol {
  list-style: disc outside none;
  margin: 15px 0;
  padding: 0 0 0 40px;
  line-height: 1.6;
}
.simditor .simditor-body ul ul, .simditor .simditor-body ul ol, .simditor .simditor-body ol ul, .simditor .simditor-body ol ol, .editor-style ul ul, .editor-style ul ol, .editor-style ol ul, .editor-style ol ol {
  padding-left: 30px;
}
.simditor .simditor-body ul ul, .simditor .simditor-body ol ul, .editor-style ul ul, .editor-style ol ul {
  list-style: circle outside none;
}
.simditor .simditor-body ul ul ul, .simditor .simditor-body ol ul ul, .editor-style ul ul ul, .editor-style ol ul ul {
  list-style: square outside none;
}
.simditor .simditor-body ol, .editor-style ol {
  list-style: decimal;
}
.simditor .simditor-body blockquote, .editor-style blockquote {
  border-left: 6px solid #ddd;
  padding: 5px 0 5px 10px;
  margin: 15px 0 15px 15px;
}
.simditor .simditor-body blockquote > :first-child, .editor-style blockquote > :first-child {
  margin-top: 0;
}
.simditor .simditor-body pre, .editor-style pre {
  padding: 10px 5px 10px 10px;
  margin: 15px 0;
  display: block;
  line-height: 18px;
  background: #F0F0F0;
  border-radius: 3px;
  font-size: 13px;
  font-family: 'monaco', 'Consolas', "Liberation Mono", Courier, monospace;
  overflow-x: auto;
  white-space: nowrap;
}
.simditor .simditor-body code, .editor-style code {
  display: inline-block;
  padding: 0 4px;
  margin: 0 5px;
  background: #eeeeee;
  border-radius: 3px;
  font-size: 13px;
  font-family: 'monaco', 'Consolas', "Liberation Mono", Courier, monospace;
}
.simditor .simditor-body hr, .editor-style hr {
  display: block;
  height: 0px;
  border: 0;
  border-top: 1px solid #ccc;
  margin: 15px 0;
  padding: 0;
}
.simditor .simditor-body table, .editor-style table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  border-spacing: 0;
  margin: 15px 0;
}
.simditor .simditor-body table thead, .editor-style table thead {
  background-color: #f9f9f9;
}
.simditor .simditor-body table td, .editor-style table td {
  min-width: 40px;
  height: 30px;
  border: 1px solid #ccc;
  vertical-align: top;
  padding: 2px 4px;
  box-sizing: border-box;
}
.simditor .simditor-body table td.active, .editor-style table td.active {
  background-color: #ffffee;
}
.simditor .simditor-body img, .editor-style img {
  margin: 0 5px;
  vertical-align: middle;
}
.simditor .simditor-body *[data-indent="0"], .editor-style *[data-indent="0"] {
  margin-left: 0px;
}
.simditor .simditor-body *[data-indent="1"], .editor-style *[data-indent="1"] {
  margin-left: 40px;
}
.simditor .simditor-body *[data-indent="2"], .editor-style *[data-indent="2"] {
  margin-left: 80px;
}
.simditor .simditor-body *[data-indent="3"], .editor-style *[data-indent="3"] {
  margin-left: 120px;
}
.simditor .simditor-body *[data-indent="4"], .editor-style *[data-indent="4"] {
  margin-left: 160px;
}
.simditor .simditor-body *[data-indent="5"], .editor-style *[data-indent="5"] {
  margin-left: 200px;
}
.simditor .simditor-body *[data-indent="6"], .editor-style *[data-indent="6"] {
  margin-left: 240px;
}
.simditor .simditor-body *[data-indent="7"], .editor-style *[data-indent="7"] {
  margin-left: 280px;
}
.simditor .simditor-body *[data-indent="8"], .editor-style *[data-indent="8"] {
  margin-left: 320px;
}
.simditor .simditor-body *[data-indent="9"], .editor-style *[data-indent="9"] {
  margin-left: 360px;
}
.simditor .simditor-body *[data-indent="10"], .editor-style *[data-indent="10"] {
  margin-left: 400px;
}

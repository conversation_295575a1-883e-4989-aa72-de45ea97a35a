.col-title {
    padding-top: 10px;
}

.form-control {
    border-radius: 0;
}

.form-control:focus {
    box-shadow: none;
    border-color: #1ab394 !important
}

#dropdownMenu1 {
    padding: 6px 12px;
}

.dropdown {
    box-shadow: none;
    border: 0;
    padding: 0;
}

.dropdown > button {
    z-index: 0;
    border: 1px solid #ccc;
}

.dropdown > .dropdown-menu {
    border-radius: 0;
    width: 100%;
}

.dropdown-menu > li {
    cursor: pointer;
}

body,
.container-fluid {
    overflow: hidden;
}

.btn {
    width: 100%;
    border-radius: 0;
    margin: auto;
    display: block;
    padding: 10px;
}

.laydate_box,
.laydate_box * {
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
}
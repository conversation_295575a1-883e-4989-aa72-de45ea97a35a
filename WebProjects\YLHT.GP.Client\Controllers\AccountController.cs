﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;
using System.Web.Security;
using YLHT.GP.Common;
using YLHT.GP.Models;
using YLHT_GP_Business.Business;

namespace YLHT.GP.Client.Controllers
{
    public class AccountController : Controller
    {
        UserBll mulbll = new UserBll();//用户业务类
        LoginPageBll loginPageBll = new LoginPageBll();
        CommonBll cb = new CommonBll();
        // GET: Account
        [HttpGet]
        public ActionResult Login( LoginModel aModel)
        {
            try
            {
                ViewBag.ip= IpHelper.GetRealIP();
                string username = aModel.UserName;
                //从seesion中获取验证码
                int imgCode = Convert.ToInt32(Session["ImgCode"]);
                HttpCookie cookie = CookiesHelper.GetCookie("YLHTClientInfo");
                if (cookie != null && Convert.ToString(cookie.Values["sUserName"]) != "")
                {
                    if (aModel != null && !string.IsNullOrWhiteSpace(aModel.UserName) && !string.IsNullOrWhiteSpace(aModel.PassWord))
                    {
                        aModel.PassWord = "";
                        aModel.ImgCode = imgCode < 3 ? "" : "Code";
                        return View(aModel);
                    }
                    aModel = new LoginModel();
                    string storedUserName = HttpUtility.UrlDecode(cookie["sUserName"], Encoding.GetEncoding("gb2312"));//获取在cookie中存储的用户名，并用url gb2312解码
                    string storedPassWord = Convert.ToString(cookie.Values["sPassWord"]);
                    if (!string.IsNullOrWhiteSpace(storedUserName) && !string.IsNullOrWhiteSpace(storedPassWord))
                    {
                        aModel.UserName = storedUserName;
                        aModel.RememberMe = true;
                        aModel.PassWord = NativeMethods.DESDecrypt(storedPassWord);
                    }
                    aModel.ImgCode = imgCode < 3 ? "" : "Code";
                   
                    
                }
                else
                {
                    if (aModel != null && !string.IsNullOrWhiteSpace(aModel.UserName) && !string.IsNullOrWhiteSpace(aModel.PassWord))
                    {
                        aModel.PassWord = "";
                        aModel.ImgCode = imgCode < 3 ? "" : "Code";
                        return View(aModel);
                    }
                    aModel = new LoginModel { ImgCode = imgCode < 3 ? "" : "Code" };
                    
                }

                //string d = Request.UrlReferrer.ToString().Substring(Request.UrlReferrer.ToString().LastIndexOf("/") + 1, Request.UrlReferrer.ToString().Length - Request.UrlReferrer.ToString().LastIndexOf("/") - 1);
                if (!string.IsNullOrEmpty(username))
                {
                    int? uid = mulbll.GetUserNameList(username)?.FirstOrDefault()?.UserId;
                    string pagename = loginPageBll.GetLoginPage(uid.Value)?.FirstOrDefault()?.PageName;
                    if (!string.IsNullOrEmpty(pagename))
                    {
                        ViewBag.flag = username;
                        return View(pagename, aModel);
                    }
                }


            }
            catch (Exception ex)
            {
                Trace.TraceError("Index,ex=" + ex);
            }
            return View(aModel);
        }
        string blackIpKey = "clientblackips";
        [HttpPost]
        //[ValidateAntiForgeryToken] //防止XSS攻击
        [AllowAnonymous]
        [Filters.CustomerValidateAntiForgeryToken]//防止XSS攻击
        public ActionResult Login(LoginModel user, string ReturnUrl)
        {           
            Stopwatch watch = new Stopwatch();
            watch.Start();
            int imgCode = Convert.ToInt32(Session["ImgCode"]);

            int imgcode = 0;
            //if (ModelState.IsValid)
            {
                ViewBag.ip = IpHelper.GetRealIP();
                string ip = IpHelper.GetRealIP();//获取ip
                long loginCount = cb.GetRedisDb().HashIncrement(blackIpKey, ip, 1);
                user.LoginIp = ip;
                this.Response.Clear();
                this.Response.BufferOutput = true;
                if (Session["ImgCode"] != null)
                {
                    imgcode = (int)Session["ImgCode"];//从session中获取登陆次数
                }
                if (imgcode >= 3)
                {
                    if (Session["ValidateCode"] != null)
                    {
                        if (Session["ValidateCode"].ToString() == user.ImgCode)
                        {
                            if (loginCount < 10)
                            {
                                if (VerifyUser(user, imgcode))
                                {
                                    cb.GetRedisDb().HashIncrement(blackIpKey, ip, -loginCount);
                                    if (!this.Response.IsRequestBeingRedirected)
                                    {
                                        imgcode = 0;
                                        Session["ImgCode"] = imgcode;
                                        if (Url.IsLocalUrl(ReturnUrl) && ReturnUrl.Length > 1 &&
                                            ReturnUrl.StartsWith("/")
                                            && !ReturnUrl.StartsWith("//") && !ReturnUrl.StartsWith("/\\"))
                                        {
                                            this.Response.Redirect(ReturnUrl, true);
                                        }
                                        else
                                        {
                                            return RedirectToAction("Index", "Home");
                                        }
                                    }
                                }
                            }
                            else
                            {
                                ModelState.AddModelError("InfoError", @"由于你的密码连续出错 账号以锁住 请联系你的平台经理 并提供下方显示的Ip。.");
                                ViewBag.InfoError = "由于你的密码连续出错 账号以锁住 请联系你的平台经理 并提供下方显示的Ip。.";
                                imgcode += 1;
                                user.ImgCode = null;
                            }
                        }
                        else
                        {
                            ModelState.AddModelError("ImgCode", "验证码不正确。");
                            ViewBag.ImgCode = "验证码不正确。.";
                            imgcode += 1;
                            user.ImgCode = null;
                        }
                    }
                    else
                    {
                        ModelState.AddModelError("ImgCode", "验证码已过时");
                        ViewBag.ImgCode = "验证码已过时。.";
                        imgcode += 1;
                        user.ImgCode = null;
                    }
                    Session["ImgCode"] = imgcode;//记录登陆次数赋值给Session
                }
                else
                {
                    if (loginCount < 10) 
                    {
                        if (VerifyUser(user, imgcode))
                        {
                            cb.GetRedisDb().HashIncrement(blackIpKey, ip, -loginCount);
                            if (!this.Response.IsRequestBeingRedirected)
                            {
                                imgcode = 0;
                                Session["ImgCode"] = imgcode;
                                if (Url.IsLocalUrl(ReturnUrl) && ReturnUrl.Length > 1 &&
                                    ReturnUrl.StartsWith("/")
                                    && !ReturnUrl.StartsWith("//") && !ReturnUrl.StartsWith("/\\"))
                                {
                                    this.Response.Redirect(ReturnUrl, true);
                                }
                                else
                                {
                                    return RedirectToAction("Index", "Home");
                                }
                            }
                        }
                        else
                        {
                            ModelState.AddModelError("InfoError", @"您的账号或密码错误，请检查重新登陆.");
                            ViewBag.InfoError = "您的账号或密码错误，请检查重新登陆。.";
                            imgcode += 1;
                            user.ImgCode = null;
                        }
                    }
                    else
                    {
                        ModelState.AddModelError("InfoError", @"由于你的密码连续出错 账号以锁住 请联系你的平台经理 并提供下方显示的Ip。.");
                        ViewBag.InfoError = "由于你的密码连续出错 账号以锁住 请联系你的平台经理 并提供下方显示的Ip。.";
                        imgcode += 1;
                    }
                    Session["ImgCode"] = imgcode;//记录登陆次数赋值给Session
                }
                imgcode = (int)Session["ImgCode"];
                user.ImgCode = imgcode < 3 ? "" : "Code";//判断imgcode是否为3次以上，超过3次赋值Code
                //return View(user);
            }
            if (string.IsNullOrWhiteSpace(user.UserName) || string.IsNullOrWhiteSpace(user.PassWord))
            {
                ModelState.AddModelError("InfoError", "用户名或密码为空！");
                ViewBag.InfoError = "用户名或密码为空！";
                string ip = IpHelper.GetRealIP();//获取ip
                Trace.TraceInformation("{0}登陆客户端系统失败,用户名或密码为空，登陆ip={1},watch={2}", user.UserName, ip, watch.ElapsedMilliseconds);
                imgCode += 1;
                Session["ImgCode"] = imgCode;
                user.ImgCode = imgCode < 3 ? "" : "Code";
                //return View(user);
            }
            

            if (!string.IsNullOrEmpty(user.flag))
            {

                int? uid = mulbll.GetUserNameList(user.flag)?.FirstOrDefault()?.UserId;

                string pagename = loginPageBll.GetLoginPage(uid.Value)?.FirstOrDefault()?.PageName;
                if (!string.IsNullOrEmpty(pagename))
                {
                    ViewBag.flag = user.flag;
                    return View(pagename, user);
                }
            }
            return View(user);

        }

        [HttpPost]
        public ActionResult RemoteLogin(LoginModel user, string ReturnUrl)
        {
            Stopwatch watch = new Stopwatch();
            watch.Start();
            int imgCode = Convert.ToInt32(Session["ImgCode"]);

            int imgcode = 0;
            if (ModelState.IsValid)
            {
                string ip = IpHelper.GetRealIP();//获取ip
                user.LoginIp = ip;
                long loginCount = cb.GetRedisDb().HashIncrement(blackIpKey, ip, 1);
                this.Response.Clear();
                this.Response.BufferOutput = true;
                if (Session["ImgCode"] != null)
                {
                    imgcode = (int)Session["ImgCode"];//从session中获取登陆次数
                }
                if (imgcode >= 3)
                {
                    if (Session["ValidateCode"] != null)
                    {
                        if (Session["ValidateCode"].ToString() == user.ImgCode)
                        {
                            if (loginCount < 10)
                            {
                                if (VerifyUser(user, imgcode))
                                {
                                    if (!this.Response.IsRequestBeingRedirected)
                                    {
                                        imgcode = 0;
                                        Session["ImgCode"] = imgcode;
                                        if (Url.IsLocalUrl(ReturnUrl) && ReturnUrl.Length > 1 &&
                                            ReturnUrl.StartsWith("/")
                                            && !ReturnUrl.StartsWith("//") && !ReturnUrl.StartsWith("/\\"))
                                        {
                                            this.Response.Redirect(ReturnUrl, true);
                                        }
                                        else
                                        {
                                            return RedirectToAction("Index", "Home");
                                        }
                                    }
                                }
                                else
                                {
                                    imgcode += 1;
                                }
                            }
                            else
                            {
                                ModelState.AddModelError("InfoError", @"提供的用户名或密码不正确。");
                                ViewBag.InfoError = "提供的用户名或密码不正确。.";
                                imgcode += 1;
                                user.ImgCode = null;
                            }
                        }
                        else
                        {
                            ModelState.AddModelError("ImgCode", "验证码不正确。");
                            ViewBag.ImgCode = "验证码不正确。.";
                            imgcode += 1;
                            user.ImgCode = null;
                        }
                    }
                    else
                    {
                        ModelState.AddModelError("ImgCode", "验证码已过时");
                        ViewBag.ImgCode = "验证码已过时。.";
                        imgcode += 1;
                        user.ImgCode = null;
                    }
                    Session["ImgCode"] = imgcode;//记录登陆次数赋值给Session
                }
                else
                {
                    if (loginCount < 10) {
                        if (VerifyUser(user, imgcode))
                        {
                            cb.GetRedisDb().HashIncrement(blackIpKey, ip, -loginCount);
                            if (!this.Response.IsRequestBeingRedirected)
                            {
                                imgcode = 0;
                                Session["ImgCode"] = imgcode;
                                if (Url.IsLocalUrl(ReturnUrl) && ReturnUrl.Length > 1 &&
                                    ReturnUrl.StartsWith("/")
                                    && !ReturnUrl.StartsWith("//") && !ReturnUrl.StartsWith("/\\"))
                                {
                                    this.Response.Redirect(ReturnUrl, true);
                                }
                                else
                                {
                                    return RedirectToAction("Index", "Home");
                                }
                            }
                        
                    }
                    else
                    {
                        imgcode += 1;
                    }
                    }
                    else
                    {
                        ModelState.AddModelError("InfoError", @"提供的用户名或密码不正确。");
                        ViewBag.InfoError = "提供的用户名或密码不正确。.";
                        imgcode += 1;
                        user.ImgCode = null;
                    }
                    Session["ImgCode"] = imgcode;//记录登陆次数赋值给Session
                }
                imgcode = (int)Session["ImgCode"];
                user.ImgCode = imgcode < 3 ? "" : "Code";//判断imgcode是否为3次以上，超过3次赋值Code
                return RedirectToAction("login", "account", user);
            }
            if (string.IsNullOrWhiteSpace(user.UserName) || string.IsNullOrWhiteSpace(user.PassWord))
            {
                ModelState.AddModelError("InfoError", "用户名或密码为空！");
                ViewBag.InfoError = "用户名或密码为空！";
                string ip = IpHelper.GetRealIP();//获取ip
                Trace.TraceInformation("{0}登陆客户端系统失败,用户名或密码为空，登陆ip={1},watch={2}", user.UserName, ip, watch.ElapsedMilliseconds);
                imgCode += 1;
                Session["ImgCode"] = imgCode;
                user.ImgCode = imgCode < 3 ? "" : "Code";
                return RedirectToAction("login", "account", user);
            }
            return RedirectToAction("login", "account", user);
        }
        /// <summary>
        /// 验证用户账号
        /// </summary>
        /// <param name="user"></param>
        public bool VerifyUser(LoginModel user, int imgcode)
        {
            int userid;
            if (!mulbll.CheckClientStop(user))
            {
                if (mulbll.CheckClientLogin(user, out userid))
                {
                    //设置forms验证Cookie
                    FormsAuthentication.SetAuthCookie(user.UserName, false);
                    if (user.RememberMe)//判断用户是否记住密码
                    {
                        //创建Cookie对象
                        HttpCookie cookie = CookiesHelper.GetCookie("YLHTClientInfo") ?? new HttpCookie("YLHTClientInfo");
                        //设置cookie值
                        cookie.Values.Set("sUserName", HttpUtility.UrlEncode(user.UserName, Encoding.GetEncoding("gb2312")));
                        cookie.Values.Set("sPassWord", NativeMethods.DESEncrypt(user.PassWord));
                        //设置Cookie过期时间 
                        cookie.Expires = DateTime.Now.AddDays(7);
                        //添加到cookie中
                        CookiesHelper.AddCookie(cookie);
                        Online(user.UserName);
                    }
                    else
                    {
                        //用户没选择记住用户名密码，删除cookie对象
                        CookiesHelper.RemoveCookie("YLHTClientInfo");
                        //创建Cookie对象
                        HttpCookie cookie = CookiesHelper.GetCookie("YLHTClientInfo") ?? new HttpCookie("YLHTClientInfo");
                        cookie.Values.Set("sPassWord", NativeMethods.Md532Encrypt(user.PassWord));
                        //设置Cookie过期时间 
                        cookie.Expires = DateTime.Now.AddDays(1);
                        //添加到cookie中
                        CookiesHelper.AddCookie(cookie);
                    }
                    //同账号只能在一处登陆
                    Online(user.UserName);
                    Session["UserId"] = userid.ToString();
                    Session["UserName"] = user.UserName;
                    Session.Timeout = 60*4;
                    return true;
                }
                else
                {
                    ModelState.AddModelError("InfoError", @"提供的用户名或密码不正确。");
                    ViewBag.InfoError = "提供的用户名或密码不正确。.";
                    imgcode += 1;
                    user.ImgCode = null;
                    return false;
                }
            }
            else
            {
                ModelState.AddModelError("InfoError", "该用户已经停用或删除.");
                ViewBag.InfoError = "该用户已经停用或删除.";
                imgcode += 1;
                user.ImgCode = null;
                return false;
            }
        }
        /// <summary>
        /// 同一个账号只能再一处登陆
        /// </summary>
        /// <param name="name"></param>
        public void Online(string name)
        {
            HttpContext httpContext = System.Web.HttpContext.Current;
            Hashtable userOnline = (Hashtable)httpContext.Application["Online"];
            if (userOnline != null)
            {
                IDictionaryEnumerator idE = userOnline.GetEnumerator();
                string strKey = string.Empty;
                while (idE.MoveNext())
                {
                    if (idE.Value != null && idE.Value.ToString().Equals(name))
                    {
                        strKey = idE.Key.ToString();
                        userOnline[strKey] = "XXXXXX";
                        break;
                    }
                }
            }
            else
            {
                userOnline = new Hashtable();
            }
            userOnline[Session.SessionID] = name;
            httpContext.Application.Lock();
            httpContext.Application["Online"] = userOnline;
            httpContext.Application.UnLock();
        }
        /// <summary>
        /// 获取验证码
        /// </summary>
        /// <returns></returns>
        public ActionResult GetValidateCode()
        {
            ValidateCode vCode = new ValidateCode();
            string code = vCode.CreateValidateCode(4);
            Session["ValidateCode"] = code;
            byte[] bytes = vCode.CreateValidateGraphic(code);
            return File(bytes, @"image/jpeg");
        }
        /// <summary>
        /// 退出
        /// </summary>
        /// <returns></returns>
        public ActionResult LogOff()
        {
            FormsAuthentication.SignOut();
            int UserId = 0;
            object s = Session["UserId"];
            int.TryParse(s + "", out UserId);
            string cacheKey = "ClientMenu_" + UserId.ToString(CultureInfo.InvariantCulture) + "Cache";//员工菜单缓存键的菜单id
            HttpRuntime.Cache.Remove(cacheKey);//删除员工菜单键
            string cacheKey1 = "Client_" + UserId.ToString(CultureInfo.InvariantCulture) + "Cache";//员工菜单缓存键
            HttpRuntime.Cache.Remove(cacheKey1);
            Session.Remove("UserId");//删除Session
            Trace.TraceError($"客户UserId={UserId},退出登陆");
            return RedirectToAction("Login", "Account");
        }
        public ActionResult Ie()
        {
            return View();
        }
    }
}
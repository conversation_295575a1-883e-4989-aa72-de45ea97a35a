/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var u=function(t){return t&&t.Math==Math&&t},i=u("object"==typeof globalThis&&globalThis)||u("object"==typeof window&&window)||u("object"==typeof self&&self)||u("object"==typeof e&&e)||function(){return this}()||Function("return this")(),f=function(t){try{return!!t()}catch(t){return!0}},c=!f((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),a={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,s={f:l&&!a.call({1:2},1)?function(t){var n=l(this,t);return!!n&&n.enumerable}:a},p=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},y={}.toString,h=function(t){return y.call(t).slice(8,-1)},g="".split,m=f((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==h(t)?g.call(t,""):Object(t)}:Object,d=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},b=function(t){return m(d(t))},v=function(t){return"object"==typeof t?null!==t:"function"==typeof t},w=function(t,n){if(!v(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!v(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!v(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!v(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},S={}.hasOwnProperty,T=function(t,n){return S.call(t,n)},O=i.document,P=v(O)&&v(O.createElement),j=!c&&!f((function(){return 7!=Object.defineProperty((t="div",P?O.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),x=Object.getOwnPropertyDescriptor,C={f:c?x:function(t,n){if(t=b(t),n=w(n,!0),j)try{return x(t,n)}catch(t){}if(T(t,n))return p(!s.f.call(t,n),t[n])}},E=function(t){if(!v(t))throw TypeError(String(t)+" is not an object");return t},A=Object.defineProperty,M={f:c?A:function(t,n,r){if(E(t),n=w(n,!0),E(r),j)try{return A(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},R=c?function(t,n,r){return M.f(t,n,p(1,r))}:function(t,n,r){return t[n]=r,t},F=function(t,n){try{R(i,t,n)}catch(r){i[t]=n}return n},N="__core-js_shared__",k=i[N]||F(N,{}),z=Function.toString;"function"!=typeof k.inspectSource&&(k.inspectSource=function(t){return z.call(t)});var I,L,_,D,q=k.inspectSource,B=i.WeakMap,G="function"==typeof B&&/native code/.test(q(B)),W=o((function(t){(t.exports=function(t,n){return k[t]||(k[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),H=0,J=Math.random(),K=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++H+J).toString(36)},Q=W("keys"),U={},V=i.WeakMap;if(G){var Y=k.state||(k.state=new V),X=Y.get,Z=Y.has,$=Y.set;I=function(t,n){return n.facade=t,$.call(Y,t,n),n},L=function(t){return X.call(Y,t)||{}},_=function(t){return Z.call(Y,t)}}else{var tt=Q[D="state"]||(Q[D]=K(D));U[tt]=!0,I=function(t,n){return n.facade=t,R(t,tt,n),n},L=function(t){return T(t,tt)?t[tt]:{}},_=function(t){return T(t,tt)}}var nt,rt,et={set:I,get:L,has:_,enforce:function(t){return _(t)?L(t):I(t,{})},getterFor:function(t){return function(n){var r;if(!v(n)||(r=L(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},ot=o((function(t){var n=et.get,r=et.enforce,e=String(String).split("String");(t.exports=function(t,n,o,u){var f,c=!!u&&!!u.unsafe,a=!!u&&!!u.enumerable,l=!!u&&!!u.noTargetGet;"function"==typeof o&&("string"!=typeof n||T(o,"name")||R(o,"name",n),(f=r(o)).source||(f.source=e.join("string"==typeof n?n:""))),t!==i?(c?!l&&t[n]&&(a=!0):delete t[n],a?t[n]=o:R(t,n,o)):a?t[n]=o:F(n,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||q(this)}))})),ut=i,it=function(t){return"function"==typeof t?t:void 0},ft=function(t,n){return arguments.length<2?it(ut[t])||it(i[t]):ut[t]&&ut[t][n]||i[t]&&i[t][n]},ct=Math.ceil,at=Math.floor,lt=function(t){return isNaN(t=+t)?0:(t>0?at:ct)(t)},st=Math.min,pt=function(t){return t>0?st(lt(t),9007199254740991):0},yt=Math.max,ht=Math.min,gt=function(t){return function(n,r,e){var o,u=b(n),i=pt(u.length),f=function(t,n){var r=lt(t);return r<0?yt(r+n,0):ht(r,n)}(e,i);if(t&&r!=r){for(;i>f;)if((o=u[f++])!=o)return!0}else for(;i>f;f++)if((t||f in u)&&u[f]===r)return t||f||0;return!t&&-1}},mt={includes:gt(!0),indexOf:gt(!1)}.indexOf,dt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),bt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=b(t),o=0,u=[];for(r in e)!T(U,r)&&T(e,r)&&u.push(r);for(;n.length>o;)T(e,r=n[o++])&&(~mt(u,r)||u.push(r));return u}(t,dt)}},vt={f:Object.getOwnPropertySymbols},wt=ft("Reflect","ownKeys")||function(t){var n=bt.f(E(t)),r=vt.f;return r?n.concat(r(t)):n},St=function(t,n){for(var r=wt(n),e=M.f,o=C.f,u=0;u<r.length;u++){var i=r[u];T(t,i)||e(t,i,o(n,i))}},Tt=/#|\.prototype\./,Ot=function(t,n){var r=jt[Pt(t)];return r==Ct||r!=xt&&("function"==typeof n?f(n):!!n)},Pt=Ot.normalize=function(t){return String(t).replace(Tt,".").toLowerCase()},jt=Ot.data={},xt=Ot.NATIVE="N",Ct=Ot.POLYFILL="P",Et=Ot,At=C.f,Mt=Array.isArray||function(t){return"Array"==h(t)},Rt=function(t){return Object(d(t))},Ft=function(t,n,r){var e=w(n);e in t?M.f(t,e,p(0,r)):t[e]=r},Nt="process"==h(i.process),kt=ft("navigator","userAgent")||"",zt=i.process,It=zt&&zt.versions,Lt=It&&It.v8;Lt?rt=(nt=Lt.split("."))[0]+nt[1]:kt&&(!(nt=kt.match(/Edge\/(\d+)/))||nt[1]>=74)&&(nt=kt.match(/Chrome\/(\d+)/))&&(rt=nt[1]);var _t,Dt=rt&&+rt,qt=!!Object.getOwnPropertySymbols&&!f((function(){return!Symbol.sham&&(Nt?38===Dt:Dt>37&&Dt<41)})),Bt=qt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Gt=W("wks"),Wt=i.Symbol,Ht=Bt?Wt:Wt&&Wt.withoutSetter||K,Jt=function(t){return T(Gt,t)&&(qt||"string"==typeof Gt[t])||(qt&&T(Wt,t)?Gt[t]=Wt[t]:Gt[t]=Ht("Symbol."+t)),Gt[t]},Kt=Jt("species"),Qt=function(t,n){var r;return Mt(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!Mt(r.prototype)?v(r)&&null===(r=r[Kt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Ut=Jt("species"),Vt=Jt("isConcatSpreadable"),Yt=9007199254740991,Xt="Maximum allowed index exceeded",Zt=Dt>=51||!f((function(){var t=[];return t[Vt]=!1,t.concat()[0]!==t})),$t=(_t="concat",Dt>=51||!f((function(){var t=[];return(t.constructor={})[Ut]=function(){return{foo:1}},1!==t[_t](Boolean).foo}))),tn=function(t){if(!v(t))return!1;var n=t[Vt];return void 0!==n?!!n:Mt(t)};!function(t,n){var r,e,o,u,f,c=t.target,a=t.global,l=t.stat;if(r=a?i:l?i[c]||F(c,{}):(i[c]||{}).prototype)for(e in n){if(u=n[e],o=t.noTargetGet?(f=At(r,e))&&f.value:r[e],!Et(a?e:c+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof u==typeof o)continue;St(u,o)}(t.sham||o&&o.sham)&&R(u,"sham",!0),ot(r,e,u,t)}}({target:"Array",proto:!0,forced:!Zt||!$t},{concat:function(t){var n,r,e,o,u,i=Rt(this),f=Qt(i,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(tn(u=-1===n?i:arguments[n])){if(c+(o=pt(u.length))>Yt)throw TypeError(Xt);for(r=0;r<o;r++,c++)r in u&&Ft(f,c,u[r])}else{if(c>=Yt)throw TypeError(Xt);Ft(f,c++,u)}return f.length=c,f}}),r.default.fn.bootstrapTable.locales["zh-CN"]=r.default.fn.bootstrapTable.locales.zh={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"正在努力地加载数据中，请稍候"},formatRecordsPerPage:function(t){return"每页显示 ".concat(t," 条记录")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"显示第 ".concat(t," 到第 ").concat(n," 条记录，总共 ").concat(r," 条记录（从 ").concat(e," 总记录中过滤）"):"显示第 ".concat(t," 到第 ").concat(n," 条记录，总共 ").concat(r," 条记录")},formatSRPaginationPreText:function(){return"上一页"},formatSRPaginationPageText:function(t){return"第".concat(t,"页")},formatSRPaginationNextText:function(){return"下一页"},formatDetailPagination:function(t){return"总共 ".concat(t," 条记录")},formatClearSearch:function(){return"清空过滤"},formatSearch:function(){return"搜索"},formatNoMatches:function(){return"没有找到匹配的记录"},formatPaginationSwitch:function(){return"隐藏/显示分页"},formatPaginationSwitchDown:function(){return"显示分页"},formatPaginationSwitchUp:function(){return"隐藏分页"},formatRefresh:function(){return"刷新"},formatToggle:function(){return"切换"},formatToggleOn:function(){return"显示卡片视图"},formatToggleOff:function(){return"隐藏卡片视图"},formatColumns:function(){return"列"},formatColumnsToggleAll:function(){return"切换所有"},formatFullscreen:function(){return"全屏"},formatAllRows:function(){return"所有"},formatAutoRefresh:function(){return"自动刷新"},formatExport:function(){return"导出数据"},formatJumpTo:function(){return"跳转"},formatAdvancedSearch:function(){return"高级搜索"},formatAdvancedCloseButton:function(){return"关闭"},formatFilterControlSwitch:function(){return"隐藏/显示过滤控制"},formatFilterControlSwitchHide:function(){return"隐藏过滤控制"},formatFilterControlSwitchShow:function(){return"显示过滤控制"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["zh-CN"])}));
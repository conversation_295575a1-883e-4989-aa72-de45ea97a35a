body {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #edf0f0;
    overflow: hidden;
}

.container-fluid {
    overflow-x: hidden;
    overflow-y: auto;
    width: 100%;
    height: 100%;
}

.breadcrumb {
    margin: 0 -15px;
    background-color: transparent;
}

.row {
    position: absolute;
    left: 15px;
    right: 15px;
    top: 56px;
    bottom: 20px;
    overflow-x: hidden;
    overflow-y: auto;
}

.row > div {
    height: 100%;
}

.form-control {
    border-radius: 0;
}

.form-control:focus {
    box-shadow: none;
    border-color: #1ab394 !important
}

.input-group-addon {
    background-color: #fff;
    border-radius: 0;
    border-left: 0;
    cursor: pointer;
}

.down-menu {
    display: none;
    list-style: none;
    position: absolute;
    top: 35px;
    left: 15px;
    padding: 0;
    margin: 0;
    background-color: #f2f2f2;
    z-index: 3;
    border: 1px solid #ccc;
}

.down-menu > li {
    padding: 6px 12px;
    cursor: pointer;
    border-top: 1px solid #ddd;
}

.down-menu > li:hover {
    background-color: #ddd;
}

.dropdown {
    box-shadow: none;
    border: 0;
    padding: 0;
}

.dropdown > button {
    z-index: 0;
    border: 1px solid #ccc;
}

.dropdown > .dropdown-menu {
    border-radius: 0;
    width: 100%;
}

.dropdown-menu > li {
    cursor: pointer;
}

.contacts {
    padding: 0;
    margin: 0;
    background-color: #f6f6f6;;
    list-style: none;
    position: absolute;
    left: 15px;
    right: 15px;
    top: 75px;
    bottom: 0;
    overflow-x: hidden;
    overflow-y: auto;
    max-height: 550px;
}

.contacts > .hint {
    padding: 0 10px;
    height: 25px;
    line-height: 25px;
    background-color: #E9E9E9;
    color: #9C9C9C;
}

.contacts > .hint > i {
    float: right;
    margin-top: 3.5px;
    color: #00C1B3;
    font-size: 18px;
    cursor: pointer;
}

.contacts > .itme {
    position: relative;
    height: 60px;
    background-color: #F6F6F6;
    cursor: pointer;
}

.contacts > .select,
.contacts > .itme:hover {
    background-color: #CBE7F4;
}

.contacts > .itme > .img {
    position: absolute;
    width: 40px;
    height: 40px;
    line-height: 40px;
    margin: 10px;
    border-radius: 50%;
    color: #fff;
    text-align: center;
    font-size: 1.4em;
}

.contacts > .itme > .name {
    display: block;
    height: 60px;
    line-height: 60px;
    position: absolute;
    top: 0;
    left: 60px;
    right: 60px;
    padding: 0 5px;
    font-size: 1.2em;

}

.contacts > .itme > .checkbox {
    position: absolute;
    top: 12px;
    right: 23px;
}

.contacts > .itme > .checkbox > i {
    display: none;
    width: 24px;
    height: 24px;
    background: url(../../images/checkbox.png) no-repeat;
    cursor: pointer;
}

.contacts > .itme:hover > .checkbox > i {
    display: block;
    background-position: -5px -5px;
}

.contacts > .itme > .checkbox > input {
    display: none;
}

.contacts > .itme > .checkbox > input:checked + i {
    display: block;
    background-position: -39px -5px;
}

.right-header {
    text-align: center;
}

.right-head,
.right-header {
    position: relative;
    height: 34px;
    line-height: 34px;
    background-color: #f9f9f9;
    padding: 0 20px;
}

.right-head,
.right-header {
    margin-bottom: 3px;
}

.right-head > a,
.right-header > a {
    text-decoration: none;
    cursor: pointer;
}

.right-header > a {
    display: none;
}

.right-head > a[name="add_contacts"] {
    float: right;
}

.contacts-i > a[name="compile"] {
    float: left;
    display: inline;
}

.contacts-a > a[name="cancel"] {
    float: left;
    display: inline;
}

.contacts-a > a[name="delete"] {
    float: right;
    color: red !important;
    display: inline;

}

.contacts-a > a[name="move"] {
    margin-right: 20px;
    float: right;
    display: inline;
}

#contacts-n {
    font-size: 16px;
    color: #999;
}

.contacts-div {
    position: absolute;
    top: 75px;
    left: 15px;
    right: 15px;
    bottom: 0;
    background-color: #fff;
    overflow-x: hidden;
    overflow-y: auto;
    max-height: 550px;
}

.contacts-div > ul {
    list-style: none;
    margin: 0;
    padding: 0;

}

.contacts-div > .contacts-list > li {
    float: left;
    margin: 25px;
    text-align: center;
    width: 98px;
    height: 130px;
    overflow: hidden;
    text-align: center;
}

.contacts-div > .contacts-list > li > .img {
    width: 96px;
    height: 96px;
    line-height: 96px;
    border-radius: 50%;
    font-size: 2em;
    color: #fff;
}

.contacts-div > .contacts-list > li > p {
    font-size: 14px;
    color: #9c9c9c;
    margin-top: 10px;
    width: 98px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.contacts-div > .contacts-info {
    /*color: #999;*/
}

.contacts-div > .contacts-info > .contacts-name {
    margin: 20px;
    height: 70px;
    position: relative;
}

.contacts-div > .contacts-info > .contacts-name > .img {
    float: left;
    border-radius: 50%;
    width: 70px;
    height: 70px;
    text-align: center;
    line-height: 70px;
    font-size: 2em;
    color: #fff;
}

.contacts-div > .contacts-info > .contacts-name > .name {
    position: absolute;
    left: 95px;
    right: 0;
    border-bottom: 1px solid #e5e5e5;
}

.contacts-div > .contacts-info > .contacts-name > .name > input {
    width: 280px;
    height: 45px;
    border: 0;
    font-size: 22px;
    outline: none;
}

.contacts-div > .contacts-info > .contacts-name > .name > input:disabled {
    background-color: transparent;
}

.contacts-div > .contacts-info > .contacts-content {
    padding: 0 20px 0 115px;
    font-size: 15px;
}

.contacts-div > .contacts-info > .contacts-content > label {
    font-weight: 500;
    color: #999;
}

.contacts-div > .contacts-info > .contacts-content > span {
    margin-left: 20px;
}

.contacts-div > .contacts-info > .contacts-content > hr {
    margin: 15px auto;
    border-top: 1px solid #e5e5e5;
}

.scrollbar::-webkit-scrollbar {
    width: 2px;
    background-color: #f5f5f5;
}

.scrollbar::-webkit-scrollbar-track {
    border-radius: 10px;
    background-color: #f5f5f5;
}

.scrollbar::-webkit-scrollbar-thumb {
    position: fixed;
    width: 2px;
    background-color: #555;
}

@media (max-width: 767px) {
    .to {
        margin-top: 20px;
    }
}

.color1 {
    background-color: #e26f41
}

.color2 {
    background-color: #dd5847
}

.color3 {
    background-color: #6e6bc8
}

.color4 {
    background-color: #517bd1
}

.color5 {
    background-color: #28bc92
}

.color6 {
    background-color: #54ae56
}

.color7 {
    background-color: #e9a63b
}


.loading{
	display:none;
    /*固定loading*/
    position: absolute;
    top: 50%;
    left: 50%;
    /*垂直水平居中*/
    margin: -20px 0 0 -20px;
    width: 40px;
    height: 40px;
    border: 2px solid;
    border-color: #333 #333 transparent;
    border-radius: 50%;
    box-sizing: border-box;
    /*动画时间1s，线性变化，无限循环*/
    animation: loading 1s linear infinite;
}
@keyframes loading{
    0%{
        transform: rotate(0deg);
    }
    100%{
        transform: rotate(360deg);
    }
}
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8"/>
        <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
        <meta content="" name="description"/>
        <meta content="" name="keywords"/>
        <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport"/>
        <title>
            Bootstrap addtabs
        </title>
        <link href="example/theme/css/bootstrap.min.css" media="screen" rel="stylesheet" type="text/css"/>
        <link href="./bootstrap.addtabs.css" media="screen" rel="stylesheet" type="text/css"/>
        <script src="example/theme/js/jquery.min.js">
        </script>
        <script src="example/theme/js/bootstrap.min.js">
        </script>
        <script src="./bootstrap.addtabs.js">
        </script>
        <script type="text/javascript">
            $(function () {
                $('#closeAll').click(function () {
                    console.log('1');
                    $.addtabs.closeAll('#tabs1');
                })
            })
        </script>
    </head>
</html>
<body>
    <div class="container">
        
           
        <div class="row">
                
            <div class="col-sm-9">
                <h4>
                    Example
                </h4>
                <hr>
                    <div class="panel panel-default">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-sm-2">
                                    <ul class="list-group">
                                        <a class="list-group-item" data-addtab="mail" data-url="example/ajax/mailbox.txt">
                                            mailbox
                                        </a>
                                        <a class="list-group-item" data-addtab="mail1" data-ajax="true" data-url="example/ajax/mailbox.txt">
                                            Use Ajax
                                        </a>
                                        <a class="list-group-item" data-addtab="message" data-content="Customize the content" data-url="example/ajax/mailbox.txt">
                                            <i class="glyphicon glyphicon-bullhorn">
                                            </i>
                                            指定内容
                                        </a>
                                        <a class="list-group-item" data-addtab="setting" data-title="Customize the title" data-url="example/ajax/mailbox.txt">
                                            <i class="glyphicon glyphicon-cog">
                                            </i>
                                            指定标题
                                        </a>
                                        <a class="list-group-item" data-addtab="userMenu1" data-url="example/ajax/mailbox.txt">
                                            用户菜单1
                                        </a>
                                        <a class="list-group-item" data-addtab="userMenu2" data-target="#tabs2" data-url="example/ajax/button.html">
                                            target2
                                        </a>
                                        <a class="list-group-item" data-addtab="sina" data-url="http://sina.com.cn">
                                            新浪
                                        </a>
                                        <a class="list-group-item" data-addtab="baidu" data-url="">
                                            百度
                                        </a>
                                        <a class="list-group-item" id="closeAll">
                                            关闭所有
                                        </a>
                                    </ul>
                                </div>
                                <div class="col-sm-10">
                                    <div>
                                        <!-- Nav tabs -->
                                        <ul class="nav nav-tabs" id="tabs1" role="tablist">
                                            <li class="active" role="presentation">
                                                <a aria-controls="home" data-toggle="tab" href="#home" role="tab">
                                                    Home
                                                </a>
                                            </li>
                                        </ul>
                                        <!-- Tab panes -->
                                        <div class="tab-content">
                                            <div class="tab-pane active" id="home" role="tabpanel">
                                                I'm a homepage.
                                            </div>
                                        </div>
                                    </div>
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </hr>
            </div>
        </div>
    </div>
</body>

@{
    ViewBag.Title = "配置用户";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@model YLHT.GP.Models.RestSendProduct

<style>
    .user-list {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #ddd;
        padding: 10px;
        margin: 10px 0;
    }
    .user-item {
        padding: 5px;
        margin: 2px 0;
        border: 1px solid #eee;
        background-color: #f9f9f9;
        cursor: pointer;
    }
    .user-item:hover {
        background-color: #e9e9e9;
    }
    .user-item.selected {
        background-color: #d4edda;
        border-color: #c3e6cb;
    }
    .user-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .user-details {
        flex: 1;
    }
    .user-status {
        font-size: 12px;
        color: #666;
    }
    .btn-group {
        margin: 10px 0;
    }
    .search-box {
        margin-bottom: 10px;
    }
    .product-user-item {
        padding: 5px;
        margin: 2px 0;
        border: 1px solid #d4edda;
        background-color: #f8f9fa;
        cursor: pointer;
        position: relative;
    }
    .product-user-item:hover {
        background-color: #e2e6ea;
    }
    .product-user-item .add-single-btn {
        position: absolute;
        right: 5px;
        top: 50%;
        transform: translateY(-50%);
    }
</style>

<nav class="breadcrumb">
    <i class="Hui-iconfont">&#xe67f;</i> 首页 
    <span class="c-gray en">&gt;</span> 系统管理 
    <span class="c-gray en">&gt;</span> 补发策略列表 
    <span class="c-gray en">&gt;</span> 配置用户
</nav>

<div class="page-container">
    <div class="text-c">
        <h3>配置补发规则用户：@ViewBag.RestSendProduct.RESTSENDNAME</h3>
    </div>
    
    <form id="configForm" method="post" action="/RestSendRule/SaveConfigUsers">
        <input type="hidden" name="restSendId" value="@ViewBag.RestSendId" />
        <input type="hidden" name="userIds" id="userIds" value="" />
        
        <div class="row cl">
            <div class="col-xs-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">所有客户账号</h4>
                        <div class="search-box">
                            <input type="text" id="searchAll" class="input-text" placeholder="搜索客户账号或名称，留空查询全部..." style="width: 200px;" onkeypress="if(event.keyCode==13) searchAllUsers();" />
                            <button type="button" class="btn btn-success btn-sm" onclick="searchAllUsers()" style="margin-left: 5px;">
                                <i class="Hui-iconfont">&#xe665;</i> 搜索
                            </button>
                            <button type="button" class="btn btn-default btn-sm" onclick="clearAllSearch()" style="margin-left: 5px;">
                                <i class="Hui-iconfont">&#xe68f;</i> 重置
                            </button>
                        </div>
                        <div class="btn-group" style="margin-top: 10px;">
                            <select id="productSelect" class="select select-box" style="width: 200px;" onchange="loadProductUsers()">
                                <option value="">选择通道组查看用户</option>
                            </select>
                            <button type="button" class="btn btn-info btn-sm" onclick="addAllProductUsers()" id="addAllBtn" style="display:none;">
                                <i class="Hui-iconfont">&#xe600;</i> 一键添加全部
                            </button>
                        </div>
                    </div>
                    <div class="panel-body">
                        <!-- 通道组用户列表 -->
                        <div id="productUsersContainer" style="display:none; margin-bottom: 15px;">
                            <h5 style="color: #337ab7; margin-bottom: 10px;">
                                <i class="Hui-iconfont">&#xe613;</i> 通道组用户列表
                                <small id="productUserCount" style="color: #999;"></small>
                            </h5>
                            <div class="user-list" id="productUsersList" style="max-height: 200px; border-color: #337ab7;">
                                <!-- 通道组用户将在这里显示 -->
                            </div>
                        </div>

                        <!-- 所有用户列表 -->
                        <div class="user-list" id="allUsersList">
                            <div style="text-align:center; color:#999; padding:20px;">
                                <i class="Hui-iconfont">&#xe665;</i> 点击搜索按钮查看全部用户，或输入关键词搜索<br><br>
                                <button type="button" class="btn btn-primary btn-sm" onclick="searchAllUsers()">
                                    <i class="Hui-iconfont">&#xe6dc;</i> 查看全部用户
                                </button>
                            </div>
                        </div>

                        <!-- 加载更多按钮 -->
                        <div id="loadMoreContainer" style="text-align:center; margin-top:10px; display:none;">
                            <button type="button" class="btn btn-default btn-sm" onclick="loadMoreUsers()">
                                <i class="Hui-iconfont">&#xe6dc;</i> 加载更多
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xs-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">已配置用户</h4>
                        <div class="search-box">
                            <input type="text" id="searchConfigured" class="input-text" placeholder="搜索已配置用户..." style="width: 200px;" onkeypress="if(event.keyCode==13) searchConfiguredUsers();" />
                            <button type="button" class="btn btn-success btn-sm" onclick="searchConfiguredUsers()" style="margin-left: 5px;">
                                <i class="Hui-iconfont">&#xe665;</i> 搜索
                            </button>
                            <button type="button" class="btn btn-default btn-sm" onclick="clearConfiguredSearch()" style="margin-left: 5px;">
                                <i class="Hui-iconfont">&#xe68f;</i> 重置
                            </button>
                        </div>
                        <div class="btn-group" style="margin-top: 10px;">
                            <button type="button" class="btn btn-danger btn-sm" onclick="clearAllUsers()">
                                <i class="Hui-iconfont">&#xe6e2;</i> 一键清空全部
                            </button>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="user-list" id="configuredUsersList">
                            @if (ViewBag.ConfiguredUsers != null)
                            {
                                foreach (YLHT.GP.Models.ClientModel user in (List<YLHT.GP.Models.ClientModel>)ViewBag.ConfiguredUsers)
                                {
                                    <div class="user-item configured" data-userid="@user.UserId" data-username="@user.UserName" data-customername="@user.CustomerName">
                                        <div class="user-info">
                                            <div class="user-details">
                                                <div><strong>@user.UserName</strong></div>
                                                <div>@user.CustomerName</div>
                                                <div class="user-status">ID: @user.UserId | 状态: @(user.Status == 1 ? "正常" : user.Status == 0 ? "停用" : "删除")</div>
                                            </div>
                                            <div>
                                                <button type="button" class="btn btn-danger btn-xs remove-user">&lt; 移除</button>
                                            </div>
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="btn-group text-c">
            <button type="button" class="btn btn-success" onclick="saveConfig()">
                <i class="Hui-iconfont">&#xe632;</i> 保存配置
            </button>
            <button type="button" class="btn btn-default" onclick="parent.layer.closeAll();">
                <i class="Hui-iconfont">&#xe6b3;</i> 取消
            </button>
        </div>
    </form>
</div>

<script type="text/javascript">
// 全局变量
var currentSearchText = '';
var currentPage = 0;
var pageSize = 50;
var hasMoreUsers = false;

    $(function () {
    // 初始化通道组下拉列表
    loadProductList();
        $("#productSelect").select2();

    // 添加用户到已配置列表
    $(document).on('click', '.add-user', function() {
        var userItem = $(this).closest('.user-item');
        var userId = userItem.data('userid');
        var userName = userItem.data('username');
        var customerName = userItem.data('customername');
        
        // 检查是否已经存在
        if ($('#configuredUsersList .user-item[data-userid="' + userId + '"]').length > 0) {
            layer.msg('该用户已经在配置列表中', {icon: 5, time: 1000});
            return;
        }
        
        // 创建新的配置用户项
        var configuredItem = userItem.clone();
        configuredItem.addClass('configured');
        configuredItem.find('.add-user').removeClass('add-user btn-primary').addClass('remove-user btn-danger').html('&lt; 移除');
        
        $('#configuredUsersList').append(configuredItem);
        updateUserIds();
    });
    
    // 从已配置列表移除用户
    $(document).on('click', '.remove-user', function() {
        var userItem = $(this).closest('.user-item');
        var userId = userItem.data('userid');

        // 更新通道组用户列表中对应按钮的状态
        var productUserBtn = $('#productUsersList .product-user-item[data-userid="' + userId + '"] .add-single-btn');
        if (productUserBtn.length > 0) {
            productUserBtn.removeClass('btn-default').addClass('btn-primary').text('添加').prop('disabled', false);
        }

        userItem.remove();
        updateUserIds();
    });
    
    // 移除原来的实时搜索功能，改为按钮触发
    
    // 初始化用户ID列表
    updateUserIds();
});

// 更新用户ID列表
function updateUserIds() {
    var userIds = [];
    $('#configuredUsersList .user-item').each(function() {
        userIds.push($(this).data('userid'));
    });
    $('#userIds').val(userIds.join(','));
}

// 保存配置
function saveConfig() {
    updateUserIds();
    $('#configForm').ajaxSubmit({
        success: function(response) {
            // 响应已经是JavaScript代码，直接执行
            eval(response);
        },
        error: function() {
            layer.alert('保存失败', {icon: 2});
        }
    });
}

// 加载通道组列表
function loadProductList() {
    $.get('/RestSendRule/GetProductList', function(data) {
        if (data.error) {
            layer.msg(data.error, {icon: 5, time: 2000});
            return;
        }

        var options = '<option value="">选择通道组查看用户</option>';
        $.each(data, function(index, item) {
            options += '<option value="' + item.ProductId + '">' + item.ProductName + (item.Remark ? ' (' + item.Remark + ')' : '') + '</option>';
        });
        $('#productSelect').html(options);
    }).fail(function() {
        layer.msg('加载通道组列表失败', {icon: 5, time: 2000});
    });
}

// 加载通道组用户列表
function loadProductUsers() {
    var productId = $('#productSelect').val();
    if (!productId) {
        $('#productUsersContainer').hide();
        $('#addAllBtn').hide();
        return;
    }

    layer.load(1);
    $.get('/RestSendRule/GetUsersByProductId', { productId: productId }, function(data) {
        layer.closeAll('loading');

        if (data.error) {
            layer.msg(data.error, {icon: 5, time: 2000});
            $('#productUsersContainer').hide();
            $('#addAllBtn').hide();
            return;
        }

        if (!data || data.length === 0) {
            $('#productUsersList').html('<div style="text-align:center; color:#999; padding:20px;">该通道组下没有用户</div>');
            $('#productUserCount').text('(0个用户)');
            $('#productUsersContainer').show();
            $('#addAllBtn').hide();
            return;
        }

        // 显示通道组用户列表
        var html = '';
        $.each(data, function(index, user) {
            var statusText = user.Status == 1 ? '正常' : (user.Status == 0 ? '停用' : '删除');
            var isConfigured = $('#configuredUsersList .user-item[data-userid="' + user.UserId + '"]').length > 0;
            var btnText = isConfigured ? '已添加' : '添加';
            var btnClass = isConfigured ? 'btn-default' : 'btn-primary';
            var btnDisabled = isConfigured ? 'disabled' : '';

            html += '<div class="product-user-item" data-userid="' + user.UserId + '" data-username="' + user.UserName + '" data-customername="' + user.CustomerName + '">' +
                '<div class="user-info">' +
                    '<div class="user-details">' +
                        '<div><strong>' + user.UserName + '</strong></div>' +
                        '<div>' + user.CustomerName + '</div>' +
                        '<div class="user-status">ID: ' + user.UserId + ' | 状态: ' + statusText + '</div>' +
                    '</div>' +
                '</div>' +
                '<button type="button" class="btn ' + btnClass + ' btn-xs add-single-btn" onclick="addSingleProductUser(this)" ' + btnDisabled + '>' + btnText + '</button>' +
            '</div>';
        });

        $('#productUsersList').html(html);
        $('#productUserCount').text('(' + data.length + '个用户)');
        $('#productUsersContainer').show();
        $('#addAllBtn').show();

    }).fail(function() {
        layer.closeAll('loading');
        layer.msg('获取用户列表失败', {icon: 5, time: 2000});
        $('#productUsersContainer').hide();
        $('#addAllBtn').hide();
    });
}

// 添加单个通道组用户
function addSingleProductUser(btn) {
    var userItem = $(btn).closest('.product-user-item');
    var userId = userItem.data('userid');
    var userName = userItem.data('username');
    var customerName = userItem.data('customername');

    // 检查是否已经存在
    if ($('#configuredUsersList .user-item[data-userid="' + userId + '"]').length > 0) {
        layer.msg('该用户已经在配置列表中', {icon: 5, time: 1000});
        return;
    }

    // 创建新的配置用户项
    var configuredItem = $('<div class="user-item configured" data-userid="' + userId + '" data-username="' + userName + '" data-customername="' + customerName + '">' +
        '<div class="user-info">' +
            '<div class="user-details">' +
                '<div><strong>' + userName + '</strong></div>' +
                '<div>' + customerName + '</div>' +
                '<div class="user-status">ID: ' + userId + ' | 状态: 正常</div>' +
            '</div>' +
            '<div>' +
                '<button type="button" class="btn btn-danger btn-xs remove-user">&lt; 移除</button>' +
            '</div>' +
        '</div>' +
    '</div>');

    $('#configuredUsersList').append(configuredItem);
    updateUserIds();

    // 更新按钮状态
    $(btn).removeClass('btn-primary').addClass('btn-default').text('已添加').prop('disabled', true);

    layer.msg('用户添加成功', {icon: 1, time: 1000});
}

// 一键添加通道组所有用户
function addAllProductUsers() {
    var productId = $('#productSelect').val();
    if (!productId) {
        layer.msg('请先选择通道组', {icon: 5, time: 1000});
        return;
    }

    var addedCount = 0;
    $('#productUsersList .product-user-item').each(function() {
        var btn = $(this).find('.add-single-btn');
        if (!btn.prop('disabled')) {
            addSingleProductUser(btn[0]);
            addedCount++;
        }
    });

    if (addedCount > 0) {
        layer.msg('成功添加 ' + addedCount + ' 个用户', {icon: 1, time: 1000});
    } else {
        layer.msg('所有用户都已添加', {icon: 5, time: 1000});
    }
}

// 一键清空所有用户
function clearAllUsers() {
    var userCount = $('#configuredUsersList .user-item').length;
    if (userCount === 0) {
        layer.msg('没有需要清空的用户', {icon: 5, time: 1000});
        return;
    }

    layer.confirm('确定要清空所有已配置的用户吗？', {
        btn: ['确定', '取消'],
        icon: 3,
        title: '确认清空'
    }, function(index) {
        // 重置通道组用户列表中所有按钮的状态
        $('#productUsersList .add-single-btn').each(function() {
            $(this).removeClass('btn-default').addClass('btn-primary').text('添加').prop('disabled', false);
        });

        $('#configuredUsersList').empty();
        updateUserIds();
        layer.close(index);
        layer.msg('已清空所有用户', {icon: 1, time: 1000});
    });
}

// 搜索所有用户
function searchAllUsers() {
    var searchText = $('#searchAll').val().trim();
    currentSearchText = searchText;
    currentPage = 0;

    // 显示加载状态
    var loadingText = searchText === '' ? '加载全部用户中...' : '搜索中...';
    $('#allUsersList').html('<div style="text-align:center; color:#999; padding:20px;"><i class="Hui-iconfont">&#xe6e4;</i> ' + loadingText + '</div>');
    $('#loadMoreContainer').hide();

    $.get('/RestSendRule/SearchUsers', {
        searchText: searchText,
        pageSize: pageSize
    }, function(data) {
        if (data.error) {
            $('#allUsersList').html('<div style="text-align:center; color:#f00; padding:20px;"><i class="Hui-iconfont">&#xe6e2;</i> ' + data.error + '</div>');
            return;
        }

        if (!data.users || data.users.length === 0) {
            var noResultText = searchText === '' ? '没有找到任何用户' : '没有找到匹配的用户';
            $('#allUsersList').html('<div style="text-align:center; color:#999; padding:20px;"><i class="Hui-iconfont">&#xe6e2;</i> ' + noResultText + '</div>');
            return;
        }

        // 渲染用户列表
        renderUserList(data.users, false);
        hasMoreUsers = data.hasMore;

        // 显示加载更多按钮
        if (hasMoreUsers) {
            $('#loadMoreContainer').show();
        }

    }).fail(function() {
        $('#allUsersList').html('<div style="text-align:center; color:#f00; padding:20px;"><i class="Hui-iconfont">&#xe6e2;</i> 搜索失败，请重试</div>');
    });
}

// 加载更多用户
function loadMoreUsers() {
    if (!hasMoreUsers || currentSearchText === '') {
        return;
    }

    currentPage++;
    var loadBtn = $('#loadMoreContainer button');
    loadBtn.prop('disabled', true).html('<i class="Hui-iconfont">&#xe6e4;</i> 加载中...');

    $.get('/RestSendRule/SearchUsers', {
        searchText: currentSearchText,
        pageSize: pageSize,
        skip: currentPage * pageSize
    }, function(data) {
        loadBtn.prop('disabled', false).html('<i class="Hui-iconfont">&#xe6dc;</i> 加载更多');

        if (data.error) {
            layer.msg(data.error, {icon: 5, time: 2000});
            return;
        }

        if (data.users && data.users.length > 0) {
            // 追加用户列表
            renderUserList(data.users, true);
            hasMoreUsers = data.hasMore;

            if (!hasMoreUsers) {
                $('#loadMoreContainer').hide();
                layer.msg('已加载全部用户', {icon: 1, time: 1000});
            }
        } else {
            hasMoreUsers = false;
            $('#loadMoreContainer').hide();
        }

    }).fail(function() {
        loadBtn.prop('disabled', false).html('<i class="Hui-iconfont">&#xe6dc;</i> 加载更多');
        layer.msg('加载失败，请重试', {icon: 5, time: 2000});
    });
}

// 渲染用户列表
function renderUserList(users, append) {
    var html = '';
    $.each(users, function(index, user) {
        var statusText = user.Status == 1 ? '正常' : (user.Status == 0 ? '停用' : '删除');
        html += '<div class="user-item" data-userid="' + user.UserId + '" data-username="' + user.UserName + '" data-customername="' + user.CustomerName + '">' +
            '<div class="user-info">' +
                '<div class="user-details">' +
                    '<div><strong>' + user.UserName + '</strong></div>' +
                    '<div>' + user.CustomerName + '</div>' +
                    '<div class="user-status">ID: ' + user.UserId + ' | 状态: ' + statusText + '</div>' +
                '</div>' +
                '<div>' +
                    '<button type="button" class="btn btn-primary btn-xs add-user">添加 &gt;</button>' +
                '</div>' +
            '</div>' +
        '</div>';
    });

    if (append) {
        $('#allUsersList').append(html);
    } else {
        $('#allUsersList').html(html);
    }
}

// 重置所有用户搜索
function clearAllSearch() {
    $('#searchAll').val('');
    currentSearchText = '';
    currentPage = 0;
    hasMoreUsers = false;
    $('#allUsersList').html('<div style="text-align:center; color:#999; padding:20px;">' +
        '<i class="Hui-iconfont">&#xe665;</i> 点击搜索按钮查看全部用户，或输入关键词搜索<br><br>' +
        '<button type="button" class="btn btn-primary btn-sm" onclick="searchAllUsers()">' +
            '<i class="Hui-iconfont">&#xe6dc;</i> 查看全部用户' +
        '</button>' +
    '</div>');
    $('#loadMoreContainer').hide();
    layer.msg('搜索已重置', {icon: 1, time: 1000});
}

// 搜索已配置用户
function searchConfiguredUsers() {
    var searchText = $('#searchConfigured').val().toLowerCase().trim();
    if (searchText === '') {
        // 如果搜索框为空，显示所有用户
        $('#configuredUsersList .user-item').show();
        return;
    }

    var visibleCount = 0;
    $('#configuredUsersList .user-item').each(function() {
        var userName = $(this).data('username').toLowerCase();
        var customerName = $(this).data('customername').toLowerCase();
        if (userName.indexOf(searchText) >= 0 || customerName.indexOf(searchText) >= 0) {
            $(this).show();
            visibleCount++;
        } else {
            $(this).hide();
        }
    });

    if (visibleCount === 0) {
        layer.msg('没有找到匹配的用户', {icon: 5, time: 1000});
    } else {
        layer.msg('找到 ' + visibleCount + ' 个匹配用户', {icon: 1, time: 1000});
    }
}

// 重置已配置用户搜索
function clearConfiguredSearch() {
    $('#searchConfigured').val('');
    $('#configuredUsersList .user-item').show();
    layer.msg('搜索已重置', {icon: 1, time: 1000});
}
</script>

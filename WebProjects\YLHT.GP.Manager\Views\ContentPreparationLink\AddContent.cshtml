﻿
@{ ViewBag.Title = "添加内容报备链接号码模板";}

    <article class="page-container">
        @using (Html.BeginForm("AddContent", "ContentPreparationLink", FormMethod.Post, new { @class = "form form-horizontal", id = "form1" }))
        {

            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-2"><span class="c-red">*</span>用户账号：</label>
                <div class="formControls col-xs-8 col-sm-9">
                    @Html.DropDownList("UserId", (List<SelectListItem>)ViewBag.client,new { style="width:100%"})
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-2">报备内容：</label>
                <div class="formControls col-xs-8 col-sm-9">
                    <textarea class="textarea" autocomplete="off" placeholder="报备内容" onKeyUp="$.Huitextarealength(this,300)" name="Text" id="Text">@ViewBag.Text</textarea>
                    <button type="button" class="btn btn-link" style="padding:0;margin-left:10px;vertical-align:middle;" onclick="showVarTypeDesc()">变量类型说明</button>
                    <label style="color:red">
                        示例：
                        【青岛求实学院】{Chinese:0-2}123<br>
                        【快宝驿站】您的快递已到{Any:5-50}，取件编号{NumberAlphabet:0-10}请{Number:0-5}日内取走，逾期不取退回{Any}
                    </label>
                </div>
            </div>
            <!--<div class="row cl">
                <label class="form-label col-xs-4 col-sm-2"><span class="c-red">*</span>优先级：</label>
                <div class="formControls col-xs-8 col-sm-9">
                    <input type="number" class="number input-text" style="width:300px" name="Priority" value="2" />
                    <label style="color:red">
                        注：与文本匹配比较，值越大，优先级越高-->
                        <!--
                         与审核敏感词的优先级对比   
                        -->
                    <!--</label>
                </div>
            </div>-->
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-2">状态：</label>
                <div class="formControls col-xs-8 col-sm-9 skin-minimal">
                    <div class="radio-box">
                        <input type="radio" id="TdType-2" name="Status" value="1" checked>
                        <label for="TdType-2">启用</label>
                    </div>
                    <div class="radio-box">
                        <input name="Status" type="radio" id="TdType-1" value="0">
                        <label for="TdType-1">停用</label>
                    </div>
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-2">备注：</label>
                <div class="formControls col-xs-8 col-sm-9">
                    <textarea class="textarea" autocomplete="off" placeholder="备注" onKeyUp="$.Huitextarealength(this,200)" name="Remark" id="Remark"></textarea>
                </div>
            </div>
            <div class="row cl">
                <div class="col-xs-8 col-sm-9 col-xs-offset-4 col-sm-offset-5">
                    <input class="btn btn-primary radius" type="button" onclick="return Content_Check()" value="&nbsp;&nbsp;保存&nbsp;&nbsp;">
                </div>
            </div>
        }
    </article>
  
    <script src="~/Scripts/sweetalert.min.js"></script>
    
<script>
    function Content_Check() {
        var text = $("#Text").val();
        if (text == "") {
            layer.msg('请输入报备内容!', { icon: 5, time: 1000 });
            return false;
        }
        $.ajax({
            url: '/ContentPreparationLink/AddContent',
            data: $("#form1").serialize(),
            type: 'post',
            dataType: 'json',
            success: function (result) {
                if (result == 1) {
                    swal("添加成功", { icon: "success" }).then((su) => {
                        if (su) {
                            parent.$("#form1").submit();
                            //var index = parent.layer.getFrameIndex(window.name);
                            //parent.layer.close(index);
                        }
                    });
                } else {
                    swal("添加失败", { icon: "error" }).then((su) => {
                        if (su) {
                            parent.$("#form1").submit();
                            //var index = parent.layer.getFrameIndex(window.name);
                            //parent.layer.close(index);
                        }
                    });
                }
            }, error: function (result) {
                console.log(result);
            }
        });
    }
    $(function () {
        $('select').select2();
    });
</script>
<script
        >


    function showVarTypeDesc() {
        var html = `<div style='text-align:left;font-size:15px;'>
<b>变量类型说明</b><br>
<span style='color:#888;'>注：类型格式如 <b>{Chinese:0-2}</b>，冒号后面为"最小长度-最大长度"。如 <b>{Any:5-50}</b> 表示任意字符，长度5-50。</span>
<br><br>
1. <b>Number</b><br>
<ul style='margin-top:0;'>
  <li>匹配条件：数字字符（0-9）</li>
  <li>长度：最小长度-最大长度，如 <b>{Number:0-5}</b></li>
  <li>使用场景：年龄、数量、价格等纯数字输入</li>
  <li>示例：</li>
  <ul>
    <li>有效字符：0, 1, 2, ..., 9</li>
    <li>无效字符：a, A, @@, 中</li>
    <li>格式示例：<b>{Number:0-5}</b>，<b>{Number}</b></li>
  </ul>
</ul>
2. <b>Chinese</b><br>
<ul style='margin-top:0;'>
  <li>匹配条件：中文字符（Unicode 范围 0x4E00-0x9FA5）</li>
  <li>长度：最小长度-最大长度，如 <b>{Chinese:0-2}</b></li>
  <li>使用场景：姓名、地址、标题等中文输入</li>
  <li>示例：</li>
  <ul>
    <li>有效字符：中, 文, 测, 试</li>
    <li>无效字符：a, A, 1, @@</li>
    <li>格式示例：<b>{Chinese:0-2}</b>，<b>{Chinese}</b></li>
  </ul>
</ul>
3. <b>Alphabet</b><br>
<ul style='margin-top:0;'>
  <li>匹配条件：字母字符（大小写英文字母）</li>
  <li>长度：最小长度-最大长度，如 <b>{Alphabet:1-10}</b></li>
  <li>使用场景：用户名、英文名、密码等字母输入</li>
  <li>示例：</li>
  <ul>
    <li>有效字符：a, b, Z, X</li>
    <li>无效字符：1, @@, 中, !</li>
    <li>格式示例：<b>{Alphabet:1-10}</b>，<b>{Alphabet}</b></li>
  </ul>
</ul>
4. <b>NumberAlphabet</b><br>
<ul style='margin-top:0;'>
  <li>匹配条件：数字或字母字符</li>
  <li>长度：最小长度-最大长度，如 <b>{NumberAlphabet:0-10}</b></li>
  <li>使用场景：混合输入，如密码、验证码、ID 等</li>
  <li>示例：</li>
  <ul>
    <li>有效字符：a, B, 1, 9</li>
    <li>无效字符：@@, 中, !, +</li>
    <li>格式示例：<b>{NumberAlphabet:0-10}</b>，<b>{NumberAlphabet}</b></li>
  </ul>
</ul>
5. <b>ChineseAlphabet</b><br>
<ul style='margin-top:0;'>
  <li>匹配条件：中文字符或字母字符</li>
  <li>长度：最小长度-最大长度，如 <b>{ChineseAlphabet:1-20}</b></li>
  <li>使用场景：中英文混合输入，如昵称、产品名称等</li>
  <li>示例：</li>
  <ul>
    <li>有效字符：中, 文, a, B</li>
    <li>无效字符：1, @@, !, +</li>
    <li>格式示例：<b>{ChineseAlphabet:1-20}</b>，<b>{ChineseAlphabet}</b></li>
  </ul>
</ul>
6. <b>Any</b><br>
<ul style='margin-top:0;'>
  <li>匹配条件：任何字符</li>
  <li>长度：最小长度-最大长度，如 <b>{Any:5-50}</b></li>
  <li>使用场景：无限制的自由文本输入</li>
  <li>示例：</li>
  <ul>
    <li>所有字符均有效：a, 1, @@, 中, !</li>
    <li>格式示例：<b>{Any:5-50}</b>，<b>{Any}</b></li>
  </ul>
</ul>
</div>`;
        layer.open({
            type: 1,
            title: '变量类型说明',
            area: ['520px', '650px'],
            shadeClose: true,
            content: html
        });
    }
</script>
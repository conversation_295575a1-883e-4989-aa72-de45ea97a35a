﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using YLHT.GP.Models;
using YLHT_GP_Business.Business;

namespace YLHT.GP.Client.Controllers
{
    /// <summary>
    /// 发送统计
    /// </summary>
    public class SendStatisticsController : BaseController
    {
        ClientBLL clientBLL = new ClientBLL();
        SendStatisticsBll statisticsBll = new SendStatisticsBll();
        // GET: SendStatistics
        public ActionResult Index(QuerySendStatistics query, FormCollection fc, int currpage = 0, int pageSize = 50)
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                
                int rows;
                int pageCount;
                if (currpage <= 0)
                {
                    currpage = 1;
                }
                UserCollectModel model = new UserCollectModel();
                query.UserId = UserId;
                query.StartTime1 = query.StartTime;
                query.EndTime1 = query.EndTime;
                List<SelectListItem> Userids = new List<SelectListItem>();
                Userids.Add(new SelectListItem { Text = "------全部------", Value = "0" });
                Userids.Add(new SelectListItem { Text = User.Identity.Name, Value = UserId + "", Selected = true });
                var usermos = clientBLL.GetClientByRootUserId(UserId);
                Userids.AddRange(usermos.Select(r => new SelectListItem { Text = r.UserName + "：" + r.CustomerName, Value = r.UserId + "" }));
                ViewBag.Users = Userids;
                var f = clientBLL.GetCustomerById(UserId.ToString());
                if (f.RoleId == (int)ClientRoleEnum.IndirectCustomers)
                {
                    query.UserId = UserId;
                }
                else {
                    if (!usermos.Where(z=>z.UserId==query.UserId).Any())
                    {
                        query.UserId=UserId;
                    }
                }
                var list = statisticsBll.GetStatisticalUser(query, out model, pageSize, currpage,0, out rows, out pageCount);
                if (currpage > pageCount)
                {
                    currpage = pageCount;
                }
                if (currpage <= 0 && pageCount != 0)
                {
                    currpage = 1;
                }
                //总页数
                ViewBag.allPage = pageCount;
                //当前页
                ViewBag.curpage = currpage;
                //总条数
                ViewBag.allSize = rows;

                ViewBag.model = model == null ? new UserCollectModel() : model;
                ViewBag.list = list;
                return View(query);
            }
            return RedirectToAction("../Account/Login");
        }
    }
}
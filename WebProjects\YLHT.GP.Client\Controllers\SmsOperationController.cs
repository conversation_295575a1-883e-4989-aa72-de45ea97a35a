﻿using ExcelDataReader;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Messaging;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using System.Web.Script.Serialization;
using System.Web.UI.WebControls;
using YLHT.GP.Common;
using YLHT.GP.Models;
using YLHT_GP_Business;
using YLHT_GP_Business.Business;

namespace YLHT.GP.Client.Controllers
{
    /// <summary>
    /// 短信操作控制器
    /// </summary>
    public class SmsOperationController : BaseController
    {
        MsgTaskBll mtbll = new MsgTaskBll();
        MsgDetialsBll mdbll = new MsgDetialsBll();
        MsgMoBll mobll = new MsgMoBll();
        UserBll userBll = new UserBll();
        ClientBLL clientBLL = new ClientBLL();
        static WhiteMsisdnBLL whiteMsisdnBLL = new WhiteMsisdnBLL();
        static object obj = new object();
        static Tuple<DateTime, List<WhiteMsisdnModel>> whiteMsisdnModels;

        

        static List<WhiteMsisdnModel> WhiteMsisdnModels
        {
            get
            {
                if (whiteMsisdnModels!=null&& whiteMsisdnModels.Item1>=DateTime.Now)
                {
                    return whiteMsisdnModels.Item2;
                }
                lock (obj)
                {
                    if (whiteMsisdnModels != null && whiteMsisdnModels.Item1 >= DateTime.Now)
                    {
                        return whiteMsisdnModels.Item2;
                    }
                    List<WhiteMsisdnModel> whiteMsisdnModels1= whiteMsisdnBLL.GetWhiteMsisdnList(new WhiteMsisdnModel(), 1, 10000000, "", "", out int rows, out int pagecount);
                    if (whiteMsisdnModels1!=null)
                    {
                        if (whiteMsisdnModels1.Count>0)
                        {
                            whiteMsisdnModels = Tuple.Create(DateTime.Now.AddMinutes(5), whiteMsisdnModels1);
                            return whiteMsisdnModels.Item2;
                        }
                    }
                }
                return null;
            }
        }
        // GET: SendSms
        public ActionResult SendSms()
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                if (showmenu.Contains(10))
                {
                    var us = clientBLL.GetCustomerById(UserId.ToString());
                    return View(us);
                }
                return Redirect("/Account/Login");
            }
            return Redirect("/Account/Login");
        }
        /// <summary>
        /// 敏感词验证
        /// </summary>
        /// <returns></returns>
        public JsonResult WordVerification()
        {

            return Json(0);
        }
        [HttpPost]
        public JsonResult SendSms(SendSmsModel ssm)
        {
            try
            {
                if (Request.IsAuthenticated && Session["UserID"] != null)
                {
                    if (!showmenu.Contains(10))
                    {
                        return Json(new { Code = 00998, Description = "您的身份已过期，请重新登陆！" });
                    }
                    int.TryParse(ConfigurationManager.AppSettings["SubmitMaxCount"], out int a);
                    if (a <= 0)
                    {
                        a = 100000;
                    }
                    if (ssm.Mobile != null && ssm.Mobile.Split(",").Count() > a)
                    {
                        return Json(new { Code = 2, Description = "号码不能超过十万条" });
                    }
                    //string UPassWord = NativeMethods.DESDecrypt(Convert.ToString(CookiesHelper.GetCookie("YLHTClientInfo").Values["sPassWord"]));//NativeMethods.Md532Encrypt(NativeMethods.DESDecrypt(Convert.ToString(CookiesHelper.GetCookie("YLHTClientInfo").Values["sPassWord"]))).ToUpper();
                    //string UPassWord= Convert.ToString(CookiesHelper.GetCookie("YLHTClientInfo").Values["sPassWord"]);
                    //ClientBLL clientBLL = new ClientBLL();
                    var us = clientBLL.GetCustomerById(UserId.ToString());
                    ssm.UserId = UserId;
                    ssm.Account = User.Identity.Name;
                    ssm.ExtNumber = "";
                    ssm.PassWord = us.ToKen;
                    ssm.TaskId = "";
                    var url = ConfigurationManager.AppSettings["InternalServiceUrl"];
                    InternalService.InternalService interser = new InternalService.InternalService();
                    interser.Url = url;

                    

                    //InternalService.NResult sendResult = interser.SendSms1(ssm.UserId, ssm.Account, sercet, timespan, ssm.Mobile, ssm.Content, ssm.PlanTime, ssm.ExtNumber, ssm.TaskId);
                    InternalService.ServiceType serviceType = InternalService.ServiceType.Sms;
                    switch (ssm.SmsType)
                    {
                        case 1:
                            serviceType = InternalService.ServiceType.Sms;
                            break;
                        case 4:
                            serviceType = InternalService.ServiceType.Fms;
                            break;
                        default:
                            break;
                    }
                    var ary = ssm.Mobile.Trim().Replace("\r\n", ",").Replace(" ", ",").Replace("，", ",").Replace("\r", ",").Replace("\n", ",").Split(',', StringSplitOptions.RemoveEmptyEntries);
                    if (ary == null || ary.Length < 1)
                    {
                        return Json(new { Code = 2, Description = "号码为空！" });
                    }
                    if (!ary.Any(x => x.Length == 11))
                    {
                        return Json(new { Code = 2, Description = "号码错误！" });
                    }
                    ssm.Content = ssm.Content.Trim();
                    if (ssm.Content.Length > 2000)
                    {
                        return Json(new { Code = 2, Description = "内容太长！" });
                    }
                    int i = 0;
                    us.SUBMITPACKAGE = us.SUBMITPACKAGE > 3000 ? 3000 : (us.SUBMITPACKAGE <= 0 ? 3000 : us.SUBMITPACKAGE);
                    List<List<string>> msisdnitem = GetListGroup(ary.ToList(), us.SUBMITPACKAGE);
                    Trace.TraceInformation($"拆分后，msisdnitemCount={msisdnitem.Count}");
                    foreach (var item2 in msisdnitem)
                    {
                        var item = item2;
                        //白名单免审，1：白名单免审，0：白名单不免审
                        var whitenoaudit = ConfigurationManager.AppSettings["WhiteNoAudit"].ToString();
                        if (string.IsNullOrEmpty(whitenoaudit))
                        {
                            whitenoaudit = "0";
                        }
                        //白名单免审
                        if (whitenoaudit == "1"&& WhiteMsisdnModels != null)
                        {
                            List<string> mobiles = new List<string>();
                            
                            {
                                var d1=WhiteMsisdnModels.Where(z => z.UserId == UserId);
                                if (d1.Any())
                                {
                                    foreach (var item1 in d1)
                                    {
                                        if (item.Contains(item1.Msisdn))
                                        {
                                            item.Remove(item1.Msisdn);
                                            mobiles.Add(item1.Msisdn);
                                        }
                                    }

                                    var timespan = NativeMethods.GetTimeStamp();
                                    var sercet = NativeMethods.Md5Encrypt(ssm.PassWord + timespan);
                                    InternalService.NResult sendResult = null;
                                    if (item.Count>0)
                                    {
                                        sendResult = interser.SendSmsByServiceType(ssm.UserId, ssm.Account, sercet, timespan, item.Join(","), ssm.Content, ssm.PlanTime, ssm.ExtNumber, ssm.TaskId, serviceType);
                                        if (sendResult.StatusCode != InternalService.StatusCode.Success)
                                        {
                                            return Json(new { Code = sendResult.StatusCode, Description = sendResult.Description + "，共 " + msisdnitem.Count + " 批，成功 " + i + " 批！" });
                                        }
                                        Trace.TraceInformation($"共 {msisdnitem.Count} 批，当前第 {i} 批发送成功");
                                        i += 1;
                                    }
                                   


                                    timespan = NativeMethods.GetTimeStamp();
                                    sercet = NativeMethods.Md5Encrypt(ssm.PassWord + timespan);
                                    sendResult = interser.SendSmsByServiceType(ssm.UserId, ssm.Account, sercet, timespan, mobiles.Join(","), ssm.Content, ssm.PlanTime, ssm.ExtNumber, ssm.TaskId, serviceType);
                                    if (sendResult.StatusCode != InternalService.StatusCode.Success)
                                    {
                                        return Json(new { Code = sendResult.StatusCode, Description = sendResult.Description + "，共 " + msisdnitem.Count + " 批，成功 " + i + " 批！" });
                                    }
                                    Trace.TraceInformation($"共 {msisdnitem.Count} 批，当前第 {i} 批发送成功");
                                    i += 1;
                                }
                                else
                                {
                                    var timespan = NativeMethods.GetTimeStamp();
                                    var sercet = NativeMethods.Md5Encrypt(ssm.PassWord + timespan);
                                    InternalService.NResult sendResult = interser.SendSmsByServiceType(ssm.UserId, ssm.Account, sercet, timespan, item.Join(","), ssm.Content, ssm.PlanTime, ssm.ExtNumber, ssm.TaskId, serviceType);
                                    if (sendResult.StatusCode != InternalService.StatusCode.Success)
                                    {
                                        return Json(new { Code = sendResult.StatusCode, Description = sendResult.Description + "，共 " + msisdnitem.Count + " 批，成功 " + i + " 批！" });
                                    }
                                    Trace.TraceInformation($"共 {msisdnitem.Count} 批，当前第 {i} 批发送成功");
                                    i += 1;
                                }
                            }
                        }
                        else
                        {
                            var timespan = NativeMethods.GetTimeStamp();
                            var sercet = NativeMethods.Md5Encrypt(ssm.PassWord + timespan);
                            InternalService.NResult sendResult = interser.SendSmsByServiceType(ssm.UserId, ssm.Account, sercet, timespan, item.Join(","), ssm.Content, ssm.PlanTime, ssm.ExtNumber, ssm.TaskId, serviceType);
                            if (sendResult.StatusCode != InternalService.StatusCode.Success)
                            {
                                return Json(new { Code = sendResult.StatusCode, Description = sendResult.Description + "，共 " + msisdnitem.Count + " 批，成功 " + i + " 批！" });
                            }
                            Trace.TraceInformation($"共 {msisdnitem.Count} 批，当前第 {i} 批发送成功");
                            i += 1;
                        }
                        
                    }
                    return Json(new { Code = 1, Description = "操作完成" });
                }
                return Json(new { Code = 00998, Description = "您的身份已过期，请重新登陆！" });
            }
            catch (Exception ex)
            {
                Trace.TraceError($"提交短信异常，SendSms，ex={ex}");
            }
            return Json(new { Code = 0000, Description = "内部异常Q！" });
        }
        //手机号码过滤
        [ValidateInput(false)]
        public JsonResult TelNumFilter(string telnum)
        {
            object json;
            if (UserId > 0)
            {
                try
                {
                    var strArray = MessageUtil.ArrangeMsisdnToArray(telnum);
                    json = new JsonResult { Data = new { MsisdnCount = "" + strArray.Length + "", StatusCode = "1", NewMsisdns = "" + strArray.Join(",") + "", } };
                    return Json(json);
                }
                catch (Exception ex)
                {
                    json = new JsonResult { Data = new { StatusCode = "" + ex.Message + "", } };
                    return Json(json);
                }

            }
            json = new JsonResult { Data = new { StatusCode = "无权访问,请登录系统.", } };
            return Json(json);
        }



        [ValidateInput(false)]
        public JsonResult NdcTelNumFilter(string telnum,int qtype,int ytype,int ltype,int dtype)
        {
            object json;
            if (UserId > 0)
            {
                try
                {
                    var strArray = MessageUtil.ArrangeMsisdnToArray(telnum);

                    if (qtype==0&& ytype==0&& ltype==0&& dtype==0)
                    {
                        json = new JsonResult { Data = new { Dec = "", MsisdnCount = "" + strArray.Length + "", StatusCode = "1", NewMsisdns = "" + strArray.Join(",") + "", } };
                    }
                    else
                    {
                        


                        Dictionary<int, List<int>> d = BaseBLL.NdcMacs;

                        List<string> op0 = new List<string>();
                        List<string> op1 = new List<string>();
                        List<string> op2 = new List<string>();
                        List<string> op3 = new List<string>();

                        foreach (var item in strArray)
                        {
                            string str1 = item.Substring(0, 3);
                            string str2 = item.Substring(0, 4);
                            if (qtype == 1)
                            {
                                if (d[0].Contains(Convert.ToInt32(str1)))
                                {
                                    op0.Add(item);
                                    continue;
                                }
                                if (d[0].Contains(Convert.ToInt32(str2)))
                                {
                                    op0.Add(item);
                                    continue;
                                }
                            }
                            if (ytype == 1)
                            {
                                if (d[1].Contains(Convert.ToInt32(str1)))
                                {
                                    op1.Add(item); continue;
                                }
                                if (d[1].Contains(Convert.ToInt32(str2)))
                                {
                                    op1.Add(item); continue;
                                }
                            }
                            if (ltype == 1)
                            {
                                if (d[2].Contains(Convert.ToInt32(str1)))
                                {
                                    op2.Add(item); continue;
                                }
                                if (d[2].Contains(Convert.ToInt32(str2)))
                                {
                                    op2.Add(item); continue;
                                }
                            }
                            if (dtype == 1)
                            {



                                if (d[3].Contains(Convert.ToInt32(str1)))
                                {
                                    op3.Add(item); continue;
                                }





                                if (d[3].Contains(Convert.ToInt32(str2)))
                                {
                                    op3.Add(item); continue;
                                }
                            }

                        }

                        List<string> list = new List<string>();
                        list.AddRange(op0);
                        list.AddRange(op1);
                        list.AddRange(op2);
                        list.AddRange(op3);

                        string[] arrr = list.ToArray();

                        string dec = "";
                        if (qtype == 1)
                        {
                            dec += $"全网数量：{op0.Count},";
                        }
                        if (ytype == 1)
                        {
                            dec += $"移动数量：{op1.Count},";
                        }
                        if (ltype == 1)
                        {
                            dec += $"联通数量：{op2.Count},";
                        }
                        if (dtype == 1)
                        {
                            dec += $"电信数量：{op3.Count}";
                        }
                        json = new JsonResult { Data = new { Dec = dec, MsisdnCount = "" + arrr.Length + "", StatusCode = "1", NewMsisdns = "" + arrr.Join(",") + "", } };
                    }

                    
                    return Json(json);
                }
                catch (Exception ex)
                {
                    json = new JsonResult { Data = new { StatusCode = "" + ex.Message + "", } };
                    return Json(json);
                }

            }
            json = new JsonResult { Data = new { StatusCode = "无权访问,请登录系统.", } };
            return Json(json);
        }



        

        /// <summary>
        /// 按指定数量对List分组
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list"></param>
        /// <param name="groupNum"></param>
        /// <returns></returns>
        public static List<List<T>> GetListGroup<T>(List<T> list, int groupNum)
        {
            List<List<T>> listGroup = new List<List<T>>();
            for (int i = 0; i < list.Count(); i += groupNum)
            {
                listGroup.Add(list.Skip(i).Take(groupNum).ToList());
            }
            return listGroup;
        }
        /// <summary>
        /// 上传号码文件
        /// </summary>
        /// <returns></returns>
        public ViewResult UpLoadMobile()
        {

            return View();
        }
        private readonly string extNames = "|.xls|.xlsx|.txt|.csv|";

        [HttpPost]
        #region UpLoadMobile
        //public JsonResult UpLoadMobile(FormCollection fc)
        //{
        //    object josn;
        //    if (UserId > 0)
        //    {
        //        if (Request.Files.Count > 0)
        //        {
        //            var httpPostedFileBase = Request.Files[0];
        //            if (httpPostedFileBase != null && (Request.Files.Count > 0 && httpPostedFileBase.ContentLength > 0))
        //            {
        //                //HttpPostedFileBase file = Request.Files[0];
        //                var bytes = new byte[httpPostedFileBase.ContentLength];
        //                httpPostedFileBase.InputStream.Read(bytes, 0, bytes.Length);
        //                //文件名
        //                string fileName = httpPostedFileBase.FileName;
        //                //文件扩展名
        //                string extName = Path.GetExtension(fileName);
        //                //文件长度
        //                //int len = file.ContentLength;
        //                if (extNames.Contains("|" + extName + "|") == false)
        //                {
        //                    return Json(2);
        //                }

        //                var appSettings = System.Configuration.ConfigurationManager.AppSettings;
        //                string TempPath = appSettings["TempPath"];
        //                var dttel = new DataSet();
        //                //获取过滤后的手机号码
        //                string telnumArray = null;

        //                if (extName != null)
        //                    switch (extName.ToLower())
        //                    {
        //                        case ".txt":
        //                            telnumArray = ReadTxt(bytes);
        //                            break;
        //                        case ".xls":
        //                            dttel = FileOperation.ReadExcel(httpPostedFileBase.InputStream, ExcelVersion.Xls);
        //                            telnumArray = GetTels(dttel);
        //                            break;
        //                        case ".xlsx":
        //                            dttel = FileOperation.ReadExcel(httpPostedFileBase.InputStream, ExcelVersion.Xlsx);
        //                            telnumArray = GetTels(dttel);
        //                            break;
        //                        case ".csv":
        //                            dttel = FileOperation.ReadExcel(httpPostedFileBase.InputStream, ExcelVersion.Csv);
        //                            telnumArray = GetTels(dttel);
        //                            break;
        //                        default:
        //                            josn = new JsonResult() { Data = new { Code = "3",Message= "未能解析的文件类型,只支持：" + extNames + "类型的文件！" } };
        //                            return Json(josn);                                        
        //                    }
        //                josn = new JsonResult() { Data = new { Code = "4", Number = telnumArray } };
        //                return Json(josn);

        //            }
        //            josn = new JsonResult() { Data=new { Code=5,Message= "导入文件出错,请核对您上传的文件后再次上传！！！" } };
        //            return Json(josn);
        //        }
        //        josn= new JsonResult() { Data = new { Code = 6, Message = "请核对您上传的文件后再次上传！！！" } };
        //        return Json(josn);
        //    }
        //    josn = new JsonResult() { Data = new { Code = 7, Message = "身份已过期，请重新登陆！！" } };
        //    return Json(josn);
        //}
        #endregion
        public string UpLoadMobile(FormCollection fc)
        {
            object josn;
            if (UserId > 0)
            {
                if (Request.Files.Count > 0)
                {
                    var httpPostedFileBase = Request.Files[0];
                    if (httpPostedFileBase != null && (Request.Files.Count > 0 && httpPostedFileBase.ContentLength > 0))
                    {
                        //HttpPostedFileBase file = Request.Files[0];
                        var bytes = new byte[httpPostedFileBase.ContentLength];
                        httpPostedFileBase.InputStream.Read(bytes, 0, bytes.Length);
                        //文件名
                        string fileName = httpPostedFileBase.FileName;
                        //文件扩展名
                        string extName = Path.GetExtension(fileName);
                        //文件长度
                        //int len = file.ContentLength;
                        if (extNames.Contains("|" + extName + "|") == false)
                        {
                            return "2";
                        }

                        var appSettings = System.Configuration.ConfigurationManager.AppSettings;
                        string TempPath = appSettings["TempPath"];
                        var dttel = new DataSet();
                        //获取过滤后的手机号码
                        string telnumArray = null;

                        if (extName != null)
                            switch (extName.ToLower())
                            {
                                case ".txt":
                                    telnumArray = ReadTxt(bytes);
                                    break;
                                case ".xls":
                                    dttel = FileOperation.ReadExcel(httpPostedFileBase.InputStream, ExcelVersion.Xls);
                                    telnumArray = GetTels(dttel);
                                    break;
                                case ".xlsx":
                                    dttel = FileOperation.ReadExcel(httpPostedFileBase.InputStream, ExcelVersion.Xlsx);
                                    telnumArray = GetTels(dttel);
                                    break;
                                case ".csv":
                                    dttel = FileOperation.ReadExcel(httpPostedFileBase.InputStream, ExcelVersion.Csv);
                                    telnumArray = GetTels(dttel);
                                    break;
                                default:
                                    josn = "{\"Code\":3,\"Message\":\"未能解析的文件类型,只支持：" + extNames + "类型的文件！\"}";
                                    //josn = new JsonResult() { Data = new { Code = "3", Message = "未能解析的文件类型,只支持：" + extNames + "类型的文件！" } };
                                    return josn.ToString();
                            }
                        josn = "{\"Code\":4,\"Number\":\"" + telnumArray + "\"}";
                        //josn = new JsonResult() { Data = new { Code = "4", Number = telnumArray } }.Data;
                        return josn.ToString();

                    }
                    josn = "{\"Code\":5,\"Message\":\"导入文件出错,请核对您上传的文件后再次上传！！！\"}";
                    //josn = new JsonResult() { Data = new { Code = 5, Message = "导入文件出错,请核对您上传的文件后再次上传！！！" } }.Data;
                    return josn.ToString();
                }
                josn = "{\"Code\":6,\"Message\":\"请核对您上传的文件后再次上传！！！\"}";
                //josn = new JsonResult() { Data = new { Code = 6, Message = "请核对您上传的文件后再次上传！！！" } }.Data;
                return josn.ToString();
            }
            josn = "{\"Code\":7,\"Message\":\"身份已过期，请重新登陆！！\"}";
            //josn = new JsonResult() { Data = new { Code = 7, Message = "身份已过期，请重新登陆！！" } };
            return josn.ToString();
        }
        /// <summary>
        /// 获取号码
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        private static string GetTels(DataSet ds)
        {
            var sbtel = new StringBuilder();
            foreach (DataTable dt in ds.Tables)
            {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    sbtel.Append(dt.Rows[i][0] + ",");
                }
            }
            return sbtel.ToString().TrimEnd(',');
        }
        BookBll bookBll = new BookBll();
        /// <summary>
        /// 获取通讯录分组
        /// </summary>
        /// <returns></returns>
        public JsonResult GetClientBook()
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                return Json(bookBll.GetGroups(UserId));
            }
            return Json(1);
        }
        public JsonResult GetClientBookMsisdn(string GroupIds)
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                try
                {
                    if (!string.IsNullOrEmpty(GroupIds))
                    {
                        List<string> msisdns = new List<string>();
                        foreach (var item in GroupIds.Split(","))
                        {
                            if (!string.IsNullOrEmpty(item))
                            {
                                msisdns.AddRange(bookBll.GetBooks(new Book() { GroupId = Convert.ToInt32(item) }).Select(x => x.Msisdn));
                            }
                        }
                        return Json(msisdns.Join(","));
                    }
                }
                catch (Exception ex)
                {
                    Trace.TraceError("GetClientBookMsisdn,ex=" + ex);
                }
                return Json(2);
            }
            return Json(1);
        }
        public static string ReadTxt(byte[] bytetxtBytes)
        {
            string allphone = Encoding.Default.GetString(bytetxtBytes);
            allphone = MessageUtil.ArrangeMsisdn(allphone) + ",";
            //去掉逗号
            //allphone = allphone.Substring(0, allphone.Length - 1);
            return allphone;
        }
        /// <summary>
        /// 短信查询
        /// </summary>
        /// <returns></returns>
#if false
        public ViewResult QuerySms()
        {
            ViewBag.curpage = 1;
            return View(new MsgTask());
        }
        [HttpPost]
        public ViewResult QuerySms(MsgTask mt,int currpage = 0,int PageSize=50)
        {
            mt.UserId = UserId;
            int rows;
            int pages;
            if (currpage<=0)
            {
                currpage = 1;
            }
            var TaskList=mtbll.GetTaskList(mt, currpage, PageSize, "sendtime", "1", out rows, out pages);
            if (currpage>pages)
            {
                currpage = rows;
            }
            if (pages!=0&&currpage<=0)
            {
                currpage = 1;
            }
            ViewBag.TaskList = TaskList;
            //当前页数
            ViewBag.curpage = currpage;
            //总条数
            ViewBag.allSize = rows;
            //总页数
            ViewBag.allPage = pages;

            return View(mt);
        }
#endif
        public ViewResult QuerySms(MsgTask mt, int currpage = 0, int PageSize = 50)
        {
            mt.UserId = UserId;
            mt.ServiceType = 1;
            int rows = 0;
            int pages = 0;
            if (Request.RequestType != "GET")
            {
                if (currpage <= 0)
                {
                    currpage = 1;
                }
                if (mt.TaskId != null && mt.TaskId != "")
                {
                    mt.QueryRows = 0;
                    ViewBag.TaskList = mtbll.QueryClientTaskList(mt, currpage, PageSize, out rows, out pages);
                    if (currpage > pages)
                    {
                        currpage = rows;
                    }
                    if (pages != 0 && currpage <= 0)
                    {
                        currpage = 1;
                    }
                }
                else
                {
                    mt.QueryRows = 1;
                    ViewBag.TaskList = mtbll.QueryClientTaskList(mt, currpage, PageSize, out rows, out pages);
                }
            }

            //当前页数
            ViewBag.curpage = currpage;
            //总条数
            ViewBag.allSize = rows;
            //总页数
            ViewBag.allPage = pages;
            ViewBag.PageSize = PageSize;
            return View(mt);
        }
        /// <summary>
        /// 获取短信查询总条数和总页数
        /// </summary>
        /// <param name="mt"></param>
        /// <param name="currpage"></param>
        /// <param name="PageSize"></param>
        /// <returns></returns>
        public JsonResult QuerySmsRowPageCount(MsgTask mt, int currpage = 0, int PageSize = 50)
        {
            mt.UserId = UserId;
            int rows = 0;
            int pages = 0;
            mt.QueryRows = 2;
            if (currpage <= 0)
            {
                currpage = 1;
            }
            mtbll.QueryClientTaskList(mt, currpage, PageSize, out rows, out pages);
            if (currpage > pages)
            {
                currpage = rows;
            }
            if (pages != 0 && currpage <= 0)
            {
                currpage = 1;
            }
            return Json(new { Rows = rows, PageCount = pages });
        }

        /// <summary>
        /// 短信明细查询
        /// </summary>
        /// <returns></returns>
#if false
        public ViewResult QuerySmsDetail()
        {
            //当前页数
            ViewBag.curpage = 0;
            //总条数
            ViewBag.allSize = 0;
            //总页数
            ViewBag.allPage = 0;
            return View(new MsgDetails());
        }
        [HttpPost]
        public ViewResult QuerySmsDetail(MsgDetails md,int currpage=0,int PageSize=50)
        {
            md.UserId = UserId;
            int rows;
            int pages;
            if (currpage<=0)
            {
                currpage = 1;
            }
            var Result=mdbll.QueryClientMsgDetailsList(md, currpage, PageSize, "sendTime", "1", out rows, out pages);
            if (pages<currpage)
            {
                currpage = pages;
            }
            if (pages!=0&&currpage<=0)
            {
                currpage = 1;
            }
            ViewBag.DetailList = Result;
            //当前页数
            ViewBag.curpage = currpage;
            //总条数
            ViewBag.allSize = rows;
            //总页数
            ViewBag.allPage = pages;
            return View(md);
        }
#endif
        public ViewResult QuerySmsDetail(MsgDetails md, int currpage = 0, int PageSize = 50)
        {
            //md.RootUserId = UserId;
            var f=clientBLL.GetCustomerById(UserId.ToString());
            if (f.RoleId==(int)ClientRoleEnum.IndirectCustomers)
            {
                md.UserId = UserId;
            }
            else
            {
                md.RootUserId = UserId;
            }
            int rows = 0;
            int pages = 0;
            List<SelectListItem> Userids = new List<SelectListItem>();
            Userids.Add(new SelectListItem { Text = "------全部------", Value = "0" });
            Userids.Add(new SelectListItem { Text=User.Identity.Name,Value=UserId+"",Selected=true});
            Userids.AddRange(clientBLL.GetClientByRootUserId(UserId).Select(r=> new SelectListItem { Text = r.UserName, Value = r.UserId + "" }));
            ViewBag.Users = Userids;
            
            if (Request.RequestType != "GET")
            {
                if (currpage <= 0)
                {
                    currpage = 1;
                }
                if (md.TaskId != null && md.TaskId != "")
                {
                    md.QueryCount = 0;
                    //ViewBag.DetailList = mdbll.QueryClientMsgDetailsListClientProc(md, currpage, PageSize, out rows, out pages);
                    ViewBag.DetailList = QueryClientMsgDetailsPoxy(md, currpage, PageSize, rows, pages);
                    if (pages < currpage)
                    {
                        currpage = pages;
                    }
                    if (pages != 0 && currpage <= 0)
                    {
                        currpage = 1;
                    }
                }
                else
                {
                    md.QueryCount = 1;
                    //ViewBag.DetailList = mdbll.QueryClientMsgDetailsListClientProc(md, currpage, PageSize, out rows, out pages);
                    ViewBag.DetailList = QueryClientMsgDetailsPoxy(md, currpage, PageSize, rows, pages);
                }
            }
            else if (md.TaskId != null && md.TaskId != "")
            {
                if (currpage <= 0)
                {
                    currpage = 1;
                }
                md.QueryCount = 0;

                //ViewBag.DetailList = mdbll.QueryClientMsgDetailsListClientProc(md, currpage, PageSize, out rows, out pages);
                ViewBag.DetailList = QueryClientMsgDetailsPoxy(md, currpage, PageSize, rows, pages);

                if (pages < currpage)
                {
                    currpage = pages;
                }
                if (pages != 0 && currpage <= 0)
                {
                    currpage = 1;
                }
            }

            //当前页数
            ViewBag.curpage = currpage;
            //总条数
            ViewBag.allsize = rows;
            //总页数
            ViewBag.allPage = pages;
            ViewBag.PageSize = PageSize;
            return View(md);
        }
        private List<MsgDetails> QueryClientMsgDetailsPoxy(MsgDetails msg, int currpage, int PageSize, int rows, int pages)
        {
            string IsHidden = ConfigurationManager.AppSettings["IsHidden"].ToString();
            string hiddenuserid = ConfigurationManager.AppSettings["HiddenUserid"];
            int HiddenLength = Convert.ToInt32(ConfigurationManager.AppSettings["HiddenLength"]);
            string[] hiddenuserids = null;
            if (hiddenuserid != null)
            {
                hiddenuserids = hiddenuserid.Split(",");
            }
            if (IsHidden == "1")
            {
                var c = mdbll.QueryClientMsgDetailsListClientProc(msg, currpage, PageSize, out rows, out pages);
                if (c != null)
                {
                    return c.Select(x =>
                    {
                        if (hiddenuserids != null)
                        {
                            if (hiddenuserids.Contains(x.UserId.ToString()))
                            {
                                x.Msisdn=Regex.Replace(x.Msisdn, "(\\d{3})\\d{4}(\\d{4})", "$1****$2");
                                if (x.Text.Length > HiddenLength)
                                {
                                    string hstr = "";
                                    for (int i = 0; i < x.Text.Substring(HiddenLength, x.Text.Length - HiddenLength).Length; i++)
                                    {
                                        hstr += "*";
                                    }
                                    x.Text = x.Text.Substring(0, HiddenLength) + hstr;
                                }
                            }
                        }
                        return x;
                    }).ToList();
                }
                return c;
            }
            else
            {
                return mdbll.QueryClientMsgDetailsListClientProc(msg, currpage, PageSize, out rows, out pages);
            }
        }

        public JsonResult QuerySmsDetailRowsPage(MsgDetails md, int currpage = 0, int PageSize = 50)
        {
            //md.RootUserId = UserId;
            md.QueryCount = 2;
            var f = clientBLL.GetCustomerById(UserId.ToString());
            if (f.RoleId == (int)ClientRoleEnum.IndirectCustomers)
            {
                md.UserId = UserId;
            }
            else
            {
                md.RootUserId = UserId;
            }
            int rows = 0;
            int pages = 0;
            if (currpage <= 0)
            {
                currpage = 1;
            }
            mdbll.QueryClientMsgDetailsListClientProc(md, currpage, PageSize, out rows, out pages);
            if (pages < currpage)
            {
                currpage = pages;
            }
            if (pages != 0 && currpage <= 0)
            {
                currpage = 1;
            }
            return Json(new { Rows = rows, PageCount = pages });
        }
        [HttpPost]
        public JsonResult QuerySmsDetailReportList(MsgDetails md)
        {
            try
            {

                md.UserId = UserId;
                if (Request.IsAuthenticated && Session["UserId"] != null)
                {
                    return Json(msgbll.GetMsgUserReportList(md));
                }
                return Json(2);
            }
            catch (Exception ex)
            {
                Trace.TraceError("QuerySmsDetailReportList,ex=" + ex);
                return Json(3);
            }
        }
        ///// <summary>
        ///// 查询上行
        ///// </summary>
        ///// <returns></returns>
        //public ViewResult QueryMsgMo()
        //{
        //    List<SelectListItem> Userids = new List<SelectListItem>();
        //    Userids.Add(new SelectListItem { Text = "------全部------", Value = "0" });
        //    Userids.Add(new SelectListItem { Text = User.Identity.Name, Value = UserId + "", Selected = true });
        //    Userids.AddRange(clientBLL.GetClientByRootUserId(UserId).Select(r => new SelectListItem { Text = r.UserName, Value = r.UserId + "" }));
        //    ViewBag.Users = Userids;

        //    //当前页数
        //    ViewBag.curpage = 0;
        //    //总条数
        //    ViewBag.allSize = 0;
        //    //总页数
        //    ViewBag.allPage = 0;
        //    return View(new MsgMoModel());
        //}
        //[HttpPost]
        public ViewResult QueryMsgMo(MsgMoModel mo, int currpage=0, int PageSize = 50)
        {
            //mo.RootUserId = UserId;
            var f = clientBLL.GetCustomerById(UserId.ToString());
            if (f.RoleId == (int)ClientRoleEnum.IndirectCustomers)
            {
                mo.UserId = UserId;
            }
            else
            {
                mo.RootUserId = UserId;
            }
            int rows;
            int pages;
            if (currpage <= 0)
            {
                currpage = 1;
            }
            if (Request.RequestType != "GET")
            {
                var Result = mobll.GetMsgMoClientList(mo, currpage, "receivetime", "1", PageSize, out rows, out pages);
                if (pages < currpage)
                {
                    currpage = pages;
                }
                if (pages != 0 && currpage <= 0)
                {
                    currpage = 1;
                }//总条数
                ViewBag.allSize = rows;
                //总页数
                ViewBag.allPage = pages;

                ViewBag.MoList = Result;
            }
            else
            {
                //当前页数
                ViewBag.curpage = 0;
                //总条数
                ViewBag.allSize = 0;
                //总页数
                ViewBag.allPage = 0;
            }
            List<SelectListItem> Userids = new List<SelectListItem>();
            Userids.Add(new SelectListItem { Text = "------全部------", Value = "0" });
            Userids.Add(new SelectListItem { Text = User.Identity.Name, Value = UserId + "", Selected = true });
            Userids.AddRange(clientBLL.GetClientByRootUserId(UserId).Select(r => new SelectListItem { Text = r.UserName + "：" + r.CustomerName, Value = r.UserId + "" }));
            ViewBag.Users = Userids;
            //当前页数
            ViewBag.curpage = currpage;
            
            return View(mo);
        }
        [HttpPost]
        public JsonResult GetMtMoMsg(string id, string mobile, string channelid)
        {

            try
            {
                if (Request.IsAuthenticated && Session["UserId"] != null)
                {
                    var list = mobll.GetSmsExchangeList(int.Parse(id), int.Parse(channelid), mobile);
                    return Json(list);
                }

                //var list = smsobll.GetSmsExchangeList(userId, channelId, msisdn);
            }
            catch (Exception ex)
            {
                Trace.TraceError("SMSOperation.GetMtMoMsg,ex={0}", ex);
            }
            return Json(0);
        }

        MsgDetialsBll msgbll = new MsgDetialsBll();
        /// <summary>
        /// 导出客户端明细查询
        /// </summary>
        /// <param name="msd"></param>
        /// <returns></returns>
        public JsonResult ExportClientMsgDetialFile(MsgDetails msd)
        {
            try
            {
                if (Request.IsAuthenticated && Session["UserId"] != null)
                {
                    //msd.UserId = UserId;
                    msd.RootUserId = UserId;
                    msd.UserName = User.Identity.Name;
                    string IsHidden = ConfigurationManager.AppSettings["IsHidden"].ToString();
                    string hiddenuserid = ConfigurationManager.AppSettings["HiddenUserid"];
                    int HiddenLength = Convert.ToInt32(ConfigurationManager.AppSettings["HiddenLength"]);
                    string[] hiddenuserids = null;
                    if (hiddenuserid != null)
                    {
                        hiddenuserids = hiddenuserid.Split(",");
                    }
                    if (IsHidden=="1")
                    {
                        if (hiddenuserids!=null)
                        {
                            if (!hiddenuserids.Contains(UserId.ToString()))
                            {
                                msgbll.ExportClientUserMsgDetailOptimize(msd, BaseBLL.FileUrl, "ClientMsgDetailsUserId_" + UserId + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".csv", BaseBLL.FileDir, "客户短信明细", 1, UserId);
                            }
                        }
                        else
                        {
                            msgbll.ExportClientUserMsgDetailOptimize(msd, BaseBLL.FileUrl, "ClientMsgDetailsUserId_" + UserId + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".csv", BaseBLL.FileDir, "客户短信明细", 1, UserId);
                        }
                    }
                    else
                    {
                        msgbll.ExportClientUserMsgDetailOptimize(msd, BaseBLL.FileUrl, "ClientMsgDetailsUserId_" + UserId + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".csv", BaseBLL.FileDir, "客户短信明细", 1, UserId);
                    }
                   
                    //msgbll.ExportClientMsgDetail(msd, BaseBLL.FileUrl, "ClientMsgDetailsUserId_" + UserId + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".csv", BaseBLL.FileDir, "客户短信明细", 1, UserId);
                    
                }
                else
                {
                    return Json(2);
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"ExportClientMsgDetialFile,ex={ex}");
                return Json(2);
            }
            return Json(1);
        }
        /// <summary>
        /// 导出客户端短信查询
        /// </summary>
        /// <param name="mt"></param>
        /// <returns></returns>
        public JsonResult ExportClientMsgTaskFile(MsgTask mt)
        {
            try
            {
                if (Request.IsAuthenticated && Session["UserId"] != null)
                {
                    mt.UserId = UserId;
                    mtbll.ExportTask(mt, BaseBLL.FileUrl, "ClientMsgTaskUserId_" + UserId + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".csv", BaseBLL.FileDir, "客户短信查询", 1, UserId);
                }
                else
                {
                    return Json(2);
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"ExportClientMsgTaskFile，ex={ex}");
                return Json(2);
            }
            return Json(1);
        }
        public JsonResult ExportClientMsgMoFile(MsgMoModel msgMoModel)
        {
            try
            {
                if (Request.IsAuthenticated && Session["UserId"] != null)
                {
                    msgMoModel.UserId = UserId;
                    mobll.ExportMoDetailClient(msgMoModel, BaseBLL.FileUrl, "ClientMsgMoUserId_" + UserId + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".csv", BaseBLL.FileDir, "客户短信回复", 1, UserId);
                }
                else
                {
                    return Json(2);
                }
            }
            catch (Exception e)
            {
                Trace.TraceError($"ExportClientMsgMoFile,ex=" + e);
            }
            return Json(1);
        }


        #region 状态查询
        public ActionResult GetSmsStatusQuery()
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                return View();
            }
            return Redirect("/Account/Login");
        }
        #endregion

        #region 个性短信
        [HttpGet]
        public ActionResult IndivSms(string filename)
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                if (showmenu.Contains(10))
                {
                    var us = clientBLL.GetCustomerById(UserId.ToString());
                    if (!string.IsNullOrEmpty(filename))
                    {
                        string savePath = Server.MapPath("~/UploadFile/");
                        //如果该文件夹不存在则创建该文件夹
                        Directory.CreateDirectory(savePath);
                        var txtpath = savePath + filename;
                        ViewBag.ContentLines = System.IO.File.ReadAllLines(txtpath).Join("\n");
                        ViewBag.filename = filename;
                    }
                    return View(us);
                }
            }
            return Redirect("/Account/Login");
        }
        [HttpPost]
        public JsonResult SendIndivSms(string filename)
        {
            try
            {
                if (Request.IsAuthenticated && Session["UserId"] != null)
                {
                    if (showmenu.Contains(10))
                    {
                        if (!string.IsNullOrEmpty(filename))
                        {
                            string savePath = Server.MapPath("~/UploadFile/");
                            //如果该文件夹不存在则创建该文件夹
                            if (!Directory.Exists(savePath))
                            {
                                return Json(new { Code = "3", Description = "不支持此格式文件！" });
                            }
                            var txtpath = savePath + filename;
                            if (!System.IO.File.Exists(txtpath))
                            {
                                return Json(new { Code = "3", Description = "不支持此格式文件！" });
                            }
                            string UserId1 = UserId.ToString();
                            var us = clientBLL.GetCustomerById(UserId1);

                            // 读取所有行
                            var allLines = System.IO.File.ReadAllLines(txtpath);
                            
                            // 设置分包大小
                            us.SUBMITPACKAGE = us.SUBMITPACKAGE > 3000 ? 3000 : (us.SUBMITPACKAGE <= 0 ? 3000 : us.SUBMITPACKAGE);
                            
                            // 按分包大小分组
                            List<List<string>> batchGroups = GetListGroup(allLines.ToList(), us.SUBMITPACKAGE);
                            
                            long successTotal = 0;
                            List<string> allErrorMsisdns = new List<string>();
                            
                            Trace.TraceInformation($"个性化短信拆分后，SUBMITPACKAGE={us.SUBMITPACKAGE}，总批次数={batchGroups.Count}");

                            // 逐批处理
                            for(int batchIndex = 0; batchIndex < batchGroups.Count; batchIndex++)
                            {
                                var currentBatch = batchGroups[batchIndex];
                                
                                // 创建临时文件保存当前批次内容
                                string tempBatchFile = savePath + $"batch_{batchIndex}_{filename}";
                                System.IO.File.WriteAllLines(tempBatchFile, currentBatch);
                                
                                byte[] buffers = System.IO.File.ReadAllBytes(tempBatchFile);
                                
                                var fileEncoding = FileOperation.GetEncoding(tempBatchFile, Encoding.GetEncoding("GB2312"));
                                if (!fileEncoding.Equals(Encoding.UTF8))
                                {
                                    buffers = Encoding.Convert(fileEncoding, Encoding.UTF8, buffers);
                                }
                                buffers = Compress(buffers);

                                string Account = User.Identity.Name;
                                string ExtNumber = "";
                                string PassWord = us.ToKen;
                                string TaskId = "";

                                var url = ConfigurationManager.AppSettings["InternalServiceUrl"];
                                InternalService.InternalService interser = new InternalService.InternalService();
                                interser.Url = url;

                                InternalService.ServiceType serviceType = InternalService.ServiceType.Sms;
                                var timespan = NativeMethods.GetTimeStamp();
                                var sercet = NativeMethods.Md5Encrypt(PassWord + timespan);
                                
                                InternalService.IndivSendResult sendResult = interser.SendIndivSmsByServiceType(
                                    UserId, Account, sercet, timespan, buffers, null, ExtNumber, TaskId, serviceType);
                                    
                                // 删除临时文件
                                if(System.IO.File.Exists(tempBatchFile))
                                {
                                    System.IO.File.Delete(tempBatchFile);
                                }

                                if (sendResult.ErrorList1 != null)
                                {
                                    allErrorMsisdns.AddRange(sendResult.ErrorList1);
                                }
                                successTotal += sendResult.SuccessCount;

                                Trace.TraceInformation($"UserId:{UserId}，发送个性短信第{batchIndex + 1}/{batchGroups.Count}批，" +
                                    $"TaskId={sendResult.TaskId}，Code={sendResult.StatusCode}，Description={sendResult.Description}");

                                if (sendResult.StatusCode != InternalService.StatusCode.Success)
                                {
                                    return Json(new { 
                                        Code = sendResult.StatusCode, 
                                        Description = $"{sendResult.Description}，已发送{batchIndex + 1}批，" +
                                            $"发送成功条数：{successTotal}，错误号码条数：{allErrorMsisdns.Count}", 
                                        ErrorMsisdn = allErrorMsisdns.Join(",") 
                                    });
                                }
                            }

                            return Json(new { 
                                Code = InternalService.StatusCode.Success, 
                                Description = $"发送完成，共{batchGroups.Count}批，" +
                                    $"发送成功条数：{successTotal}，错误号码条数：{allErrorMsisdns.Count}",
                                ErrorMsisdn = allErrorMsisdns.Join(",")
                            });
                        }
                    }
                    return Json(new { Code = "2", Description = "您无此权限！" });
                }
                return Json(new { Code = "4", Description = "您的身份已失效，请重新登录！" });
            }
            catch (Exception ex)
            {
                Trace.TraceError("发送个性短信IndivSms,ex=" + ex);
            }
            return Json(new { Code = 5, Description = "内部异常" });
        }



        public JsonResult SendIndivSmsOld(string filename)
        {
            try
            {
                if (Request.IsAuthenticated && Session["UserId"] != null)
                {
                    if (showmenu.Contains(10))
                    {
                        if (!string.IsNullOrEmpty(filename))
                        {
                            string savePath = Server.MapPath("~/UploadFile/");
                            //如果该文件夹不存在则创建该文件夹
                            if (!Directory.Exists(savePath))
                            {
                                return Json(new { Code = "3", Description = "不支持此格式文件！" });
                            }
                            var txtpath = savePath + filename;
                            if (!System.IO.File.Exists(txtpath))
                            {
                                return Json(new { Code = "3", Description = "不支持此格式文件！" });
                            }
                            string UserId1 = UserId.ToString();
                            var us = clientBLL.GetCustomerById(UserId1);

                            var allline = System.IO.File.ReadAllLines(txtpath);

                            us.SUBMITPACKAGE = us.SUBMITPACKAGE > 3000 ? 3000 : (us.SUBMITPACKAGE <= 0 ? 3000 : us.SUBMITPACKAGE);
                            List<List<string>> msisdnitem = GetListGroup(allline.ToList(), us.SUBMITPACKAGE);
                            Trace.TraceInformation($"个性化短信拆分后，msisdnitemCount={msisdnitem.Count}");
                            foreach (var item2 in msisdnitem)

                            {

                                byte[] buffers = System.IO.File.ReadAllBytes(txtpath);

                                var fileEncoding = FileOperation.GetEncoding(txtpath, Encoding.GetEncoding("GB2312"));
                                //string teststr = fileEncoding.GetString(buffers).Trim();
                                if (!fileEncoding.Equals(Encoding.UTF8))
                                {
                                    buffers = Encoding.Convert(fileEncoding, Encoding.UTF8, buffers);
                                }
                                buffers = Compress(buffers);

                                string Account = User.Identity.Name;
                                string ExtNumber = "";

                                string PassWord = us.ToKen;
                                string TaskId = "";

                                var url = ConfigurationManager.AppSettings["InternalServiceUrl"];
                                InternalService.InternalService interser = new InternalService.InternalService();
                                interser.Url = url;

                                //InternalService.NResult sendResult = interser.SendSms1(ssm.UserId, ssm.Account, sercet, timespan, ssm.Mobile, ssm.Content, ssm.PlanTime, ssm.ExtNumber, ssm.TaskId);
                                InternalService.ServiceType serviceType = InternalService.ServiceType.Sms;
                                var timespan = NativeMethods.GetTimeStamp();
                                var sercet = NativeMethods.Md5Encrypt(PassWord + timespan);
                                InternalService.IndivSendResult sendResult = interser.SendIndivSmsByServiceType(UserId, Account, sercet, timespan, buffers, null, ExtNumber, TaskId, serviceType);
                                IEnumerable<string> errorMsisdn = sendResult.ErrorList1;
                                Trace.TraceInformation($"UserId:{UserId}，发送个性短信，Code={sendResult.StatusCode}，Description={sendResult.Description}，FilePath={txtpath}");
                                if (sendResult.StatusCode != InternalService.StatusCode.Success)
                                {
                                    return Json(new { Code = sendResult.StatusCode, Description = sendResult.Description + "，发送成功条数：" + sendResult.SuccessCount + "，错误号码条数：" + errorMsisdn?.Count(), ErrorMsisdn = errorMsisdn.Join(",") });
                                }
                                else
                                {
                                    return Json(new { Code = sendResult.StatusCode, Description = sendResult.Description + "，发送成功条数：" + sendResult.SuccessCount + "，错误号码条数：" + errorMsisdn?.Count(), ErrorMsisdn = errorMsisdn.Join(",") });
                                }
                            }







                        }
                    }
                    return Json(new { Code = "2", Description = "您无此权限！" });
                }
                return Json(new { Code = "4", Description = "您的身份已失效，请重新登录！" });
            }
            catch (Exception ex)
            {
                Trace.TraceInformation("发送个性短信IndivSms,ex=" + ex);
            }
            return Json(new { Code = 5, Description = "内部异常" });
        }
        /// <summary>
        /// 导入个性短信
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult ImportIndivSms()
        {
            try
            {
                if (Request.IsAuthenticated && Session["UserId"] != null)
                {

                    if (UserId > 0)
                    {
                        if (Request.Files.Count > 0)
                        {
                            var httpPostedFileBase = Request.Files[0];
                            if (httpPostedFileBase != null && (Request.Files.Count > 0 && httpPostedFileBase.ContentLength > 0))
                            {
                                string Content = Request["Content"];
                                string PhonePosition = Request["PhonePosition"];
                                //HttpPostedFileBase file = Request.Files[0];
                                var bytes = new byte[httpPostedFileBase.ContentLength];
                                httpPostedFileBase.InputStream.Read(bytes, 0, bytes.Length);
                                //文件名
                                string fileName = httpPostedFileBase.FileName;
                                //文件扩展名
                                string extName = Path.GetExtension(fileName);
                                string SaveFilePath1 = UserId + "--IndivSmsFile--" + Guid.NewGuid().ToString("N") + DateTime.Now.ToString("yyyyMMddHHmmssffff") + ".txt";
                                //文件长度
                                //int len = file.ContentLength;
                                if (extNames.Contains("|" + extName + "|") == false)
                                {
                                    return Json(3);
                                }
                                //获取文件路径
                                string savePath = Server.MapPath("~/UploadFile");
                                //如果该文件夹不存在则创建该文件夹
                                Directory.CreateDirectory(savePath);
                                string SaveFilePath = savePath + "//" + SaveFilePath1;
                                var dttel = new DataSet();
                                if (extName != null)
                                    switch (extName.ToLower())
                                    {
                                        case ".xls":
                                            dttel = FileOperation.ReadExcel(httpPostedFileBase.InputStream, ExcelVersion.Xls);
                                            break;
                                        case ".xlsx":
                                            dttel = FileOperation.ReadExcel(httpPostedFileBase.InputStream, ExcelVersion.Xlsx);
                                            break;
                                        default:
                                            return Json(3);
                                    }
                                int phoneCount = 0;
                                for (int i = 0; i < dttel.Tables.Count; i++)
                                {
                                    phoneCount += dttel.Tables[i].Rows.Count;
                                }
                                int.TryParse(ConfigurationManager.AppSettings["IndivSmsUploadCount"].ToString(), out int maxcount);
                                if (maxcount <= 0)
                                {
                                    maxcount = 10000;
                                }
                                //超10000字
                                if (phoneCount > maxcount)
                                {
                                    return Json(4);
                                }
                                SaveIndivFile(dttel, PhonePosition, Content, SaveFilePath);
                                return Json(SaveFilePath1);
                            }
                            return Json(5);
                        }
                        return Json(6);
                    }
                    return Json(7);
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError("ImportIndivSms，ex=" + ex);
                return Json(8);
            }
            return Json(7);
        }
        private /*ConvertEnum*/ void SaveIndivFile(DataSet ds, string phonePosition, string Content, string SaveFilePath)
        {
            //ConvertEnum b = ConvertEnum.Faild;
            //保存文件（手机，内容）
            var SaveTxt = new StringBuilder();
            StreamWriter sw = new StreamWriter(SaveFilePath);

            for (int i = 0; i < ds.Tables.Count; i++)
            {
                for (int j = 0; j < ds.Tables[i].Rows.Count; j++)
                {
                    string contentSb = Content;
                    //循环18列，替换内容变量
                    for (int k = 0; k < 18; k++)
                    {
                        //获取列名，字符转换，A、B、C
                        string p = char.ConvertFromUtf32('A' + k);
                        //替换对应的列数据
                        contentSb = contentSb.Replace("[(" + p + ")]", ds.Tables[i].Columns.Count <= k ? "" : ds.Tables[i].Rows[j][k].ToString());
                    }
                    string msisdn = ds.Tables[i].Rows[j][Convert.ToInt32(phonePosition)].ToString();
                    if (MessageUtil.IsPhoneNumber(msisdn) == false)
                    {
                        continue;
                    }
                    SaveTxt.Append(msisdn + " ");
                    if (Content.Length > 2000)
                    {
                        sw.Close();
                        //b = ConvertEnum.LongLength;
                        return;
                    }
                    SaveTxt.Append(contentSb);
                    sw.WriteLine(SaveTxt.ToString());
                    SaveTxt.Clear();
                }
            }
            sw.Flush();
            sw.Close();
            //return b;
        }
        #endregion
        /// <summary> 
        /// 压缩数据 
        /// </summary> 
        /// <param name="data"></param> 
        /// <returns></returns> 
        private static byte[] Compress(byte[] data)
        {
            var ms = new MemoryStream();
            var stream = new GZipStream(ms, CompressionMode.Compress, true);
            stream.Write(data, 0, data.Length);
            stream.Close();
            stream.Dispose();
            //必须把stream流关闭才能返回ms流数据,不然数据会不完整 
            //并且解压缩方法stream.Read(buffer, 0, buffer.Length)时会返回0 
            byte[] bData = ms.ToArray();
            ms.Close();
            ms.Dispose();
            return bData;
        }

        /// <summary>
        /// 压缩指定路径的文件,并删除原文件
        /// </summary>
        /// <param name="path"></param>
        private static string Compress(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
            {
                return "";
            }
            if (!System.IO.File.Exists(path))
            {
                return "";
            }
            using (FileStream fs = new FileStream(path, FileMode.Open))
            {
                if (fs.Length <= 0)
                {
                    fs.Close();
                    fs.Dispose();
                    System.IO.File.Delete(path);
                    return "";
                }
                using (FileStream outfile = System.IO.File.Create(path + ".zip"))
                {
                    using (GZipStream ComPress = new GZipStream(outfile, CompressionMode.Compress))
                    {
                        fs.CopyTo(ComPress);
                    }
                }
            }
            System.IO.File.Delete(path);
            return Path.GetFileName(path) + ".zip";
        }
    }
    public class SendSmsModel
    {
        public int UserId { get; set; }
        public string Account { get; set; }
        public string PassWord { get; set; }
        public string Mobile { get; set; }
        public string Content { get; set; }

        public DateTime? PlanTime { get; set; }
        public string ExtNumber { get; set; }
        public string TaskId { get; set; }
        public int SmsType { get; set; }
    }
    public enum ConvertEnum
    {
        //内容超长度
        LongLength = 9,
        Faild = 10,
        Success = 11
    }
}
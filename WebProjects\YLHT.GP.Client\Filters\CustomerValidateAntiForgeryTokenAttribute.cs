﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Web;
using System.Web.Helpers;
using System.Web.Mvc;

namespace YLHT.GP.Client.Filters
{
    /// <summary>  
    /// 自定义AntiForgeryToken校验，过滤用户  
    /// </summary>  
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false, Inherited = true)]
    public class CustomerValidateAntiForgeryTokenAttribute : FilterAttribute, IAuthorizationFilter
    {
        internal Action ValidateAction
        {
            get;
            private set;
        }

        public CustomerValidateAntiForgeryTokenAttribute() : this(new Action(AntiForgery.Validate))
        {
        }

        internal CustomerValidateAntiForgeryTokenAttribute(Action validateAction)
        {
            this.ValidateAction = validateAction;
        }

        public void OnAuthorization(AuthorizationContext filterContext)
        {
            if (filterContext == null)
            {
                throw new ArgumentNullException("filterContext");
            }
            try
            {

                this.ValidateAction();
            }
            catch (Exception ex)
            {
                Trace.TraceError(ex.ToString());
                //string url = filterContext.HttpContext.Request.UrlReferrer.ToString();  
                //EventLog.WriteLog(url);  
                filterContext.Result = new RedirectResult("/Account/Login", true);
                return;
                //throw;  
            }
        }
    }


}
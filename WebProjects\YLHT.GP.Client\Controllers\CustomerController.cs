﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Messaging;
using System.Threading;
using System.Web;
using System.Web.Mvc;
using YLHT.GP.Common;
using YLHT.GP.Models;
using YLHT_GP_Business;
using YLHT_GP_Business.Business;

namespace YLHT.GP.Client.Controllers
{
    /// <summary>
    /// 客户
    /// </summary>
    public class CustomerController : BaseController
    {
        NavBll nbl = new NavBll();
        ClientBLL cbll = new ClientBLL();
        SalesManBll salesManBll = new SalesManBll();
        private static string QueuePath
        {
            get
            {
                return ConfigurationManager.AppSettings["ReportQueuePath"];
            }
        }
        // GET: Customer
        public ActionResult Index(ClientModel cm, int currpage = 0, int PageSize = 50)
        {
            int allrows;
            int allpage;
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {

                if (currpage <= 0)
                {
                    currpage = 1;
                }
                List<SelectListItem> channelPro = new List<SelectListItem>();
                channelPro.Add(new SelectListItem { Text = "全部", Value = 0 + "" });
                channelPro.AddRange(cbll.GetProductList().Select(s => new SelectListItem { Text = s.ProductName, Value = s.ProductId + "" }).ToList());
                ViewBag.pdlist = channelPro;
                //ViewBag.clist=cbll.GetClientList(cm,"", currpage, pageSize,"addtime","1",out myRows,out myPageCount);
                if (cm.ParentUserId == 0)
                {
                    cm.ParentUserId = UserId;
                }
                ViewBag.clist = cbll.GetClientList(cm, "1", "1", currpage, PageSize, "1", "AddTime", out allrows, out allpage);
                if (currpage > allrows)
                {
                    currpage = allrows;
                }
                if (allpage != 0 && currpage <= 0)
                {
                    currpage = 1;
                }
                //总页数
                ViewBag.allPage = allpage;
                //当前页
                ViewBag.curpage = currpage;
                //总条数
                ViewBag.allSize = allrows;
                ViewBag.showmenu = showmenu;
                return View(cm);
            }
            return RedirectToAction("../Account/Login");

        }
        /// <summary>
        /// 添加间接客户
        /// </summary>
        /// <returns></returns>
        public ActionResult AddCustomer()
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                if (UserId > 0)
                {
                    RoleClientEdit("2", "");
                    ViewBag.showmenu = showmenu;
                    return View(cbll.GetCustomerById(UserId.ToString()));
                }
            }
            return RedirectToAction("Login", "Account");
        }
        /// <summary>
        /// 添加间接客户
        /// </summary>
        /// <param name="cm"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult AddCustomer(ClientModel cm, FormCollection fc)
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                var option = fc["interfaceoption"];
                string[] arroption = option.Split(',');
                int optionvalues = 0;
                foreach (var item in arroption)
                {
                    optionvalues += int.Parse(item);
                }
                //与父级账号同步设置
                ClientModel cm1 = cbll.GetCustomerById(UserId.ToString());
                cm.NumberLimit = cm1.NumberLimit;
                cm.DayLimit = cm1.DayLimit;
                cm.ParentUserId = cm1.UserId;
                cm.RootUserId = cm1.RootUserId;
                cm.ProductMmsId = cm1.ProductMmsId;
                cm.ProductSmsId = cm1.ProductSmsId;
                cm.Price = cm1.Price;
                cm.Price1 = cm1.Price1;
                cm.Status = 1;
                cm.Options = cm1.Options;
                string pwd = cm.PassWord;
                cm.PassWord = NativeMethods.Md532Encrypt(pwd);
                cm.ToKen = NativeMethods.Md516Encrypt(pwd);
                cm.ExtNumber = cbll.GetIndirectExtNumber(cm1.UserId.ToString());
                cm.RoleId = 2;
                cm.RootUserId = cm1.RootUserId;
                cm.FeeType = cm1.FeeType;
                cm.ChargeType = cm1.ChargeType;
                cm.AccessOrigin = (AccessOrigin)optionvalues;
                cm.ReceiveMode = cm1.ReceiveMode;
                var sa = salesManBll.GetSalesManByUserId(UserId.ToString());
                cm.SalesId = (sa == null ? 0 : sa.Id);
                cm.Rate = cm1.Rate;
                cm.AuditNumber = cm1.AuditNumber;
                cm.ConnectCount = cm1.ConnectCount;
                cm.CompanyId = cm1.CompanyId;
                cm.ContentDayLimit = cm1.ContentDayLimit;




                cm.ContentDayLimitEndTime = cm1.ContentDayLimitEndTime;
                cm.ContentDayLimitStartTime = cm1.ContentDayLimitStartTime;
                cm.SubmitLimitEndTime = cm1.SubmitLimitEndTime;
                cm.SubmitLimitStartTime = cm1.SubmitLimitStartTime;
                cm.MaxSubmitLimit = cm1.MaxSubmitLimit;
                cm.IsContentMsisdn = cm1.IsContentMsisdn;
                cm.ContentMsisdnEndTime = cm1.ContentMsisdnEndTime;
                cm.ContentMsisdnStartTime = cm1.ContentMsisdnStartTime;


                if (cbll.AddIndirectClient(cm, User.Identity.Name))
                {
                    int userid = UserId;
                    string username = User.Identity.Name;
                    //通知
                    Notify(HttpNotifyMethod._DataService.ResetUserInfo, 0, userid, username);
                    var menus = Request.Form["menu"];//.ToString();
                    if (menus != null)
                    {
                        menus = menus.ToString();
                    }
                    nbl.SetClientUserMenu(cm.UserId.ToString(), menus);
                    //if (cm.ReceiveMode == 3)
                    //{
                    //    string path = "";
                    //    if (string.IsNullOrEmpty(QueuePath))
                    //    {
                    //        path = $@".\private$\YLHT_GP_UserTcp_{cm.UserId}";
                    //    }
                    //    else
                    //    {
                    //        path = QueuePath + "_" + cm.UserId;
                    //    }
                    //    Thread thread = new Thread(() => BaseBLL.GetMessageQueue(path, new XmlMessageFormatter(new Type[] { typeof(Common.UserDeliver) })));
                    //    thread.IsBackground = true;
                    //    thread.Start();
                    //}
                    return Json(1);
                }

                //string menu=fc["menu"];
                //RoleClientEdit("2");
                //ViewBag.showmenu = showmenu;

                return Json(0);
            }
            return Json(0);
        }

        [HttpPost]
        public JsonResult GetCustomerName(string name)
        {
            JsonResult ret = new JsonResult();
            validbool vd = new validbool() { valid = false };
            string message = "";
            if (name != null && name != "")
            {
                if (name.Contains("："))
                {
                    if (name.Split("：")[0] == name.Split("：")[1])
                    {
                        message = "1";
                        vd.valid = true;
                        ret.Data = vd;
                        return ret;
                    }
                }
                if (cbll.GetCustUserNameByName(name) == null)
                {
                    message = "1";
                    vd.valid = true;
                    ret.Data = vd;
                    return ret;
                }
            }
            ret.Data = vd;
            return ret;
        }
        /// <summary>
        /// 客户端角色设置权限
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ActionResult RoleClientEdit(string id, string userid)
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                var one = nbl.PermissionNaviList("1", "0", "2");//获取一级菜单列表

                //foreach (var item in one)
                //{
                //    item.isRoleMenuChecked = nbl.RoleMenuChecked(id, item.MENUID + "", "2");//判断当前角色是否拥有此权限
                //    item.SubMenus = nbl.PermissionNaviList("2", item.MENUID + "", "2");//获取二级菜单
                //    foreach (var item1 in item.SubMenus)
                //    {
                //        item1.isRoleMenuChecked = nbl.RoleMenuChecked(id, item1.MENUID + "", "2");
                //    }
                //}
                if (userid != null && userid != "")
                {
                    if (nbl.IsIndirectCustomer(userid))
                    {
                        var userMenus = nbl.GetClientUserMenus(userid);
                        foreach (var item in one)
                        {
                            var c = userMenus.Where(d => d.MENUID == item.MENUID);
                            if (c.Count() > 0)
                            {
                                item.isRoleMenuChecked = true;
                            }
                            else
                            {
                                item.isRoleMenuChecked = false;
                            }

                            item.SubMenus = nbl.PermissionNaviList("2", item.MENUID + "", "2");//获取二级菜单
                            foreach (var item1 in item.SubMenus)
                            {
                                var x = userMenus.Where(d => d.MENUID == item1.MENUID);
                                if (x.Count() > 0)
                                {
                                    item1.isRoleMenuChecked = true;
                                }
                                else
                                {
                                    item1.isRoleMenuChecked = false;
                                }
                            }

                        }
                    }
                }
                else
                {
                    foreach (var item in one)
                    {
                        item.isRoleMenuChecked = nbl.RoleMenuChecked(id, item.MENUID + "", "2");//判断当前角色是否拥有此权限
                        item.SubMenus = nbl.PermissionNaviList("2", item.MENUID + "", "2");//获取二级菜单
                        foreach (var item1 in item.SubMenus)
                        {
                            item1.isRoleMenuChecked = nbl.RoleMenuChecked(id, item1.MENUID + "", "2");
                        }
                    }
                }
                ViewBag.Menus = one;
                ViewBag.RoleId = id;
                return null;
            }
            return RedirectToAction("Login", "Account");
        }
        /// <summary>
        /// 修改间接客户
        /// </summary>
        /// <returns></returns>
        public ActionResult UpdateIndirectClient(string id)
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                RoleClientEdit("2", id);
                ViewBag.showmenu = showmenu;
                var cu=cbll.GetCustomerById(id);
                return View(cu);
            }
            return RedirectToAction("Login", "Account");
        }
        /// <summary>
        /// 修改间接客户
        /// </summary>
        /// <param name="cm"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult UpdateIndirectClient(ClientModel cm, FormCollection fc)
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                var option = fc["interfaceoption"];
                string[] arroption = option.Split(',');
                int optionvalues = 0;
                foreach (var item in arroption)
                {
                    optionvalues += int.Parse(item);
                }
                ClientModel cm1 = cbll.GetCustomerById(UserId.ToString());
                cm.ParentUserId = cm1.UserId;
                cm.ProductMmsId = cm1.ProductMmsId;
                cm.ProductSmsId = cm1.ProductSmsId;
                cm.Price = cm1.Price;
                cm.Price1 = cm1.Price1;
                cm.DayLimit = cm1.DayLimit;
                cm.Status = 1;
                cm.Options = cm1.Options;
                cm.FeeType = cm1.FeeType;
                //cm.ChargeType = cm1.ChargeType;
                cm.AuditNumber = cm1.AuditNumber;
                cm.ContentDayLimit= cm1.ContentDayLimit;
                cm.ContentDayLimitEndTime= cm1.ContentDayLimitEndTime;
                cm.ContentDayLimitStartTime= cm1.ContentDayLimitStartTime;
                cm.SubmitLimitEndTime = cm1.SubmitLimitEndTime;
                cm.SubmitLimitStartTime = cm1.SubmitLimitStartTime;
                cm.MaxSubmitLimit = cm1.MaxSubmitLimit;
                cm.IsContentMsisdn= cm1.IsContentMsisdn;
                cm.ContentMsisdnEndTime= cm1.ContentMsisdnEndTime;
                cm.ContentMsisdnStartTime= cm1.ContentMsisdnStartTime;
                //if (cm.PassWord!=null)
                //{
                //    cm.PassWord = NativeMethods.Md532Encrypt(cm.PassWord);

                //}
                //else
                //{
                //    cm.PassWord = fc["oldpassword"];

                //}
                //if (cm.ToKen!=null)
                //{
                //    cm.ToKen = NativeMethods.Md516Encrypt(cm.ToKen);
                //}
                //else
                //{
                //    cm.ToKen = fc["oldtoken"];
                //}
                if (cm.PassWord != null && cm.PassWord != "")
                {
                    cm.PassWord = NativeMethods.Md532Encrypt(cm.PassWord);
                }
                if (cm.ToKen != null && cm.ToKen != "")
                {
                    cm.ToKen = NativeMethods.Md516Encrypt(cm.ToKen);
                }
                //cm.ExtNumber = cbll.GetIndirectExtNumber(cm1.UserId.ToString());
                cm.RoleId = 2;
                cm.RootUserId = cm1.RootUserId;
                cm.CompanyId = cm1.CompanyId;
                cm.ContentDayLimit = cm1.ContentDayLimit;
                cm.AccessOrigin = (AccessOrigin)optionvalues;
                var menus = Request.Form["menu"];//.ToString();
                if (menus != null)
                {
                    menus = menus.ToString();
                }
                nbl.SetClientUserMenu(cm.UserId.ToString(), menus);
                if (cbll.UpdateIndirectClient(cm, User.Identity.Name))
                {
                    //通知
                    int userid = UserId;
                    string username = User.Identity.Name;
                    Notify(HttpNotifyMethod._DataService.ResetUserInfo, cm.UserId,userid,username);


                    //if (cm.ReceiveMode == 3)
                    //{
                    //    string path = "";
                    //    if (string.IsNullOrEmpty(QueuePath))
                    //    {
                    //        path = $@".\private$\YLHT_GP_UserTcp_{cm.UserId}";
                    //    }
                    //    else
                    //    {
                    //        path = QueuePath + "_" + cm.UserId;
                    //    }
                    //    Thread thread = new Thread(() => BaseBLL.GetMessageQueue(path, new XmlMessageFormatter(new Type[] { typeof(Common.UserDeliver) })));
                    //    thread.IsBackground = true;
                    //    thread.Start();
                    //}
                    return Json(1);
                }
            }
            return Json(0);
        }
        /// <summary>
        /// 删除间接客户
        /// </summary>
        /// <param name="userid"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult DeleteIndirectClient(string[] userid)
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                int.Parse(userid[0]);
                if (userid != null)
                {
                    if (cbll.DeleteCustomer(userid, User.Identity.Name))
                    {
                        //通知
                        int useridd = UserId;
                        string username = User.Identity.Name;
                        Notify(HttpNotifyMethod._DataService.ResetUserInfo, int.Parse(userid[0]),useridd,username);
                        return Json(1);
                    }
                }
                return Json(0);
            }
            return Json(2);
        }
        public ActionResult ClientRecharge(int id)
        {
            ViewBag.id = id;
            return View(cbll.GetCustomerById(id.ToString()));
        }
        [HttpPost]
        public JsonResult ClientRecharge(string isrecharge,string value,string remark,string amounttype, int Userid)
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                try
                {
                    string UValue = value;
                    string URemark = remark;
                    string UAmountType = amounttype;
                    string Isrecharge = isrecharge;
                    
                    string UPassWord = cbll.GetCustomerById(this.UserId.ToString()).PassWord;
                    //string UPassWord = NativeMethods.Md532Encrypt(NativeMethods.DESDecrypt(Convert.ToString(CookiesHelper.GetCookie("YLHTManagerInfo").Values["sPassWord"])));

                    var burl = ConfigurationManager.AppSettings["BillingServiceUrl"];
                    var timeStamp = NativeMethods.GetTimeStamp();
                    var md5Secret = NativeMethods.Md5Encrypt(UPassWord + timeStamp).ToLower();
                    BillingService.AmountParam aparam = new BillingService.AmountParam
                    {
                        Id = this.UserId,
                        IsServer = false,
                        UserId = Userid,
                        Account = User.Identity.Name,
                        Secret = md5Secret,
                        TimeStamp = timeStamp,
                        Value = decimal.Parse(UValue),
                        AmountType = (BillingService.AmountType)int.Parse(UAmountType),
                        Remark = URemark
                    };
                    BillingService.BillingService b = new BillingService.BillingService();
                    b.Url = burl;
                    //充值
                    if (Isrecharge=="1")
                    {
                        var result = b.ClientRecharge(aparam);

                        if (result.Code == 1)
                        {
                            Trace.TraceInformation($"客户端，用户：{User.Identity.Name}给Userid={UserId}，充值成功！Code={result.Code}，充值{(UAmountType == "4" ? "金额" : "条数")}：{UValue}，额度为：{result.Value}");
                            return Json(new { Code = result.Code, Value = result.Value, Isrecharge = true });
                        }
                        Trace.TraceInformation($"客户端，用户：{User.Identity.Name}给Userid={UserId}，充值失败！失败原因：{result.Description},Code={result.Code}，充值{(UAmountType == "4" ? "金额" : "条数")}：{UValue}，额度为：{result.Value}");
                        return Json(new { Code = result.Code, Description = result.Description, Isrecharge = true });
                    }
                    //扣费
                    var result1 = b.ClientDeduct(aparam);
                    if (result1.Code == 1)
                    {
                        Trace.TraceInformation($"客户端，用户：{User.Identity.Name}给Userid={UserId}，扣费成功！Code={result1.Code}，扣除{(UAmountType == "4" ? "金额" : "条数")}：{UValue}，额度为：{result1.Value}");
                        return Json(new { Code = result1.Code, Value = result1.Value, Isrecharge = false });
                    }
                    Trace.TraceInformation($"客户端，用户：{User.Identity.Name}给Userid={UserId}，扣费失败！失败原因：{result1.Description},Code={result1.Code}，扣除{(UAmountType == "4" ? "金额" : "条数")}：{UValue}，额度为：{result1.Value}");
                    return Json(new { Code = result1.Code, Description = result1.Description, Isrecharge = false });

                }
                catch (Exception ex)
                {
                    Trace.TraceError($"客户端，用户：{User.Identity.Name}给Userid={UserId}，充值异常！ex={ex}");
                }
            }
            return Json(new { Code = -1, Description = "服务端异常！" });
        }
    }
    internal class validbool
    {
        public bool valid { get; set; }
    }
}
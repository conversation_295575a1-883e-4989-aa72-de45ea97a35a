﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Configuration;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Mvc;
using System.Xml;
using System.Xml.Serialization;
using YLHT.GP.Client.Models;
using YLHT.GP.Common;
using YLHT.GP.Models;
using YLHT_GP_Business.Business;

namespace YLHT.GP.Client.Controllers
{
    public class MmsOperationController : BaseController
    {
        MsgTaskBll mtbll = new MsgTaskBll();
        ClientBLL clientBLL = new ClientBLL();
        MsgDetialsBll mdbll = new MsgDetialsBll();
        // GET: MmsOperation
        public ActionResult Index()
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                return View();
            }
            return RedirectToAction("/Account/Login");
        }
        private readonly string extNames="|.tms|";
        /// <summary>
        /// 发送彩信
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public ActionResult SendMms()
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                var us = clientBLL.GetCustomerById(UserId.ToString());
                return View(us);
            }
            return RedirectToAction("/Account/Login");
        }
        [HttpPost]
        public JsonResult SendMms(SendMmsModel model)
        {
            string msg = "{\"Code\":\"1\",\"msg\":\"发送成功\"}";
            if (Request.Files.Count > 0)
            {
                var httpPostedFileBase = Request.Files[0];
                if (httpPostedFileBase != null && (Request.Files.Count > 0 && httpPostedFileBase.ContentLength > 0))
                {
                    string fileName = httpPostedFileBase.FileName;
                    string extName = Path.GetExtension(fileName);
                    if (extNames.Contains("|" + extName + "|") == false)
                    {
                        msg = "{\"Code\":\"2\",\"msg\":\"扩展名不正确，只支持tms文件\"}";
                        return Json(msg);
                    }
                    var bytes = new byte[httpPostedFileBase.ContentLength];
                    httpPostedFileBase.InputStream.Read(bytes, 0, bytes.Length);

                    var us = clientBLL.GetCustomerById(UserId.ToString());
                    model.UserId = UserId;
                    model.Account = User.Identity.Name;
                    model.PassWord = us.ToKen;
                    model.TaskId = "";
                    model.Content = Convert.ToBase64String(bytes);
                    var url = ConfigurationManager.AppSettings["InternalServiceUrl"];
                    InternalService.InternalService interser = new InternalService.InternalService();
                    interser.Url = url;
                    var timespan = NativeMethods.GetTimeStamp();
                    var sercet = NativeMethods.Md5Encrypt(model.PassWord + timespan);
                    InternalService.NResult sendResult = interser.SendMms(model.UserId, model.Account, sercet, timespan, model.Mobile, model.Title, model.Content, model.PlanTime, model.ExtNumber, model.TaskId);
                    if (sendResult.StatusCode == InternalService.StatusCode.Success)
                    {
                        return Json(msg);
                    }
                    else
                    {
                        msg = "{\"Code\":\"" + sendResult.StatusCode + "\",\"msg\":\"" + sendResult.Description + "\"}";
                        return Json(msg);
                    }
                }
            }
            else
            {
                msg = "{\"Code\":\"2\",\"msg\":\"tms文件为空！\"}";
                return Json(msg);
            }
            return Json(msg);
        }
        /// <summary>
        /// 动态彩信
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public ActionResult SendDynamicMms()
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                var us = clientBLL.GetCustomerById(UserId.ToString());
                return View(us);
            }
            return RedirectToAction("/Account/Login");
        }
        [HttpPost]
        public JsonResult SendDynamicMms(string dynamicMmsStr, string mmsStr)
        {
            
            string msg = "{\"Code\":\"1\",\"msg\":\"发送成功\"}";
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                DynamicMms.Frame[] dynamicMms = JsonConvert.DeserializeObject<DynamicMms.Frame[]>(dynamicMmsStr);
                if (dynamicMms != null)
                {
                    mmsStr = HttpUtility.UrlDecode(mmsStr);
                    SendMmsModel mms = JsonConvert.DeserializeObject<SendMmsModel>(mmsStr);
                    //if (!ModelState.IsValid)
                    //{
                    //    msg = "{\"Code\":\"2\",\"msg\":\"手机号或彩信标题为空！\"}";
                    //    return Json(msg);
                    //}
                    if (mms == null || string.IsNullOrEmpty(mms.Title) || string.IsNullOrEmpty(mms.Mobile))
                    {
                        msg = "{\"Code\":\"2\",\"msg\":\"手机号或彩信标题为空！\"}";
                        return Json(msg);
                    }
                    if (dynamicMms.Count() > 0)
                    {
                        List<byte> tmsBytes = new List<byte>();
                        //彩信文件按帧分组
                        var dynamicMmsGroup = dynamicMms.GroupBy(d => d.Zid);
                        //smil文件上半部分html
                        StringBuilder smilTop = new StringBuilder();
                        smilTop.Append(@"<smil> 
                                            <head> 
                                            <meta name='title' content=''/> 
                                            <layout> 
                                            <root-layout title='' width='240' height='320' background-color='#FFFFFF' color='#FF000008' font-size='8' font-family='Tahoma' font-style='' font-variant='' font-weight=''/> ");
                        StringBuilder smilAfter = new StringBuilder();
                        smilAfter.Append(@"</layout> 
                                        </head>  
                                        <body> ");
                        for (int i = 0; i < dynamicMmsGroup.Count(); i++)
                        {
                            var FrameId = dynamicMmsGroup.ElementAt(i);//帧Id
                            smilAfter.Append($@"<par dur='{FrameId.ElementAt(0).ZTime}s'>");
                            for (int k = 0; k < FrameId.Count(); k++)
                            {
                                var mmsInfo = FrameId.ElementAt(k);
                                string path = Server.MapPath($"/MmsUserFrameElement/{UserId}_{mmsInfo.Zid}/");
                                FileInfo fileInfo = new FileInfo(path + mmsInfo.FileName);
                                var id = mmsInfo.FileName.Split(".")[0];
                                var FileName = mmsInfo.FileName;
                                if (fileInfo.Exists)
                                {
                                    switch (mmsInfo.FileName.Split(".")[1].ToLower())
                                    {
                                        case "img":
                                        case "png":
                                        case "jpg":
                                        case "gif":
                                            smilTop.Append($@"<region left='0%' top='0%' width='100%' height='100%' id='{id}' z-index='0' fit='meet'/>");
                                            smilAfter.Append($@"<img region='{id}' src='{FileName}'/>");
                                            break;
                                        case "txt":
                                            smilTop.Append($@"<region left='0%' top='0%' width='100%' height='100%' id='{id}' z-index='0' fit='meet'/>");
                                            smilAfter.Append($@"<text region='{id}' src='{FileName}'/>");
                                            break;
                                        case "bmp":
                                            break;
                                        case "xml":
                                            break;
                                        case "wav":
                                            break;
                                        case "mid":
                                            break;
                                        case "amr":
                                            break;
                                        case "mp3":
                                            break;
                                        case "mp4":
                                            smilTop.Append($@"<region left='0%' top='0%' width='100%' height='100%' id='{id}' z-index='0'/>");
                                            smilAfter.Append($@"<video region='{id}' src='{FileName}'/>");
                                            break;
                                        default:
                                            break;
                                    }
                                    tmsBytes.AddRange(GetTmsByte(path + mmsInfo.FileName, mmsInfo.FileName));
                                }
                            }
                            smilAfter.Append($@"</par>");
                        }

                        smilAfter.Append(@"</body></smil>");
                        smilTop.Append(smilAfter.ToString());
                        string path1 = Server.MapPath($"/MmsUserFrameElement/{UserId}/");
                        if (!Directory.Exists(path1))
                        {
                            Directory.CreateDirectory(path1);
                        }
                        var smilByte = Encoding.UTF8.GetBytes(smilTop.ToString());
                        path1 += "smil.smil";
                        using (FileStream fileStream = new FileStream(path1, FileMode.Create, FileAccess.Write))
                        {
                            fileStream.Write(smilByte, 0, smilByte.Length);
                            fileStream.Close();
                        }
                        tmsBytes.AddRange(GetTmsByte(path1, "smil.smil"));
                        //List<byte> smilBytes = new List<byte>();
                        //smilBytes.AddRange(GetTmsByte(path1, "smil.smil"));
                        //smilBytes.AddRange(tmsBytes);
                        path1 = Server.MapPath($"/MmsUserFrameElement/{UserId}/tms1.tms");
                        using (FileStream stream = new FileStream(path1, FileMode.Create, FileAccess.Write))
                        {
                            byte[] tmsData = new byte[tmsBytes.Count()];
                            tmsBytes.CopyTo(tmsData);
                            stream.Write(tmsData, 0, tmsBytes.Count());
                            stream.Close();
                        }
                        //发送
                        var client = clientBLL.GetCustomerById(UserId.ToString());
                        mms.UserId = UserId;
                        mms.Account = User.Identity.Name;
                        //mms.ExtNumber = "";
                        mms.PassWord = client.ToKen;
                        mms.TaskId = "";
                        mms.Content = Convert.ToBase64String(tmsBytes.ToArray());

                        var url = ConfigurationManager.AppSettings["InternalServiceUrl"];
                        InternalService.InternalService interser = new InternalService.InternalService();
                        interser.Url = url;
                        var timespan = NativeMethods.GetTimeStamp();
                        var sercet = NativeMethods.Md5Encrypt(mms.PassWord + timespan);
                        InternalService.NResult result = interser.SendMms(mms.UserId, mms.Account, sercet, timespan, mms.Mobile, mms.Title, mms.Content, mms.PlanTime, mms.ExtNumber, mms.TaskId);
                        if (result.StatusCode == InternalService.StatusCode.Success)
                        {
                            return Json(msg);
                        }
                        else
                        {
                            msg = "{\"Code\":\"" + result.StatusCode + "\",\"msg\":\"" + result.Description + "\"}";
                            return Json(msg);
                        }
                    }
                    else
                    {
                        msg = "{\"Code\":\"2\",\"msg\":\"至少有一个帧元素！\"}";
                        return Json(msg);
                    }
                }
                else
                {
                    msg = "{\"Code\":\"2\",\"msg\":\"彩信元素为空！\"}";
                    return Json(msg);
                }
            }
            msg = "{\"Code\":\"3\",\"msg\":\"您的身份已过期，请重新登陆\"}";
            return Json(msg);
        }
        public static byte[] GetTmsByte2(string path,string FileName)
        {
            byte[] fileBuffer = null;
            using (MemoryStream ms = new MemoryStream())
            {
                using (BinaryWriter wr = new BinaryWriter(ms,Encoding.UTF8))
                {
                    using (FileStream fs = new FileStream(path, FileMode.Open))
                    {
                        using (BinaryReader br = new BinaryReader(fs, Encoding.UTF8))//二进制读取类
                        {
                            //文件名
                            byte[] fName = Encoding.ASCII.GetBytes(FileName + "\0");
                            byte[] fContent = br.ReadBytes((int)fs.Length);
                            wr.Write(fName, 0, fName.Length);
                            wr.Write((int)fs.Length);
                            wr.Write(fContent, 0, fContent.Length);
                        }
                    }
                }
                fileBuffer = ms.ToArray();
            }
            return fileBuffer;
        }
        public static byte[] GetTmsByte(string path, string FileName)
        {
            byte[] fileBuffer = null;
            using (MemoryStream ms = new MemoryStream())
            {
                using (BinaryWriter wr = new BinaryWriter(ms))
                {
                    using (FileStream fs = new FileStream(path, FileMode.Open))
                    {
                        using (BinaryReader bread = new BinaryReader(fs))
                        {
                            int filelen = (int)fs.Length;
                            byte[] fname = Encoding.ASCII.GetBytes(FileName + "\0");
                            byte[] content = bread.ReadBytes(filelen);
                            wr.Write(fname, 0, fname.Length);
                            wr.Write(filelen);
                            wr.Write(content, 0, content.Length);
                            bread.Close();
                        }
                        fs.Close();
                    }
                    wr.Close();
                }
                fileBuffer = ms.ToArray();
            }
            return fileBuffer;
        }
        [HttpGet]
        public ActionResult SaveDynamicMms()
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                var us = clientBLL.GetCustomerById(UserId.ToString());
                return View(us);
            }
            return RedirectToAction("/Account/Login");
        }
        public JsonResult SaveTemplateDynamicMmsOld(string dynamicMmsStr, string mmsStr)
        {

            string msg = "{\"Code\":\"1\",\"msg\":\"报备成功\"}";
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                DynamicMms.Frame[] dynamicMms = JsonConvert.DeserializeObject<DynamicMms.Frame[]>(dynamicMmsStr);
                if (dynamicMms != null)
                {
                    mmsStr = HttpUtility.UrlDecode(mmsStr);
                    SendMmsModel mms = JsonConvert.DeserializeObject<SendMmsModel>(mmsStr);
                    //if (!ModelState.IsValid)
                    //{
                    //    msg = "{\"Code\":\"2\",\"msg\":\"手机号或彩信标题为空！\"}";
                    //    return Json(msg);
                    //}
                    if (mms == null || string.IsNullOrEmpty(mms.Title))
                    {
                        msg = "{\"Code\":\"2\",\"msg\":\"彩信标题为空！\"}";
                        return Json(msg);
                    }
                    if (dynamicMms.Count() > 0)
                    {
                        List<byte> tmsBytes = new List<byte>();
                        //彩信文件按帧分组
                        var dynamicMmsGroup = dynamicMms.GroupBy(d => d.Zid);
                        //smil文件上半部分html
                        StringBuilder smilTop = new StringBuilder();
                        smilTop.Append(@"<smil> 
                                            <head> 
                                            <meta name='title' content=''/> 
                                            <layout> 
                                            <root-layout title='' width='240' height='320' background-color='#FFFFFF' color='#FF000008' font-size='8' font-family='Tahoma' font-style='' font-variant='' font-weight=''/> ");
                        StringBuilder smilAfter = new StringBuilder();
                        smilAfter.Append(@"</layout> 
                                        </head>  
                                        <body> ");
                        for (int i = 0; i < dynamicMmsGroup.Count(); i++)
                        {
                            var FrameId = dynamicMmsGroup.ElementAt(i);//帧Id
                            smilAfter.Append($@"<par dur='{FrameId.ElementAt(0).ZTime}s'>");
                            for (int k = 0; k < FrameId.Count(); k++)
                            {
                                var mmsInfo = FrameId.ElementAt(k);
                                string path = Server.MapPath($"/MmsUserFrameElement/{UserId}_{mmsInfo.Zid}/");
                                FileInfo fileInfo = new FileInfo(path + mmsInfo.FileName);
                                var id = mmsInfo.FileName.Split(".")[0];
                                var FileName = mmsInfo.FileName;
                                if (fileInfo.Exists)
                                {
                                    switch (mmsInfo.FileName.Split(".")[1].ToLower())
                                    {
                                        case "img":
                                        case "png":
                                        case "jpg":
                                        case "gif":
                                            smilTop.Append($@"<region left='0%' top='0%' width='100%' height='100%' id='{id}' z-index='0' fit='meet'/>");
                                            smilAfter.Append($@"<img region='{id}' src='{FileName}'/>");
                                            break;
                                        case "txt":
                                            smilTop.Append($@"<region left='0%' top='0%' width='100%' height='100%' id='{id}' z-index='0' fit='meet'/>");
                                            smilAfter.Append($@"<text region='{id}' src='{FileName}'/>");
                                            break;
                                        case "bmp":
                                            break;
                                        case "xml":
                                            break;
                                        case "wav":
                                            break;
                                        case "mid":
                                            break;
                                        case "amr":
                                            break;
                                        case "mp3":
                                            break;
                                        case "mp4":
                                            smilTop.Append($@"<region left='0%' top='0%' width='100%' height='100%' id='{id}' z-index='0'/>");
                                            smilAfter.Append($@"<video region='{id}' src='{FileName}'/>");
                                            break;
                                        default:
                                            break;
                                    }
                                    if (mmsInfo.FileName.Split(".")[1].ToLower() == "txt")
                                    {
                                        var txtcontent = System.IO.File.ReadAllText(path + mmsInfo.FileName);
                                        if (!string.IsNullOrEmpty(txtcontent))
                                        {
                                            if (txtcontent.Contains("#*#"))
                                            {
                                                Regex regex = new Regex("#*#");
                                                regex.Replace(txtcontent, "", 1);

                                            }
                                        }
                                    }
                                    else
                                    {
                                        tmsBytes.AddRange(GetTmsByte(path + mmsInfo.FileName, mmsInfo.FileName));
                                    }
                                }
                            }
                            smilAfter.Append($@"</par>");
                        }

                        smilAfter.Append(@"</body></smil>");
                        smilTop.Append(smilAfter.ToString());
                        string path1 = Server.MapPath($"/MmsUserFrameElement/{UserId}/");
                        if (!Directory.Exists(path1))
                        {
                            Directory.CreateDirectory(path1);
                        }
                        var smilByte = Encoding.UTF8.GetBytes(smilTop.ToString());
                        path1 += "smil.smil";
                        using (FileStream fileStream = new FileStream(path1, FileMode.Create, FileAccess.Write))
                        {
                            fileStream.Write(smilByte, 0, smilByte.Length);
                            fileStream.Close();
                        }
                        tmsBytes.AddRange(GetTmsByte(path1, "smil.smil"));
                        //List<byte> smilBytes = new List<byte>();
                        //smilBytes.AddRange(GetTmsByte(path1, "smil.smil"));
                        //smilBytes.AddRange(tmsBytes);
                        path1 = Server.MapPath($"/MmsUserFrameElement/{UserId}/tms1.tms");
                        using (FileStream stream = new FileStream(path1, FileMode.Create, FileAccess.Write))
                        {
                            byte[] tmsData = new byte[tmsBytes.Count()];
                            tmsBytes.CopyTo(tmsData);
                            stream.Write(tmsData, 0, tmsBytes.Count());
                            stream.Close();
                        }
                        //发送
                        var client = clientBLL.GetCustomerById(UserId.ToString());
                        mms.UserId = UserId;
                        mms.Account = User.Identity.Name;
                        //mms.ExtNumber = "";
                        mms.PassWord = client.ToKen;
                        mms.TaskId = "";
                        mms.Content = Convert.ToBase64String(tmsBytes.ToArray());

                        var url = ConfigurationManager.AppSettings["InternalServiceUrl"];
                        InternalService.InternalService interser = new InternalService.InternalService();
                        interser.Url = url;
                        var timespan = NativeMethods.GetTimeStamp();
                        var sercet = NativeMethods.Md5Encrypt(mms.PassWord + timespan);
                        InternalService.SaveMmsResult result = interser.SaveMms(UserId, User.Identity.Name, sercet, timespan, mms.Title, mms.Content);
                        if (result.StatusCode == InternalService.StatusCode.Success)
                        {
                            return Json(msg);
                        }
                        else
                        {
                            msg = "{\"Code\":\"" + result.StatusCode + "\",\"msg\":\"" + result.Description + "\"}";
                            return Json(msg);
                        }
                    }
                    else
                    {
                        msg = "{\"Code\":\"2\",\"msg\":\"至少有一个帧元素！\"}";
                        return Json(msg);
                    }
                }
                else
                {
                    msg = "{\"Code\":\"2\",\"msg\":\"彩信元素为空！\"}";
                    return Json(msg);
                }
            }
            msg = "{\"Code\":\"3\",\"msg\":\"您的身份已过期，请重新登陆\"}";
            return Json(msg);
        }
        public JsonResult SaveTemplateDynamicMms(string dynamicMmsStr, string mmsStr)
        {
            List<string> faildcount = new List<string>();
            List<string> successcount = new List<string>();
            string msg = "{\"Code\":\"1\",\"msg\":\"报备成功\"}";
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                
                DynamicMms.Frame[] dynamicMms = JsonConvert.DeserializeObject<DynamicMms.Frame[]>(dynamicMmsStr);
                if (dynamicMms != null)
                {
                    mmsStr = HttpUtility.UrlDecode(mmsStr);
                    SendMmsModel mms = JsonConvert.DeserializeObject<SendMmsModel>(mmsStr);
                    //if (!ModelState.IsValid)
                    //{
                    //    msg = "{\"Code\":\"2\",\"msg\":\"手机号或彩信标题为空！\"}";
                    //    return Json(msg);
                    //}
                    if (mms == null || string.IsNullOrEmpty(mms.Title))
                    {
                        msg = "{\"Code\":\"2\",\"msg\":\"彩信标题为空！\"}";
                        return Json(msg);
                    }
                    if (!string.IsNullOrEmpty(mms.Vars))
                    {
                        foreach (var item in mms.Vars.Split("\r\n"))
                        {
                            Trace.TraceInformation("item="+item);
                            if (dynamicMms.Count() > 0)
                            {
                                List<byte> tmsBytes = new List<byte>();
                                //彩信文件按帧分组
                                var dynamicMmsGroup = dynamicMms.GroupBy(d => d.Zid);
                                //smil文件上半部分html
                                StringBuilder smilTop = new StringBuilder();
                                smilTop.Append(@"<smil> 
                                            <head> 
                                            <meta name='title' content=''/> 
                                            <layout> 
                                            <root-layout title='' width='240' height='320' background-color='#FFFFFF' color='#FF000008' font-size='8' font-family='Tahoma' font-style='' font-variant='' font-weight=''/> ");
                                StringBuilder smilAfter = new StringBuilder();
                                smilAfter.Append(@"</layout> 
                                        </head>  
                                        <body> ");
                                for (int i = 0; i < dynamicMmsGroup.Count(); i++)
                                {
                                    var FrameId = dynamicMmsGroup.ElementAt(i);//帧Id
                                    smilAfter.Append($@"<par dur='{FrameId.ElementAt(0).ZTime}s'>");
                                    for (int k = 0; k < FrameId.Count(); k++)
                                    {
                                        var mmsInfo = FrameId.ElementAt(k);
                                        string path = Server.MapPath($"/MmsUserFrameElement/{UserId}_{mmsInfo.Zid}/");
                                        FileInfo fileInfo = new FileInfo(path + mmsInfo.FileName);
                                        var id = mmsInfo.FileName.Split(".")[0];
                                        var FileName = mmsInfo.FileName;
                                        if (fileInfo.Exists)
                                        {
                                            switch (mmsInfo.FileName.Split(".")[1].ToLower())
                                            {
                                                case "img":
                                                case "png":
                                                case "jpg":
                                                case "gif":
                                                    smilTop.Append($@"<region left='0%' top='0%' width='100%' height='100%' id='{id}' z-index='0' fit='meet'/>");
                                                    smilAfter.Append($@"<img region='{id}' src='{FileName}'/>");
                                                    break;
                                                case "txt":
                                                    smilTop.Append($@"<region left='0%' top='0%' width='100%' height='100%' id='{id}' z-index='0' fit='meet'/>");
                                                    smilAfter.Append($@"<text region='{id}' src='{FileName}'/>");
                                                    break;
                                                case "bmp":
                                                    break;
                                                case "xml":
                                                    break;
                                                case "wav":
                                                    break;
                                                case "mid":
                                                    break;
                                                case "amr":
                                                    break;
                                                case "mp3":
                                                    break;
                                                case "mp4":
                                                    smilTop.Append($@"<region left='0%' top='0%' width='100%' height='100%' id='{id}' z-index='0'/>");
                                                    smilAfter.Append($@"<video region='{id}' src='{FileName}'/>");
                                                    break;
                                                default:
                                                    break;
                                            }
                                            string oldtxtcontent = "";
                                            if (mmsInfo.FileName.Split(".")[1].ToLower() == "txt")
                                            {

                                                var txtcontent = System.IO.File.ReadAllText(path + mmsInfo.FileName,Encoding.GetEncoding("GB2312"));
                                                oldtxtcontent = txtcontent;
                                                Trace.TraceInformation("开始txtcontent=" + txtcontent);
                                                if (!string.IsNullOrEmpty(txtcontent))
                                                {
                                                    if (txtcontent.Contains("##"))
                                                    {
                                                        if (item.Contains("#"))
                                                        {
                                                            foreach (var item1 in item.Split("#"))
                                                            {
                                                                Regex regex = new Regex("##");
                                                                txtcontent=regex.Replace(txtcontent, item1, 1);
                                                                Trace.TraceInformation("txtcontent="+ txtcontent);
                                                            }
                                                        }
                                                        else
                                                        {
                                                            
                                                            txtcontent = txtcontent.Replace("##",item);
                                                            Trace.TraceInformation("txtcontent=" + txtcontent);
                                                        }
                                                    }
                                                }
                                               
                                                System.IO.File.WriteAllText(path + mmsInfo.FileName, txtcontent,Encoding.GetEncoding("GB2312"));
                                            }
                                            {
                                                tmsBytes.AddRange(GetTmsByte(path + mmsInfo.FileName, mmsInfo.FileName));
                                            }
                                            if (!string.IsNullOrEmpty(oldtxtcontent))
                                            {
                                                System.IO.File.WriteAllText(path + mmsInfo.FileName, oldtxtcontent, Encoding.GetEncoding("GB2312"));
                                            }
                                           
                                        }
                                    }
                                    smilAfter.Append($@"</par>");
                                }

                                smilAfter.Append(@"</body></smil>");
                                smilTop.Append(smilAfter.ToString());
                                string path1 = Server.MapPath($"/MmsUserFrameElement/{UserId}/");
                                if (!Directory.Exists(path1))
                                {
                                    Directory.CreateDirectory(path1);
                                }
                                var smilByte = Encoding.UTF8.GetBytes(smilTop.ToString());
                                path1 += "smil.smil";
                                using (FileStream fileStream = new FileStream(path1, FileMode.Create, FileAccess.Write))
                                {
                                    fileStream.Write(smilByte, 0, smilByte.Length);
                                    fileStream.Close();
                                }
                                tmsBytes.AddRange(GetTmsByte(path1, "smil.smil"));
                                //List<byte> smilBytes = new List<byte>();
                                //smilBytes.AddRange(GetTmsByte(path1, "smil.smil"));
                                //smilBytes.AddRange(tmsBytes);
                                path1 = Server.MapPath($"/MmsUserFrameElement/{UserId}/tms1.tms");
                                using (FileStream stream = new FileStream(path1, FileMode.Create, FileAccess.Write))
                                {
                                    byte[] tmsData = new byte[tmsBytes.Count()];
                                    tmsBytes.CopyTo(tmsData);
                                    stream.Write(tmsData, 0, tmsBytes.Count());
                                    stream.Close();
                                }
                                //发送
                                var client = clientBLL.GetCustomerById(UserId.ToString());
                                mms.UserId = UserId;
                                mms.Account = User.Identity.Name;
                                //mms.ExtNumber = "";
                                mms.PassWord = client.ToKen;
                                mms.TaskId = "";
                                mms.Content = Convert.ToBase64String(tmsBytes.ToArray());

                                var url = ConfigurationManager.AppSettings["InternalServiceUrl"];
                                InternalService.InternalService interser = new InternalService.InternalService();
                                interser.Url = url;
                                var timespan = NativeMethods.GetTimeStamp();
                                var sercet = NativeMethods.Md5Encrypt(mms.PassWord + timespan);
                                InternalService.SaveMmsResult result = interser.SaveMms(UserId, User.Identity.Name, sercet, timespan, mms.Title, mms.Content);
                                if (result.StatusCode == InternalService.StatusCode.Success)
                                {
                                    //return Json(msg);
                                    successcount.Add("1");
                                }
                                else
                                {
                                    faildcount.Add("1");
                                    msg = "{\"Code\":\"" + result.StatusCode + "\",\"msg\":\"" + result.Description + "，成功数量：" + successcount.Count() + "，失败数量：" + faildcount.Count + "\"}";
                                    return Json(msg);
                                }
                            }
                            else
                            {
                                faildcount.Add("1");
                                msg = "{\"Code\":\"2\",\"msg\":\"至少有一个帧元素！，成功数量："+successcount.Count()+"，失败数量："+faildcount.Count+"\"}";
                                return Json(msg);
                            }
                        }
                        msg = "{\"Code\":\"1\",\"msg\":\"处理完成！成功数量：" + successcount.Count() + "，失败数量：" + faildcount.Count + "\"}";
                        return Json(msg);
                    }
                    else
                    {
                        if (dynamicMms.Count() > 0)
                        {
                            List<byte> tmsBytes = new List<byte>();
                            //彩信文件按帧分组
                            var dynamicMmsGroup = dynamicMms.GroupBy(d => d.Zid);
                            //smil文件上半部分html
                            StringBuilder smilTop = new StringBuilder();
                            smilTop.Append(@"<smil> 
                                            <head> 
                                            <meta name='title' content=''/> 
                                            <layout> 
                                            <root-layout title='' width='240' height='320' background-color='#FFFFFF' color='#FF000008' font-size='8' font-family='Tahoma' font-style='' font-variant='' font-weight=''/> ");
                            StringBuilder smilAfter = new StringBuilder();
                            smilAfter.Append(@"</layout> 
                                        </head>  
                                        <body> ");
                            for (int i = 0; i < dynamicMmsGroup.Count(); i++)
                            {
                                var FrameId = dynamicMmsGroup.ElementAt(i);//帧Id
                                smilAfter.Append($@"<par dur='{FrameId.ElementAt(0).ZTime}s'>");
                                for (int k = 0; k < FrameId.Count(); k++)
                                {
                                    var mmsInfo = FrameId.ElementAt(k);
                                    string path = Server.MapPath($"/MmsUserFrameElement/{UserId}_{mmsInfo.Zid}/");
                                    FileInfo fileInfo = new FileInfo(path + mmsInfo.FileName);
                                    var id = mmsInfo.FileName.Split(".")[0];
                                    var FileName = mmsInfo.FileName;
                                    if (fileInfo.Exists)
                                    {
                                        switch (mmsInfo.FileName.Split(".")[1].ToLower())
                                        {
                                            case "img":
                                            case "png":
                                            case "jpg":
                                            case "gif":
                                                smilTop.Append($@"<region left='0%' top='0%' width='100%' height='100%' id='{id}' z-index='0' fit='meet'/>");
                                                smilAfter.Append($@"<img region='{id}' src='{FileName}'/>");
                                                break;
                                            case "txt":
                                                smilTop.Append($@"<region left='0%' top='0%' width='100%' height='100%' id='{id}' z-index='0' fit='meet'/>");
                                                smilAfter.Append($@"<text region='{id}' src='{FileName}'/>");
                                                break;
                                            case "bmp":
                                                break;
                                            case "xml":
                                                break;
                                            case "wav":
                                                break;
                                            case "mid":
                                                break;
                                            case "amr":
                                                break;
                                            case "mp3":
                                                break;
                                            case "mp4":
                                                smilTop.Append($@"<region left='0%' top='0%' width='100%' height='100%' id='{id}' z-index='0'/>");
                                                smilAfter.Append($@"<video region='{id}' src='{FileName}'/>");
                                                break;
                                            default:
                                                break;
                                        }
                                        {
                                            tmsBytes.AddRange(GetTmsByte(path + mmsInfo.FileName, mmsInfo.FileName));
                                        }
                                    }
                                }
                                smilAfter.Append($@"</par>");
                            }

                            smilAfter.Append(@"</body></smil>");
                            smilTop.Append(smilAfter.ToString());
                            string path1 = Server.MapPath($"/MmsUserFrameElement/{UserId}/");
                            if (!Directory.Exists(path1))
                            {
                                Directory.CreateDirectory(path1);
                            }
                            var smilByte = Encoding.UTF8.GetBytes(smilTop.ToString());
                            path1 += "smil.smil";
                            using (FileStream fileStream = new FileStream(path1, FileMode.Create, FileAccess.Write))
                            {
                                fileStream.Write(smilByte, 0, smilByte.Length);
                                fileStream.Close();
                            }
                            tmsBytes.AddRange(GetTmsByte(path1, "smil.smil"));
                            //List<byte> smilBytes = new List<byte>();
                            //smilBytes.AddRange(GetTmsByte(path1, "smil.smil"));
                            //smilBytes.AddRange(tmsBytes);
                            path1 = Server.MapPath($"/MmsUserFrameElement/{UserId}/tms1.tms");
                            using (FileStream stream = new FileStream(path1, FileMode.Create, FileAccess.Write))
                            {
                                byte[] tmsData = new byte[tmsBytes.Count()];
                                tmsBytes.CopyTo(tmsData);
                                stream.Write(tmsData, 0, tmsBytes.Count());
                                stream.Close();
                            }
                            //发送
                            var client = clientBLL.GetCustomerById(UserId.ToString());
                            mms.UserId = UserId;
                            mms.Account = User.Identity.Name;
                            //mms.ExtNumber = "";
                            mms.PassWord = client.ToKen;
                            mms.TaskId = "";
                            mms.Content = Convert.ToBase64String(tmsBytes.ToArray());

                            var url = ConfigurationManager.AppSettings["InternalServiceUrl"];
                            InternalService.InternalService interser = new InternalService.InternalService();
                            interser.Url = url;
                            var timespan = NativeMethods.GetTimeStamp();
                            var sercet = NativeMethods.Md5Encrypt(mms.PassWord + timespan);
                            InternalService.SaveMmsResult result = interser.SaveMms(UserId, User.Identity.Name, sercet, timespan, mms.Title, mms.Content);
                            if (result.StatusCode == InternalService.StatusCode.Success)
                            {
                                return Json(msg);
                            }
                            else
                            {
                                msg = "{\"Code\":\"" + result.StatusCode + "\",\"msg\":\"" + result.Description + "\"}";
                                return Json(msg);
                            }
                        }
                        else
                        {
                            msg = "{\"Code\":\"2\",\"msg\":\"至少有一个帧元素！\"}";
                            return Json(msg);
                        }
                    }
                }
                else
                {
                    msg = "{\"Code\":\"2\",\"msg\":\"彩信元素为空！\"}";
                    return Json(msg);
                }
            }
            msg = "{\"Code\":\"3\",\"msg\":\"您的身份已过期，请重新登陆\"}";
            return Json(msg);
        }
        /// <summary>
        /// 彩信查询
        /// </summary>
        /// <param name="mt"></param>
        /// <param name="currpage"></param>
        /// <param name="PageSize"></param>
        /// <returns></returns>
        public ViewResult QueryMms(MsgTask mt, int currpage = 0, int PageSize = 50)
        {
            mt.ServiceType = 2;
            mt.UserId = UserId;
            int rows = 0;
            int pages = 0;
            if (Request.RequestType != "GET")
            {
                if (currpage <= 0)
                {
                    currpage = 1;
                }
                if (mt.TaskId != null && mt.TaskId != "")
                {
                    mt.QueryRows = 0;
                    ViewBag.TaskList = mtbll.QueryClientTaskList(mt, currpage, PageSize, out rows, out pages);
                    if (currpage > pages)
                    {
                        currpage = rows;
                    }
                    if (pages != 0 && currpage <= 0)
                    {
                        currpage = 1;
                    }
                }
                else
                {
                    mt.QueryRows = 1;
                    ViewBag.TaskList = mtbll.QueryClientTaskList(mt, currpage, PageSize, out rows, out pages);
                }
            }

            //当前页数
            ViewBag.curpage = currpage;
            //总条数
            ViewBag.allsize = rows;
            //总页数
            ViewBag.allPage = pages;
            ViewBag.PageSize = PageSize;
            return View(mt);
        }
        /// <summary>
        /// 获取彩信查询总条数和总页数
        /// </summary>
        /// <param name="mt"></param>
        /// <param name="currpage"></param>
        /// <param name="PageSize"></param>
        /// <returns></returns>
        public JsonResult QueryMmsRowPageCount(MsgTask mt, int currpage = 0, int PageSize = 50)
        {
            mt.ServiceType = 2;
            mt.UserId = UserId;
            int rows = 0;
            int pages = 0;
            mt.QueryRows = 2;
            if (currpage <= 0)
            {
                currpage = 1;
            }
            mtbll.QueryClientTaskList(mt, currpage, PageSize, out rows, out pages);
            if (currpage > pages)
            {
                currpage = rows;
            }
            if (pages != 0 && currpage <= 0)
            {
                currpage = 1;
            }
            return Json(new { Rows = rows, PageCount = pages });
        }
        /// <summary>
        /// 查询彩信明细
        /// </summary>
        /// <param name="md"></param>
        /// <param name="currpage"></param>
        /// <param name="PageSize"></param>
        /// <returns></returns>
        public ViewResult QueryMmsDetail(MsgDetails md, int currpage = 0, int PageSize = 50)
        {
            md.ServiceType = 2;
            md.UserId = UserId;
            int rows = 0;
            int pages = 0;
            //ViewBag.templates = mdbll.GetMmsTemplates(UserId);
            if (Request.RequestType != "GET")
            {
                if (currpage <= 0)
                {
                    currpage = 1;
                }
                if (md.TaskId != null && md.TaskId != "")
                {
                    md.QueryCount = 0;
                    ViewBag.DetailList = mdbll.QueryClientMsgDetailsListClientProc(md, currpage, PageSize, out rows, out pages);
                    if (pages < currpage)
                    {
                        currpage = pages;
                    }
                    if (pages != 0 && currpage <= 0)
                    {
                        currpage = 1;
                    }
                }
                else
                {
                    md.QueryCount = 1;
                    ViewBag.DetailList = mdbll.QueryClientMsgDetailsListClientProc(md, currpage, PageSize, out rows, out pages);
                }
            }
            else if (md.TaskId != null && md.TaskId != "")
            {
                if (currpage <= 0)
                {
                    currpage = 1;
                }
                md.QueryCount = 0;
                ViewBag.DetailList = mdbll.QueryClientMsgDetailsListClientProc(md, currpage, PageSize, out rows, out pages);
                if (pages < currpage)
                {
                    currpage = pages;
                }
                if (pages != 0 && currpage <= 0)
                {
                    currpage = 1;
                }
            }

            //当前页数
            ViewBag.curpage = currpage;
            //总条数
            ViewBag.allsize = rows;
            //总页数
            ViewBag.allPage = pages;
            ViewBag.PageSize = PageSize;
            return View(md);
        }
        /// <summary>
        /// 获取彩信明细总条数
        /// </summary>
        /// <param name="md"></param>
        /// <param name="currpage"></param>
        /// <param name="PageSize"></param>
        /// <returns></returns>
        public JsonResult QueryMmsDetailRowsPage(MsgDetails md, int currpage = 0, int PageSize = 50)
        {
            md.ServiceType = 2;
            md.UserId = UserId;
            md.QueryCount = 2;
            int rows = 0;
            int pages = 0;
            if (currpage <= 0)
            {
                currpage = 1;
            }
            mdbll.QueryClientMsgDetailsListClientProc(md, currpage, PageSize, out rows, out pages);
            if (pages < currpage)
            {
                currpage = pages;
            }
            if (pages != 0 && currpage <= 0)
            {
                currpage = 1;
            }
            return Json(new { Rows = rows, PageCount = pages });
        }
        /// <summary>
        /// 彩信详情
        /// </summary>
        /// <param name="TaskId"></param>
        /// <param name="UserId"></param>
        /// <returns></returns>
        [HttpGet]
        public ActionResult MmsFileDetail(string TaskId, string UserId)
        {
            var mmsModel = new MsgDetialsBll().GetMsgData(TaskId);

            foreach (var item in mmsModel)
            {
                StringReader s = new StringReader(item.DataObject);
                var mms = new XmlSerializer(typeof(MmsContent)).Deserialize(s) as MmsContent;
                s.Close();
                var elements = mms.Elements.ToDictionary(x => x.Name);
                var doc = new XmlDocument();
                byte[] smilBytes = System.Convert.FromBase64String(mms.GetSmil().Base64Text);
                var smilxml = System.Text.Encoding.UTF8.GetString(smilBytes);
                doc.LoadXml(smilxml);
                var sbmmscontentBuilder = new StringBuilder();
                int parcount = 0;
                var parlist = doc.SelectNodes("/smil/body/par");
                int elementcount = -1;
                var mmsElementPath = Server.MapPath("/MmsElement/" + TaskId + "/");
                var mmsemementurl = "/MmsElement/" + TaskId + "/";
                if (!Directory.Exists(mmsElementPath))
                {
                    Directory.CreateDirectory(mmsElementPath);
                }
                if (parlist != null)
                {
                    //foreach (XmlNode item1 in parlist)
                    for (var parindex = 0; parindex < parlist.Count; parindex++)
                    {
                        sbmmscontentBuilder.Append("<table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style='border:1px solid #a9a9a9;'>");
                        var xmlAttributeCollection = parlist[parindex].Attributes;
                        if (xmlAttributeCollection != null)
                            sbmmscontentBuilder.Append("<tr><td style='background-color: #b0c4de;line-height: 20px;height: 20px;'>第" + (parindex + 1) + "帧/播放时间：" + xmlAttributeCollection["dur"].Value + "</td></tr>");
                        var elementsNodes = parlist[parindex].ChildNodes;
                        for (var elementindex = 0; elementindex < elementsNodes.Count; elementindex++)
                        {
                            elementcount++;
                            var attributes = elementsNodes[elementindex].Attributes;
                            if (attributes != null)
                            {
                                var ename = attributes["src"].Value;
                                if (!string.IsNullOrEmpty(ename))
                                {
                                    if (elements.ContainsKey(ename))
                                    {
                                        if (elements[ename].ElementType != ElementType.Smil)
                                        {
                                            sbmmscontentBuilder.Append(
                                                "<tr><td style='border-bottom:1px solid #a9a9a9;padding-top:6px;padding-bottom:6px;'>");
                                            if (elementsNodes[elementindex].Attributes != null &&
                                                !string.IsNullOrEmpty(ename))
                                            {
                                                sbmmscontentBuilder.Append("文件名称：" +
                                                                           elementsNodes[elementindex].Attributes["src"]
                                                                               .Value +
                                                                           "/文件类型：" +
                                                                           elements[
                                                                               elementsNodes[elementindex].Attributes["src"]
                                                                                   .Value]
                                                                               .ElementType +
                                                                           "<br />"); //elements[elementcount].ElementType
                                                sbmmscontentBuilder.Append("文件内容：<br />");
                                                //var attributeCollection = elementsNodes[elementindex].Attributes;

                                                switch (elementsNodes[elementindex].Name.ToLower())
                                                {
                                                    case "img":
                                                        var imgsrc = Base64StringToFile(elements[ename].Base64Text,
                                                            mmsElementPath,
                                                            mmsemementurl,
                                                            ename);
                                                        if (imgsrc != "error")
                                                        {
                                                            sbmmscontentBuilder.Append("<img src='" + imgsrc + "' /><br />");
                                                        }
                                                        else
                                                        {
                                                            sbmmscontentBuilder.Append(
                                                                "<span style='color:red;'>Base64编码字符串错误，无法转换显示图片.</span><br />");
                                                        }
                                                        break;
                                                    case "text":
                                                        var txt = Base64StringToTxtString(elements[ename].Base64Text);
                                                        sbmmscontentBuilder.Append(txt != "error"
                                                            ? "<pre>" + txt + "</pre><br />"
                                                            : "<span style='color:red;'>Base64编码字符串错误，无法转换显示文字.</span><br />");
                                                        break;
                                                    case "video":
                                                        var vsrc = Base64StringToFile(elements[ename].Base64Text,
                                                            mmsElementPath,
                                                            mmsemementurl,
                                                            ename);
                                                        if (vsrc != "error")
                                                        {
                                                            sbmmscontentBuilder.Append("视频文件:<a href='" + vsrc +
                                                                                       "' alt='右键目标另存为'>下载文件</a><br />");
                                                        }
                                                        else
                                                        {
                                                            sbmmscontentBuilder.Append(
                                                                "<span style='color:red;'>Base64编码字符串错误，无法转换显示图片.</span><br />");
                                                        }
                                                        break;
                                                    case "audio":
                                                        var asrc = Base64StringToFile(elements[ename].Base64Text,
                                                            mmsElementPath,
                                                            mmsemementurl,
                                                            ename);
                                                        if (asrc != "error")
                                                        {
                                                            sbmmscontentBuilder.Append("音频文件：<a href='" + asrc +
                                                                                       "' alt='右键目标另存为'>下载文件</a><br />");
                                                        }
                                                        else
                                                        {
                                                            sbmmscontentBuilder.Append(
                                                                "<span style='color:red;'>Base64编码字符串错误，无法转换显示图片.</span><br />");
                                                        }

                                                        break;
                                                    case "smil":
                                                        var smil = Base64StringToTxtString(elements[ename].Base64Text);
                                                        sbmmscontentBuilder.Append(smil != "error"
                                                            ? "<pre>" + smil + "</pre><br />"
                                                            : "<span style='color:red;'>Base64编码字符串错误，无法转换显示文字.</span><br />");
                                                        break;
                                                    default:
                                                        var dtxt = Base64StringToTxtString(elements[ename].Base64Text);
                                                        sbmmscontentBuilder.Append(dtxt != "error"
                                                            ? "<pre>" + dtxt + "</pre><br />"
                                                            : "<span style='color:red;'>Base64编码字符串错误，无法转换显示文字.</span><br />");
                                                        break;
                                                }
                                            }
                                            else
                                            {
                                                sbmmscontentBuilder.Append(
                                                    "文件内容：<br /><span style='color:red;'>找不到文件.</span><br />");
                                            }
                                            sbmmscontentBuilder.Append("</td></tr>");
                                        }
                                    }
                                    else
                                    {
                                        sbmmscontentBuilder.Append(
                                                    "<tr><td>文件内容：<br /><span style='color:red;'>找不到文件.</span><br /></td></tr>");
                                    }
                                }
                            }
                        }
                        sbmmscontentBuilder.Append("</table>");
                    }
                    parcount = parlist.Count;
                }
                ViewBag.subject = "主题：" + mms.Subject;
                ViewBag.msgremark = "共" + parcount + "帧，大小：" + Math.Round(((double)mms.GetSize() / 1024), 2) + "KB";
                ViewBag.userinfo = "账户：" + UserId.Substring(0, UserId.IndexOf('_'));
                ViewBag.datetime = "日期：" + UserId.Substring(UserId.IndexOf('_') + 1);//Convert.ToDateTime(UserId.Substring(UserId.IndexOf('_') + 1)).ToString("yyyyMMddHHmmss");
                //ViewBag.datetime = "日期：" + DateTime.ParseExact(, "yyyyMMddHHmmss", System.Globalization.CultureInfo.InvariantCulture);
                ViewBag.mmscontent = sbmmscontentBuilder.ToString();
                ViewBag.smil = smilxml;
                break;
            }
            return View();
        }
        [HttpGet]
        public ActionResult MmsTemplateFileDetail(string TaskId, string UserId)
        {
            var mmsModel = new MsgDetialsBll().GetMmsTemplateData(TaskId);
             
            foreach (var item in mmsModel)
            {
                StringReader s = new StringReader(item.MsgData);
                var mms = new XmlSerializer(typeof(MmsContent)).Deserialize(s) as MmsContent;
                s.Close();
                var elements = mms.Elements.ToDictionary(x => x.Name);
                var doc = new XmlDocument();
                byte[] smilBytes = System.Convert.FromBase64String(mms.GetSmil().Base64Text);
                var smilxml = System.Text.Encoding.UTF8.GetString(smilBytes);
                doc.LoadXml(smilxml);
                var sbmmscontentBuilder = new StringBuilder();
                int parcount = 0;
                var parlist = doc.SelectNodes("/smil/body/par");
                int elementcount = -1;
                var mmsElementPath = Server.MapPath("/MmsElement/" + TaskId + "/");
                var mmsemementurl = "/MmsElement/" + TaskId + "/";
                if (!Directory.Exists(mmsElementPath))
                {
                    Directory.CreateDirectory(mmsElementPath);
                }
                if (parlist != null)
                {
                    //foreach (XmlNode item1 in parlist)
                    for (var parindex = 0; parindex < parlist.Count; parindex++)
                    {
                        sbmmscontentBuilder.Append("<table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style='border:1px solid #a9a9a9;'>");
                        var xmlAttributeCollection = parlist[parindex].Attributes;
                        if (xmlAttributeCollection != null)
                            sbmmscontentBuilder.Append("<tr><td style='background-color: #b0c4de;line-height: 20px;height: 20px;'>第" + (parindex + 1) + "帧/播放时间：" + xmlAttributeCollection["dur"].Value + "</td></tr>");
                        var elementsNodes = parlist[parindex].ChildNodes;
                        for (var elementindex = 0; elementindex < elementsNodes.Count; elementindex++)
                        {
                            elementcount++;
                            var attributes = elementsNodes[elementindex].Attributes;
                            if (attributes != null)
                            {
                                var ename = attributes["src"].Value;
                                if (!string.IsNullOrEmpty(ename))
                                {
                                    if (elements.ContainsKey(ename))
                                    {
                                        if (elements[ename].ElementType != ElementType.Smil)
                                        {
                                            sbmmscontentBuilder.Append(
                                                "<tr><td style='border-bottom:1px solid #a9a9a9;padding-top:6px;padding-bottom:6px;'>");
                                            if (elementsNodes[elementindex].Attributes != null &&
                                                !string.IsNullOrEmpty(ename))
                                            {
                                                sbmmscontentBuilder.Append("文件名称：" +
                                                                           elementsNodes[elementindex].Attributes["src"]
                                                                               .Value +
                                                                           "/文件类型：" +
                                                                           elements[
                                                                               elementsNodes[elementindex].Attributes["src"]
                                                                                   .Value]
                                                                               .ElementType +
                                                                           "<br />"); //elements[elementcount].ElementType
                                                sbmmscontentBuilder.Append("文件内容：<br />");
                                                //var attributeCollection = elementsNodes[elementindex].Attributes;

                                                switch (elementsNodes[elementindex].Name.ToLower())
                                                {
                                                    case "img":
                                                        var imgsrc = Base64StringToFile(elements[ename].Base64Text,
                                                            mmsElementPath,
                                                            mmsemementurl,
                                                            ename);
                                                        if (imgsrc != "error")
                                                        {
                                                            sbmmscontentBuilder.Append("<img src='" + imgsrc + "' /><br />");
                                                        }
                                                        else
                                                        {
                                                            sbmmscontentBuilder.Append(
                                                                "<span style='color:red;'>Base64编码字符串错误，无法转换显示图片.</span><br />");
                                                        }
                                                        break;
                                                    case "text":
                                                        var txt = Base64StringToTxtString(elements[ename].Base64Text);
                                                        sbmmscontentBuilder.Append(txt != "error"
                                                            ? "<pre>" + txt + "</pre><br />"
                                                            : "<span style='color:red;'>Base64编码字符串错误，无法转换显示文字.</span><br />");
                                                        break;
                                                    case "video":
                                                        var vsrc = Base64StringToFile(elements[ename].Base64Text,
                                                            mmsElementPath,
                                                            mmsemementurl,
                                                            ename);
                                                        if (vsrc != "error")
                                                        {
                                                            sbmmscontentBuilder.Append("视频文件:<a href='" + vsrc +
                                                                                       "' alt='右键目标另存为'>下载文件</a><br />");
                                                        }
                                                        else
                                                        {
                                                            sbmmscontentBuilder.Append(
                                                                "<span style='color:red;'>Base64编码字符串错误，无法转换显示图片.</span><br />");
                                                        }
                                                        break;
                                                    case "audio":
                                                        var asrc = Base64StringToFile(elements[ename].Base64Text,
                                                            mmsElementPath,
                                                            mmsemementurl,
                                                            ename);
                                                        if (asrc != "error")
                                                        {
                                                            sbmmscontentBuilder.Append("音频文件：<a href='" + asrc +
                                                                                       "' alt='右键目标另存为'>下载文件</a><br />");
                                                        }
                                                        else
                                                        {
                                                            sbmmscontentBuilder.Append(
                                                                "<span style='color:red;'>Base64编码字符串错误，无法转换显示图片.</span><br />");
                                                        }

                                                        break;
                                                    case "smil":
                                                        var smil = Base64StringToTxtString(elements[ename].Base64Text);
                                                        sbmmscontentBuilder.Append(smil != "error"
                                                            ? "<pre>" + smil + "</pre><br />"
                                                            : "<span style='color:red;'>Base64编码字符串错误，无法转换显示文字.</span><br />");
                                                        break;
                                                    default:
                                                        var dtxt = Base64StringToTxtString(elements[ename].Base64Text);
                                                        sbmmscontentBuilder.Append(dtxt != "error"
                                                            ? "<pre>" + dtxt + "</pre><br />"
                                                            : "<span style='color:red;'>Base64编码字符串错误，无法转换显示文字.</span><br />");
                                                        break;
                                                }
                                            }
                                            else
                                            {
                                                sbmmscontentBuilder.Append(
                                                    "文件内容：<br /><span style='color:red;'>找不到文件.</span><br />");
                                            }
                                            sbmmscontentBuilder.Append("</td></tr>");
                                        }
                                    }
                                    else
                                    {
                                        sbmmscontentBuilder.Append(
                                                    "<tr><td>文件内容：<br /><span style='color:red;'>找不到文件.</span><br /></td></tr>");
                                    }
                                }
                            }
                        }
                        sbmmscontentBuilder.Append("</table>");
                    }
                    parcount = parlist.Count;
                }
                ViewBag.subject = "主题：" + mms.Subject;
                ViewBag.msgremark = "共" + parcount + "帧，大小：" + Math.Round(((double)mms.GetSize() / 1024), 2) + "KB";
                ViewBag.userinfo = "账户：" + UserId.Substring(0, UserId.IndexOf('_'));
                ViewBag.datetime = "日期：" + UserId.Substring(UserId.IndexOf('_') + 1);//Convert.ToDateTime(UserId.Substring(UserId.IndexOf('_') + 1)).ToString("yyyyMMddHHmmss");
                //ViewBag.datetime = "日期：" + DateTime.ParseExact(, "yyyyMMddHHmmss", System.Globalization.CultureInfo.InvariantCulture);
                ViewBag.mmscontent = sbmmscontentBuilder.ToString();
                ViewBag.smil = smilxml;
                break;
            }
            return View();
        }
        private string Base64StringToFile(string base64String, string mmselementpath, string mmsurl, string filename)
        {
            string fullfilename = mmselementpath + filename;
            if (!System.IO.File.Exists(fullfilename))
            {
                try
                {
                    byte[] fileBytes = Convert.FromBase64String(base64String);
                    System.IO.File.WriteAllBytes(fullfilename, fileBytes);
                }
                catch
                {
                    return "error";
                }
            }
            return mmsurl + filename;
        }
        /// <summary>
        /// base64转string
        /// </summary>
        /// <param name="base64String"></param>
        /// <returns></returns>
        private string Base64StringToTxtString(string base64String)
        {
            try
            {
                byte[] txtBytesBytes = Convert.FromBase64String(base64String);
                var txtstring = Encoding.Default.GetString(txtBytesBytes);
                return txtstring;
            }
            catch (Exception)
            {
                return "error";
            }
        }
        /// <summary>
        /// 下载彩信
        /// </summary>
        /// <param name="msgid"></param>
        /// <returns></returns>
        public ActionResult DownloadMms(string TaskId)
        {
            var obj = new MsgDetialsBll().GetMsgData(TaskId);
            StringReader reader = new StringReader(obj.FirstOrDefault().DataObject);
            XmlSerializer xmlSerializer = new XmlSerializer(typeof(MmsContent));
            var mms = xmlSerializer.Deserialize(reader) as MmsContent;
            var buffer = mms.Elements.ToTms();
            var fileName = mms.Subject + ".tms";
            //清除之前输出信息
            this.Response.Clear();
            this.Response.ClearHeaders();
            this.Response.ClearContent();

            //设置文件头格式
            this.Response.Buffer = false;
            this.Response.AddHeader("Accept-Ranges", "bytes");
            this.Response.AddHeader("Content-Length", buffer.Length.ToString());
            this.Response.AddHeader("Connection", "Keep-Alive");
            this.Response.ContentType = "application/octet-stream";//设置输出类型，为文件流
            this.Response.Charset = "GB2312";
            this.Response.ContentEncoding = this.Response.HeaderEncoding = System.Text.Encoding.GetEncoding("GB2312");
            var userAgent = this.Request.UserAgent;
            if (userAgent != null && userAgent.Contains("MSIE"))
            {
                fileName = HttpUtility.UrlEncode(fileName, System.Text.Encoding.UTF8);//对文件名进行url编码
            }
            this.Response.AddHeader("Content-Disposition", "attachment;filename=" + fileName);//设置保存时默认的文件名
            //System.IO.File.WriteAllBytes(@"d:\a.tms", buffer);
            this.Response.OutputStream.Write(buffer, 0, buffer.Length);
            //清空输出
            this.Response.Flush();
            this.Response.End();
            return null;

        }
        /// <summary>
        /// 帧内容部分视图页
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ActionResult _FrameView(int id)
        {
            ViewBag.id = id;
            return PartialView();
        }
        /// <summary>
        /// 上传帧彩信文件
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        public JsonResult UpLoadMmsFile(string uid,string zid)
        {
            string json = "{\"msg\":\"上传成功!\"}";
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                try
                {
                    string MmsUserFrameElement = Server.MapPath($"/MmsUserFrameElement/{uid}_{zid}/");
                    if (!Directory.Exists(MmsUserFrameElement))
                    {
                        Directory.CreateDirectory(MmsUserFrameElement);
                    }
                    HttpFileCollectionBase mmsfiles = Request.Files;
                    if (mmsfiles.Count > 0)
                    {
                        for (int i = 0; i < mmsfiles.Count; i++)
                        {
                            string filename = mmsfiles[i].FileName;
                            filename = MmsUserFrameElement + filename;
                            mmsfiles[i].SaveAs(filename);
                        }
                    }
                    json = "{\"msg\":\"上传成功!\",\"FileName\":\"" + mmsfiles[0].FileName + "\"}";
                    return Json(json);
                }
                catch (Exception ex)
                {
                    json = "{\"msg\":\"上传失败！后台异常"+ex.Message+"!\"}";
                    return Json(json);
                }
            }
            json = "{\"msg\":\"上传失败！身份已过期，或在其他登陆!\"}";
            return Json(json);
        }
        /// <summary>
        /// 删除上传帧彩信文件
        /// </summary>
        /// <returns></returns>
        public JsonResult DeleteMmsFile(string uid, string zid,string key)
        {
            string json="{\"msg\":\"删除成功!\"}";
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                string path = Server.MapPath($"/MmsUserFrameElement/{uid}_{zid}/");
                try
                {
                    if (key != null && key != "")
                    {
                        path += key;
                        System.IO.File.Delete(path);
                        //Response.Write(json); return;
                        return Json(json);
                    }
                    json = "{\"msg\":\"删除失败!\"}";
                    //Response.Write(json); return;
                    return Json(json);
                }
                catch (Exception ex)
                {
                    json = "{\"error\":\"删除失败!后台异常，ex="+ex.Message+"\"}";
                    //Response.Write(json);
                    //return Json(json);
                }
                return Json(json);
            }
            json = "{\"error\":\"删除失败！身份已过期，或在其他登陆!\"}";
            //Response.Write(json); return;
            return Json(json);
        }
        /// <summary>
        /// 删除帧目录
        /// </summary>
        /// <param name="uid"></param>
        /// <param name="zid"></param>
        /// <returns></returns>
        public JsonResult DeleteMmsFileDirectory(string uid,string zid)
        {
            string json = "{\"msg\":\"帧删除成功!\"}";
            if (Request.IsAuthenticated && Session["UserId"]!=null)
            {
                string path = Server.MapPath($"/MmsUserFrameElement/{uid}_{zid}/");
                System.IO.Directory.Delete(path, true);
                return Json(json);
            }
            json = "{\"error\":\"删除失败！身份已过期，或在其他登陆!\"}";
            return Json(json);
        }
        /// <summary>
        /// 获取彩信帧内容文件
        /// </summary>
        /// <param name="uid"></param>
        /// <param name="zid"></param>
        /// <returns></returns>
        public JsonResult GetMmsFile(string uid, string zid)
        {
            string json = "{\"msg\":\"获取失败！\"}";
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                List<string> FileNames = new List<string>();
                string path = Server.MapPath($"/MmsUserFrameElement/{uid}_{zid}/");
                DirectoryInfo info = new DirectoryInfo(path);
                FileInfo[] fileInfo=info.GetFiles();
                foreach (var item in fileInfo)
                {
                    FileNames.Add(item.Name);
                }
                return Json(FileNames);
            }
            json = "{\"msg\":\"获取失败！身份已过期，或在其他登陆!\"}";
            return Json(json);
        }
        #region 增彩
        /// <summary>
        /// 模板报备
        /// </summary>
        /// <returns></returns>
        public ActionResult MmsTemplate()
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                return View();
            }
            return RedirectToAction("/Account/Login");
        }
        public JsonResult TemplateMms(MmsTemplateModel mms)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                try
                {
                    if (Request.Files.Count > 0)
                    {
                        var httpPostedFileBase = Request.Files[0];
                        if (httpPostedFileBase != null && (Request.Files.Count > 0 && httpPostedFileBase.ContentLength > 0))
                        {
                            var us = clientBLL.GetCustomerById(UserId.ToString());
                            var bytes = new byte[httpPostedFileBase.ContentLength];
                            httpPostedFileBase.InputStream.Read(bytes, 0, bytes.Length);
                            var content = Convert.ToBase64String(bytes);
                            InternalService.InternalService internalService = new InternalService.InternalService();
                            internalService.Url = ConfigurationManager.AppSettings["InternalServiceUrl"].ToString();
                            string UPassWord = us.ToKen;
                            var timeStamp = NativeMethods.GetTimeStamp();
                            var md5Secret = NativeMethods.Md5Encrypt(UPassWord + timeStamp).ToLower();
                            InternalService.SaveMmsResult result = internalService.SaveMms(UserId, User.Identity.Name, md5Secret, timeStamp, mms.Subject, content);
                            if (result.StatusCode == InternalService.StatusCode.Success)
                            {
                                return Json(new { code = 0, msg = "报备成功！" });
                            }
                            else
                            {
                                return Json(new { code = result.StatusCode, msg = result.Description });
                            }
                        }

                        return Json(new { code = 2, msg = "彩信文件不能为空！" });
                    }
                }
                catch (Exception ex)
                {
                    Trace.TraceError("报备彩信模板异常，ex="+ex);
                }

            }
            return Json(new { code=0,msg="您的身份验证过期！"});
        }
        /// <summary>
        /// 发送增彩
        /// </summary>
        /// <returns></returns>
        public ActionResult SendMmsTemplate()
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                ViewBag.templates=mdbll.GetMmsTemplates(UserId);
                return View();
            }
            return RedirectToAction("/Account/Login");
        }
        /// <summary>
        /// 下载彩信
        /// </summary>
        /// <param name="msgid"></param>
        /// <returns></returns>
        public ActionResult DownloadMmsTemplate(string TaskId)
        {
            var obj = mdbll.GetMmsTemplateData(TaskId);
            StringReader reader = new StringReader(obj.FirstOrDefault().MsgData);
            XmlSerializer xmlSerializer = new XmlSerializer(typeof(MmsContent));
            var mms = xmlSerializer.Deserialize(reader) as MmsContent;
            var buffer = mms.Elements.ToTms();
            var fileName = mms.Subject + ".tms";
            //清除之前输出信息
            this.Response.Clear();
            this.Response.ClearHeaders();
            this.Response.ClearContent();

            //设置文件头格式
            this.Response.Buffer = false;
            this.Response.AddHeader("Accept-Ranges", "bytes");
            this.Response.AddHeader("Content-Length", buffer.Length.ToString());
            this.Response.AddHeader("Connection", "Keep-Alive");
            this.Response.ContentType = "application/octet-stream";//设置输出类型，为文件流
            this.Response.Charset = "GB2312";
            this.Response.ContentEncoding = this.Response.HeaderEncoding = System.Text.Encoding.GetEncoding("GB2312");
            var userAgent = this.Request.UserAgent;
            if (userAgent != null && userAgent.Contains("MSIE"))
            {
                fileName = HttpUtility.UrlEncode(fileName, System.Text.Encoding.UTF8);//对文件名进行url编码
            }
            this.Response.AddHeader("Content-Disposition", "attachment;filename=" + fileName);//设置保存时默认的文件名
            //System.IO.File.WriteAllBytes(@"d:\a.tms", buffer);
            this.Response.OutputStream.Write(buffer, 0, buffer.Length);
            //清空输出
            this.Response.Flush();
            this.Response.End();
            return null;

        }
        public JsonResult MmsTemplateSend(SendMmsModel model)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                if (model.MmsId.Contains("+"))
                {
                    model.MmsId = (model.MmsId.Split("+")[0]);
                }
                int.TryParse(ConfigurationManager.AppSettings["SubmitMaxCount"], out int a);
                if (a <= 0)
                {
                    a = 100000;
                }
                if (model.Mobile != null && model.Mobile.Split(",").Count() > a)
                {
                    return Json(new { Code = 2, Description = "号码不能超过十万条" });
                }

                var ary = model.Mobile.Trim().Replace("\r\n", ",").Replace(" ", ",").Replace("，", ",").Replace("\r", ",").Replace("\n", ",").Split(',', StringSplitOptions.RemoveEmptyEntries);
                if (ary == null || ary.Length < 1)
                {
                    return Json(new { Code = 2, Description = "号码为空！" });
                }
                if (!ary.Any(x => x.Length == 11))
                {
                    return Json(new { Code = 2, Description = "号码错误！" });
                }


                


                var us = clientBLL.GetCustomerById(UserId.ToString());


                us.SUBMITPACKAGE = us.SUBMITPACKAGE > 3000 ? 3000 : (us.SUBMITPACKAGE <= 0 ? 3000 : us.SUBMITPACKAGE);
                List<List<string>> msisdnitem = GetListGroup(ary.ToList(), us.SUBMITPACKAGE);
                Trace.TraceInformation($"userid={User.Identity.Name},拆分后，msisdnitemCount={msisdnitem.Count}");

                var url = ConfigurationManager.AppSettings["InternalServiceUrl"];
                InternalService.InternalService internalService = new InternalService.InternalService();
                internalService.Url = url;

                model.UserId = UserId;
                model.PassWord = us.ToKen;
                int i = 0;
                foreach (var item in msisdnitem)
                {
                    var timeStamp = NativeMethods.GetTimeStamp();
                    var md5Secret = NativeMethods.Md5Encrypt(model.PassWord + timeStamp).ToLower();
                    InternalService.NResult sendResult = internalService.SendMmsByMmsId(model.UserId, User.Identity.Name, md5Secret, timeStamp, item.Join(","), model.MmsId, model.PlanTime, model.ExtNumber, "");
                    if (sendResult.StatusCode != InternalService.StatusCode.Success)
                        return Json(new { Code = sendResult.StatusCode, Description = sendResult.Description + "，共 " + msisdnitem.Count + " 批，成功 " + i + " 批！" });

                    Trace.TraceInformation($"user {User.Identity.Name} 共 {msisdnitem.Count} 批，当前第 {i} 批发送成功");
                    i += 1;
                }
                return Json(new { code = 1, Description = "操作完成" });
            }
            return Json(new { code=2, Description = ""});
        }

        /// <summary>
        /// 模板发送带变量
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public JsonResult MmsTemplateSendVars(SendMmsModel model)
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                if (string.IsNullOrEmpty(model.Vars)){
                    return Json(new { Code = 2, Description = "参数不能为空" });
                }

                int count = 0;
                if (model.MmsId.Contains("+"))
                {
                    count =Convert.ToInt32( model.MmsId.Split("+")[1]);
                    model.MmsId = ( model.MmsId.Split("+")[0]);
                }
                var arr1=model.Vars.Split("#");
                if (arr1.Length!=count)
                {
                    return Json(new { Code = 2, Description = "参数数量与模板数量不匹配" });
                }
                var rmodel=mdbll.GetMmsTemplateData(model.MmsId)?.FirstOrDefault();
                if (rmodel==null)
                {
                    return Json(new { Code = 2, Description = "模板不存在" });
                }
                else
                {
                    if (model.Vars.Contains("#"))
                    {
                        var arr = model.Vars.Split("#");
                        if (arr.Length != count)
                        {
                            return Json(new { Code = 2, Description = "模板参数数量不一致" });
                        }
                        else
                        {
                            List<Dictionary<string, string>> dic = new List<Dictionary<string, string>>();
                            Dictionary<string, string> d1 = new Dictionary<string, string>();
                            for (int d = 1; d <= arr.Length; d++)
                            {
                                d1.Add(d.ToString(), arr[d-1].ToString());
                                
                            }
                            dic.Add(d1);
                            model.Vars=JsonConvert.SerializeObject(dic);
                        }
                    }
                    else
                    {
                        
                        List<Dictionary<string,string>> dic = new List<Dictionary<string, String>>();
                        Dictionary<string, string> d1 = new Dictionary<string, string>();
                        d1.Add("1", model.Vars);
                        dic.Add(d1);
                        model.Vars = JsonConvert.SerializeObject(dic);
                    }
                }
                Trace.TraceInformation($"UserName={User.Identity.Name},vars={model.Vars}");

                
                

                int.TryParse(ConfigurationManager.AppSettings["SubmitMaxCount"], out int a);
                if (a <= 0)
                {
                    a = 100000;
                }
                if (model.Mobile != null && model.Mobile.Split(",").Count() > a)
                {
                    return Json(new { Code = 2, Description = "号码不能超过十万条" });
                }

                var ary = model.Mobile.Trim().Replace("\r\n", ",").Replace(" ", ",").Replace("，", ",").Replace("\r", ",").Replace("\n", ",").Split(',', StringSplitOptions.RemoveEmptyEntries);
                if (ary == null || ary.Length < 1)
                {
                    return Json(new { Code = 2, Description = "号码为空！" });
                }
                if (!ary.Any(x => x.Length == 11))
                {
                    return Json(new { Code = 2, Description = "号码错误！" });
                }





                var us = clientBLL.GetCustomerById(UserId.ToString());


                us.SUBMITPACKAGE = us.SUBMITPACKAGE > 3000 ? 3000 : (us.SUBMITPACKAGE <= 0 ? 3000 : us.SUBMITPACKAGE);
                List<List<string>> msisdnitem = GetListGroup(ary.ToList(), us.SUBMITPACKAGE);
                Trace.TraceInformation($"userid={User.Identity.Name},拆分后，msisdnitemCount={msisdnitem.Count}");

                var url = ConfigurationManager.AppSettings["InternalServiceUrl"];
                InternalService.InternalService internalService = new InternalService.InternalService();
                internalService.Url = url;

                model.UserId = UserId;
                model.PassWord = us.ToKen;
                int i = 0;
                foreach (var item in msisdnitem)
                {
                    var timeStamp = NativeMethods.GetTimeStamp();
                    var md5Secret = NativeMethods.Md5Encrypt(model.PassWord + timeStamp).ToLower();
                    InternalService.NResult sendResult = internalService.SendMmsByMmsIdVars(model.UserId, User.Identity.Name, md5Secret, timeStamp, item.Join(","), model.MmsId, model.PlanTime, model.ExtNumber, "",model.Vars);
                    if (sendResult.StatusCode != InternalService.StatusCode.Success)
                        return Json(new { Code = sendResult.StatusCode, Description = sendResult.Description + "，共 " + msisdnitem.Count + " 批，成功 " + i + " 批！" });

                    Trace.TraceInformation($"user {User.Identity.Name} 共 {msisdnitem.Count} 批，当前第 {i} 批发送成功");
                    i += 1;
                }
                return Json(new { code = 1, Description = "操作完成" });
            }
            return Json(new { code = 2, Description = "" });
        }

        /// <summary>
        /// 按指定数量对List分组
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list"></param>
        /// <param name="groupNum"></param>
        /// <returns></returns>
        public static List<List<T>> GetListGroup<T>(List<T> list, int groupNum)
        {
            List<List<T>> listGroup = new List<List<T>>();
            for (int i = 0; i < list.Count(); i += groupNum)
            {
                listGroup.Add(list.Skip(i).Take(groupNum).ToList());
            }
            return listGroup;
        }
        /// <summary>
        /// 增彩模板查询
        /// </summary>
        /// <returns></returns>
        public ActionResult MmsTemplateView(MmsTemplateModel mt, int currpage = 0, int PageSize = 20)
        {

            mt.UserId = UserId;
            int rows = 0;
            int pages = 0;
            if (mt.StartTime==null)
            {
                mt.StartTime = DateTime.Now.Date;
            }
            if (Request.RequestType != "GET")
            {
                if (currpage <= 0)
                {
                    currpage = 1;
                }
                mt.PageSize = PageSize;
                mt.CurrPage = currpage;
                ViewBag.templates = mdbll.GetMmsTemplates(mt);
                pages = mt.PageCount;
                rows = mt.AllCount;
                if (currpage > pages)
                {
                    currpage = rows;
                }
                if (pages != 0 && currpage <= 0)
                {
                    currpage = 1;
                }
            }

            //当前页数
            ViewBag.curpage = currpage;
            //总条数
            ViewBag.allsize = rows;
            //总页数
            ViewBag.allPage = pages;
            ViewBag.PageSize = PageSize;
            return View(mt);
        }

            #endregion

        }
    public class SendMmsModel
    {
        [Required]
        public string Title { get; set; }
        public int UserId { get; set; }
        [Required]
        public string Mobile { get; set; }
        public DateTime? PlanTime { get; set; }
        public string Account { get; set; }
        public string PassWord { get; set; }
        public string Content { get; set; }
        public string ExtNumber { get; set; }
        public string TaskId { get; set; }
        public string MmsId { get; set; }
        public string Vars { get; set; }
    }
}
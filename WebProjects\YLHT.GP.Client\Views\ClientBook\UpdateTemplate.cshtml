﻿@model YLHT.GP.Models.BirthdayTemplate
@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="renderer" content="webkit">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <title>修改生日祝福模板</title>

    <link href="~/Content/bootstrap.min.css" rel="stylesheet" />
    <link href="~/Client/css/font-awesome.min.css" rel="stylesheet" />
    <link href="~/Client/css/animate.min.css" rel="stylesheet" />
    <link href="~/Client/css/addGroup.css?v=1.0" rel="stylesheet" />
    <script src="~/Client/js/jquery.min.js"></script>
    <script src="~/Client/js/bootstrap.min.js"></script>
    <script src="~/Client/js/layer/layer.js"></script>
    <script src="~/Client/js/plugins/layer/laydate/laydate.js"></script>
    <script src="~/Client/js/addGroup.js?v=1.0"></script>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-xs-12 col-title">
                <div class="form-group">
                    <input type="hidden" id="tid" value="@Model.TEMPLATEID"/>
                    <label for="note">生日模板：</label>
                    <textarea id="group-britday"
                              class="form-control" rows="6" style="resize:none;">@Model.Template</textarea>
                </div>
                <button class="btn btn-primary" id="edittemplate">确认修改</button>
            </div>
        </div>
    </div>
</body>
</html>

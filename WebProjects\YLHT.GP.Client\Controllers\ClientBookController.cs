﻿using ExcelDataReader;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using YLHT.GP.Common;
using YLHT.GP.Models;
using YLHT_GP_Business.Business;

namespace YLHT.GP.Client.Controllers
{
    public class ClientBookController : BaseController
    {
        BookBll bookBll = new BookBll();
        // GET: ClientBook
        public ActionResult Index()
        {
            return View();
        }
        public JsonResult GetGroups()
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                List<BookGroup> bookGroups = new List<BookGroup>();
                bookGroups.Add(new BookGroup() { GroupId = 0,GroupName="全部联系人" });
                bookGroups.AddRange(bookBll.GetGroups(UserId));
                return Json(bookGroups);
            }
            return Json(null);
        }
        public JsonResult GetContacts()
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                return Json(bookBll.GetBooks(new GP.Models.Book() { UserId= UserId }));
            }
            return Json(null);
        }
        public ActionResult AddBook()
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                List<BirthdayTemplate> birthdayTemplates = new List<BirthdayTemplate>();
                birthdayTemplates.Add(new BirthdayTemplate() { UserId = UserId, TEMPLATEID = 0,Template="---请选择模板---" });
                birthdayTemplates.AddRange(bookBll.GetBirthdayTemplates(new BirthdayTemplate() { UserId = UserId }));
                ViewBag.BirthdayTemplates = birthdayTemplates;
                return View();
            }

            return RedirectToAction("/Account/Login");
        }
        public ActionResult UpdateGroup(int groupid)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                ViewBag.BirthdayTemplates = bookBll.GetBirthdayTemplates(new BirthdayTemplate() { UserId=UserId});
                return View(bookBll.GetGroupById(groupid));
            }
            return RedirectToAction("/Account/Login");
        }
        [HttpPost]
        public JsonResult UpdateGroup(int groupid,string name,int template)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                if (bookBll.UpdateGroup(new BookGroup() {GroupId=groupid,GroupName=name,TemplateId=template }))
                {
                    return Json("修改成功");
                }
            }
            return Json("修改失败");
        }
        public JsonResult AddGroup(string name,int templateid=0)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                if (bookBll.AddGroup(new GP.Models.BookGroup() { GroupName=name, TemplateId = templateid,UserId=UserId }))
                {
                    return Json("添加成功");
                }
            }
            return Json("添加失败");
        }

        public JsonResult UpdateRemind(int bookid,int isremind) {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                if (bookBll.UpdateRemind(bookid,isremind))
                {
                    return Json(1);
                }
            }
            return Json(0);
        }


        public ViewResult addContacts()
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                List<BookGroup> bookGroups = new List<BookGroup>();
                bookGroups.AddRange(bookBll.GetGroups(UserId));
                ViewBag.Groups = bookGroups;
            }
            return View();
        }
        [HttpPost]
        public  JsonResult AddContacts(int gId,string name,string mb,string mes,string birthday,int sexa,string isremind="0")
        {
            if (Request.IsAuthenticated && Session["UserId"]!=null)
            {
                Book book = new Book();
                book.UserName = name;
                book.BirthdayTime = birthday;
                book.GroupId = gId;
                book.Msisdn = mb;
                book.Remark = mes;
                book.UserId = UserId;
                book.IsRemind = isremind;
                book.Sex = sexa;
                if (bookBll.AddBook(book))
                {
                    return Json("添加成功");
                }
                return Json("添加失败");
            }
            return Json("添加失败");
        }
        public ViewResult updateContacts(int Id)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                List<BookGroup> bookGroups = new List<BookGroup>();
                bookGroups.AddRange(bookBll.GetGroups(UserId));
                ViewBag.Groups = bookGroups;
                return View(bookBll.GetBookById(Id));
            }
            return View();
        }
        public JsonResult DeleteContacts(string cid)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                if (bookBll.DeleteBook(cid))
                {
                    return Json("删除成功");
                }
            }
            return Json("删除失败");
        }
        [HttpPost]
        public JsonResult UpdateContacts(int id,int gId, string name, string mb, string mes, string birthday,int sexa)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                Book book = new Book();
                book.BookId = id;
                book.UserName = name;
                book.BirthdayTime = birthday;
                book.GroupId = gId;
                book.Msisdn = mb;
                book.Remark = mes;
                book.Sex = sexa;
                if (bookBll.UpdateBook(book))
                {
                    return Json("修改成功");
                }
            }
            return Json("修改失败");
        }


        public ActionResult Move(string ids)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {

                ViewBag.Id = ids;
                List<BookGroup> bookGroups = new List<BookGroup>();
                bookGroups.AddRange(bookBll.GetGroups(UserId));
                ViewBag.Groups = bookGroups;
                return View();
            }
            return RedirectToAction("/Account/Login"); ;
        }
        [HttpPost]
        public JsonResult Move(string ids,int gid)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                if (bookBll.MoveGroup(ids,gid))
                {
                    return Json("转移成功"); 
                }
            }
            return Json("转移失败");
        }

        public JsonResult DeleteGroup(int id)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                if (bookBll.DeleteGroup(id))
                {
                    return Json("删除成功");
                }
            }
            return Json("删除失败");
        }
        public ActionResult UpdateTemplate(int id)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                return View(bookBll.GetTemplate(id));
            }
            return RedirectToAction("/Account/Login");
        }
        [HttpPost]
        public JsonResult TemplateUpdate(int tid,string template)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                if (bookBll.UpdateTemplate(tid,template))
                {
                    return Json("修改成功");
                }
            }
            return Json("修改失败");
        }
        [HttpPost]
        public JsonResult DeleteTemplate(int id)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                if (bookBll.DeleteTemplate(id))
                {
                    return Json("删除成功");
                }
            }
            return Json("删除失败");
        }
        public ActionResult TemplateList(BirthdayTemplate birthdayTemplate)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                birthdayTemplate.UserId = UserId;
                ViewBag.Templates= bookBll.GetBirthdayTemplates(birthdayTemplate);
                return View(birthdayTemplate);
            }
            return RedirectToAction("/Account/Login");
        }
        public ActionResult AddTemplate()
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                return View();
            }
            return RedirectToAction("/Account/Login");
        }
        [HttpPost]
        public JsonResult AddTemplate(string content)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                if (bookBll.AddBirthdayTemplate(UserId,content))
                {
                    return Json("添加成功");
                }
            }
            return Json("添加失败");
        }
        public ActionResult BatchAdd()
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                ViewBag.Groups = bookBll.GetGroups(UserId); 
                return View();
            }
            return RedirectToAction("/Account/Login");
        }
        private readonly string extNames = "|.xls|.xlsx|";
        [HttpPost]
        public JsonResult BatchAdd(int group)
        {
            List<Book> books = new List<Book>();
            if (Request.IsAuthenticated&Session["UserId"]!=null)
            {
                if (Request.Files.Count>0)
                {
                    var httpPostedFileBase = Request.Files[0];
                    if (httpPostedFileBase != null && (Request.Files.Count > 0 && httpPostedFileBase.ContentLength > 0))
                    {
                        //文件名
                        string fileName = httpPostedFileBase.FileName;
                        //文件扩展名
                        string extName = Path.GetExtension(fileName);
                        //文件长度
                        if (extName != null && extNames.Contains("|" + extName.ToLower() + "|") == false)
                        {
                            return Json("不支持的文件类型,只支持:" + extNames + "类型的文件！");
                        }
                        //获取文件路径
                        string savePath = Server.MapPath("~/UploadFile");
                        //如果该文件夹不存在则创建该文件夹
                        Directory.CreateDirectory(savePath);
                        //新文件的名字
                        fileName = "Black" + DateTime.Now.ToString("yyyyMMddHHmmss") + "--U" + UserId + "--" + fileName;
                        //文件保存的完整路径
                        savePath = savePath + "//" + fileName;
                        //保存文件
                        httpPostedFileBase.SaveAs(savePath);
                        var dttel = new DataSet();
                        //判断文件类型，用于解析文件
                        if (extName != null)
                        {
                            switch (extName.ToLower())
                            {
                                case ".xlsx":
                                    dttel = ReadExcel(httpPostedFileBase.InputStream, ExcelVersion.Xlsx);
                                    break;
                                case ".xls":
                                    dttel = ReadExcel(httpPostedFileBase.InputStream, ExcelVersion.Xls);
                                    break;
                                case ".csv":
                                    dttel = ReadExcel(httpPostedFileBase.InputStream, ExcelVersion.Csv);
                                    break;
                                default:
                                    return Json("未能解析的文件类型,只支持：" + extNames + "类型的文件！");
                            }
                        }
                        if (dttel!=null)
                        {
                            foreach (DataTable dt in dttel.Tables)
                            {
                                //姓名 性别 手机号 生日 备注 分组 是否提醒
                                for (int i = 0; i < dt.Rows.Count; i++)
                                {
                                    string username = dt.Rows[i][0].ToString();
                                    int sex= dt.Rows[i][1].ToString()=="男" ? 0 : 1;
                                    string phone = dt.Rows[i][2].ToString();
                                    string birthday= dt.Rows[i][3].ToString();
                                    string remark= dt.Rows[i][4].ToString();
                                    //int group= int.Parse(dt.Rows[i][5].ToString());
                                    string isremaind =dt.Rows[i][5].ToString();
                                    books.Add(new Book() { UserName=username,Sex=sex,Msisdn=phone ,BirthdayTime=birthday,GroupId=group,IsRemind=isremaind,UserId=UserId,Remark=remark});
                                }
                            }
                        }
                        if (books.Count>0)
                        {
                            if (bookBll.BatchAddBook(books))
                            {
                                return Json("添加成功");
                            }
                        }
                    }
                }
                return Json("添加失败");
            }
            return Json("添加失败");
        }
        public static DataSet ReadExcel(Stream stream, ExcelVersion version)
        {
            try
            {
                if (version == ExcelVersion.Xls)
                {
                    IExcelDataReader excelReader = ExcelReaderFactory.CreateBinaryReader(stream);
                    return excelReader.AsDataSet();
                }
                if (version == ExcelVersion.Xlsx)
                {
                    IExcelDataReader excelReader = ExcelReaderFactory.CreateOpenXmlReader(stream);
                    return excelReader.AsDataSet();
                }
                if (version == ExcelVersion.Csv)
                {
                    IExcelDataReader csvReader = ExcelReaderFactory.CreateCsvReader(stream);
                    return csvReader.AsDataSet();
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError("ReadExcel,导入文件异常，ex=" + ex);
            }
            return null;
        }
    }
}
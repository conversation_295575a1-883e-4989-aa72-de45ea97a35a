/**
 * Created by fanfa on 2017/5/2.
 */
$(document).ready(function () {
    if ($("select").hasClass("selectpicker1")) {
        $(".selectpicker1").selectpicker({
            noneSelectedText: '请选择生日模板'//默认显示内容，
        });
    }
    
    layer.ready(function () {
    	$("#add").on('click', function() {
    		if($("#group-name").val() == "") {
    			layer.tips('请输入组名', '#group-name', {
					tips: [1, '#0FA6D8']
				});
    		} else {
    			$(this).prop('disabled',true);
    			$.ajax({
    				type : 'post',
                    url: '/ClientBook/AddGroup',
    				data : {
                        name: $("#group-name").val(),
                        templateid: $("#group-template").val()
    				},
    				success : function(data) {
    					layer.msg(data, {end : function() {
    						parent.layer.closeAll();
    						parent.location.reload();//刷新父页面
    					}});
    				}
    			});
    		}
        });
        $("#addtemplate").on('click', function () {
            if ($("#group-name").val() == "") {
                layer.tips('请输入生日模板', '#group-britday', {
                    tips: [1, '#0FA6D8']
                });
            } else {
                $(this).prop('disabled', true);
                $.ajax({
                    type: 'post',
                    url: '/ClientBook/AddTemplate',
                    data: {
                        content: $("#group-britday").val()
                    },
                    success: function (data) {
                        layer.msg(data, {
                            end: function () {
                                parent.layer.closeAll();
                                parent.location.reload();//刷新父页面
                            }
                        });
                    }
                });
            }
        });
        $("#edittemplate").on('click', function () {
            if ($("#group-name").val() == "") {
                layer.tips('请输入生日模板', '#group-britday', {
                    tips: [1, '#0FA6D8']
                });
            } else {
                $(this).prop('disabled', true);
                $.ajax({
                    type: 'post',
                    url: '/ClientBook/TemplateUpdate',
                    data: {
                        tid: $("#tid").val(),
                        template: $("#group-britday").val()
                    },
                    success: function (data) {
                        layer.msg(data, {
                            end: function () {
                                parent.layer.closeAll();
                                parent.location.reload();//刷新父页面
                            }
                        });
                    }
                });
            }
        });
        $("#edit").on('click', function () {
            if ($("#group-name").val() == "") {
                layer.tips('请输入生日模板', '#group-britday', {
                    tips: [1, '#0FA6D8']
                });
            } else {
                $(this).prop('disabled', true);
                $.ajax({
                    type: 'post',
                    url: '/ClientBook/UpdateGroup',
                    data: {
                        groupid: $("#group-id").val(),
                        name: $("#group-name").val(),

                        template: $("#group-template").val()
                    },
                    success: function (data) {
                        layer.msg(data, {
                            end: function () {
                                parent.layer.closeAll();
                                parent.location.reload();//刷新父页面
                            }
                        });
                    }
                });
            }
        });
    });
});
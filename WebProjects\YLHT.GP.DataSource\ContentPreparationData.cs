using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YLHT.GP.Models;
using YLHT.GP.Common.Data;
using YLHT.GP.Common;

namespace YLHT.GP.DataSource
{
    /// <summary>
    /// 内容报备数据实现
    /// </summary>
    public partial class YLHTSmsWebDataSource
    {
        /// <summary>
        /// 获取内容报备列表
        /// </summary>
        /// <param name="cm"></param>
        /// <param name="CurrPage"></param>
        /// <param name="PageSize"></param>
        /// <param name="orderField"></param>
        /// <param name="orderFlag"></param>
        /// <param name="rows"></param>
        /// <param name="pageCount"></param>
        /// <returns></returns>
        public List<ContentPreparationModel> GetContentPreparationList(ContentPreparationModel cm,int CurrPage,int PageSize,string orderField, string orderFlag, out int rows,out int pageCount)
        {
            StringBuilder sb = new StringBuilder();
            StringBuilder sb1 = new StringBuilder();
            StringBuilder sql = new StringBuilder();
            sb.Append("(select bt.*,bu.username,ba.adminname as OperatorAdminName,nvl((select bs.username from base_user bs where bs.userid=bt.operatoruserid),'') as OperatorUserName from base_texttemplate bt left join base_user bu on bu.userid = bt.userid left join base_admin ba on ba.adminid=bt.operatoradminid");
            sb1.Append("select count(bt.id) from base_texttemplate bt " +
                        "left join base_user bu on bu.userid = bt.userid left join base_admin ba on ba.adminid=bt.operatoradminid");
            sb1.Append(" where 1=1");
            sb.Append(" where 1=1");
            try
            {
                if (cm.UserName!=null&&cm.UserName!="")
                {
                    sb.Append(" and instr(bu.username,'"+cm.UserName+"')>0");
                    sb1.Append(" and instr(bu.username,'" + cm.UserName + "')>0");
                }
                if (cm.Text!=null&&cm.Text!="")
                {
                    sb.Append(" and instr(bt.text,'" + cm.Text + "')>0");
                    sb1.Append(" and instr(bt.text,'" + cm.Text + "')>0");

                }
                if (cm.Status!=-1)
                {
                    sb.Append(" and bt.status="+cm.Status);
                    sb1.Append(" and bt.status=" + cm.Status);
                }
                if (cm.UserId!=0)
                {
                    sb.Append(" and bt.userid="+cm.UserId);
                    sb1.Append(" and bt.userid=" + cm.UserId);
                }
                sb.Append(")");
                var rowss=this.QueryRecord<decimal>(sb1.ToString());
                Page pe = GetPage((int)rowss,PageSize);
                rows = pe.Rows;
                pageCount = pe.PageCount;
                sql=Pager(sb.ToString(), "*", CurrPage, pe.PageCount, PageSize, orderField, orderFlag);
                var clist=this.Query<ContentPreparationModel>(sql.ToString());
                Trace.TraceInformation("检索内容报备，sql="+sql.ToString());
                return (List<ContentPreparationModel>)clist;
            }
            catch (Exception ex)
            {
                rows = 0;
                pageCount = 0;
                Trace.TraceError("检索内容报备异常，sql="+sql.ToString()+",ex="+ex);
            }
            return null;
        }

        /// <summary>
        /// 添加内容报备
        /// </summary>
        /// <returns></returns>
        public bool AddContentPreparation(ContentPreparationModel cm)
        {
            try
            {
                   var p = new DynamicParameters(new {
                    userid=cm.UserId,
                    text=cm.Text,
                    status=cm.Status,
                    TEMPLATEID=cm.Templateld,
                    OPERATORUSERID=cm.OperatorUserId,
                    OPERATORADMINID=cm.OperatorAdminId,
                    REMARK=cm.Remark,
                    Priority=cm.Priority
                   });
                return this.Execute("insert into Base_Texttemplate(USERID,TEXT,STATUS,TEMPLATEID,OPERATORUSERID,OPERATORADMINID,REMARK,Priority) values(:USERID,:TEXT,:STATUS,:TEMPLATEID,:OPERATORUSERID,:OPERATORADMINID,:REMARK,:Priority)", p)>0;
            }
            catch (Exception ex)
            {
                Trace.TraceError("添加内容报备异常，ex="+ex);
            }

            return false;
        }
        /// <summary>
        /// 删除内容报备
        /// </summary>
        /// <param name="cid"></param>
        /// <returns></returns>
        public bool DeleteContentPreparation(string []cid)
        {
            try
            {
                if (cid!=null&&cid.Length>0)
                {
                    return this.Execute("delete from base_texttemplate bt where bt.id in (select * from table(fun_split('" + cid.Join(",") + "')))")>0;
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError("删除内容报备异常，ex="+ex);
            }
            return false;
        }
        /// <summary>
        /// 修改内容报备
        /// </summary>
        /// <param name="cm"></param>
        /// <returns></returns>
        public bool UpdateContentPreparation(ContentPreparationModel cm)
        {
            try
            {
                var p = new DynamicParameters(new
                {
                    id=cm.Id,
                    userid = cm.UserId,
                    text = cm.Text,
                    OPERATORADMINID = cm.OperatorAdminId,
                    REMARK = cm.Remark
                });
                return this.Execute("update base_texttemplate set userid=:userid,text=:text,OPERATORADMINID=:OPERATORADMINID,REMARK=:REMARK where id=:id", p) > 0;
            }
            catch (Exception ex)
            {
                Trace.TraceError("修改内容报备异常，ex="+ex);
            }
            return false;
        }
        /// <summary>
        /// 根据获取内容报备Id获取内容报备
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ContentPreparationModel GetContentPreparationById(string id)
        {
            try
            {
                return this.QueryRecord<ContentPreparationModel>("select * from base_texttemplate where id="+id);
            }
            catch (Exception ex)
            {
                Trace.TraceError("根据报备内容id获取报备内容异常，ex="+ex);
            }
            return null;
        }

        public bool UpdateContentStatus(string status, string[] id)
        {
            try
            {
                //if (status=="0")
                //{
                //    return this.Execute("update base_texttemplate set status=1 where id=" + id)>0;
                //}
                //else if(status=="1")
                //{
                //    return this.Execute("update base_texttemplate set status=0 where id=" + id) > 0;
                //}
                return this.Execute("update base_texttemplate set status="+status+" where id in (select * from table(fun_split('" + id.Join(",") + "')))") > 0;
            }
            catch (Exception ex)
            {
                Trace.TraceError("UpdateContentStatus，修改内容报备异常，ex="+ex);
            }
            return false;
        }







        /// <summary>
        /// 获取内容报备列表
        /// </summary>
        /// <param name="cm"></param>
        /// <param name="CurrPage"></param>
        /// <param name="PageSize"></param>
        /// <param name="orderField"></param>
        /// <param name="orderFlag"></param>
        /// <param name="rows"></param>
        /// <param name="pageCount"></param>
        /// <returns></returns>
        public List<ContentPreparationModel> GetContentPreparationLinkList(ContentPreparationModel cm, int CurrPage, int PageSize, string orderField, string orderFlag, out int rows, out int pageCount)
        {
            StringBuilder sb = new StringBuilder();
            StringBuilder sb1 = new StringBuilder();
            StringBuilder sql = new StringBuilder();
            sb.Append("(select bt.*,bu.username,ba.adminname as OperatorAdminName,nvl((select bs.username from base_user bs where bs.userid=bt.operatoruserid),'') as OperatorUserName from base_texttemplateLink bt left join base_user bu on bu.userid = bt.userid left join base_admin ba on ba.adminid=bt.operatoradminid");
            sb1.Append("select count(bt.id) from base_texttemplateLink bt " +
                        "left join base_user bu on bu.userid = bt.userid left join base_admin ba on ba.adminid=bt.operatoradminid");
            sb1.Append(" where 1=1");
            sb.Append(" where 1=1");
            try
            {
                if (cm.UserName != null && cm.UserName != "")
                {
                    sb.Append(" and instr(bu.username,'" + cm.UserName + "')>0");
                    sb1.Append(" and instr(bu.username,'" + cm.UserName + "')>0");
                }
                if (cm.Text != null && cm.Text != "")
                {
                    sb.Append(" and instr(bt.text,'" + cm.Text + "')>0");
                    sb1.Append(" and instr(bt.text,'" + cm.Text + "')>0");

                }
                if (cm.Status != -1)
                {
                    sb.Append(" and bt.status=" + cm.Status);
                    sb1.Append(" and bt.status=" + cm.Status);
                }
                if (cm.UserId != 0)
                {
                    sb.Append(" and bt.userid=" + cm.UserId);
                    sb1.Append(" and bt.userid=" + cm.UserId);
                }
                sb.Append(")");
                var rowss = this.QueryRecord<decimal>(sb1.ToString());
                Page pe = GetPage((int)rowss, PageSize);
                rows = pe.Rows;
                pageCount = pe.PageCount;
                sql = Pager(sb.ToString(), "*", CurrPage, pe.PageCount, PageSize, orderField, orderFlag);
                var clist = this.Query<ContentPreparationModel>(sql.ToString());
                Trace.TraceInformation("检索内容报备链接号码，sql=" + sql.ToString());
                return (List<ContentPreparationModel>)clist;
            }
            catch (Exception ex)
            {
                rows = 0;
                pageCount = 0;
                Trace.TraceError("检索内容报备链接号码异常，sql=" + sql.ToString() + ",ex=" + ex);
            }
            return null;
        }

        /// <summary>
        /// 添加内容报备
        /// </summary>
        /// <returns></returns>
        public bool AddContentPreparationLink(ContentPreparationModel cm)
        {
            try
            {
                var p = new DynamicParameters(new
                {
                    userid = cm.UserId,
                    text = cm.Text,
                    status = cm.Status,
                    TEMPLATEID = cm.Templateld,
                    OPERATORUSERID = cm.OperatorUserId,
                    OPERATORADMINID = cm.OperatorAdminId,
                    REMARK = cm.Remark,
                    Priority = cm.Priority
                });
                return this.Execute("insert into Base_TexttemplateLink(USERID,TEXT,STATUS,TEMPLATEID,OPERATORUSERID,OPERATORADMINID,REMARK,Priority) values(:USERID,:TEXT,:STATUS,:TEMPLATEID,:OPERATORUSERID,:OPERATORADMINID,:REMARK,:Priority)", p) > 0;
            }
            catch (Exception ex)
            {
                Trace.TraceError("添加内容报备链接号码异常，ex=" + ex);
            }

            return false;
        }
        /// <summary>
        /// 删除内容报备
        /// </summary>
        /// <param name="cid"></param>
        /// <returns></returns>
        public bool DeleteContentPreparationLink(string[] cid)
        {
            try
            {
                if (cid != null && cid.Length > 0)
                {
                    return this.Execute("delete from base_texttemplateLink bt where bt.id in (select * from table(fun_split('" + cid.Join(",") + "')))") > 0;
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError("删除内容报备链接号码异常，ex=" + ex);
            }
            return false;
        }
        /// <summary>
        /// 修改内容报备
        /// </summary>
        /// <param name="cm"></param>
        /// <returns></returns>
        public bool UpdateContentPreparationLink(ContentPreparationModel cm)
        {
            try
            {
                var p = new DynamicParameters(new
                {
                    id = cm.Id,
                    userid = cm.UserId,
                    text = cm.Text,
                    OPERATORADMINID = cm.OperatorAdminId,
                    REMARK = cm.Remark
                });
                return this.Execute("update base_texttemplateLink set userid=:userid,text=:text,OPERATORADMINID=:OPERATORADMINID,REMARK=:REMARK where id=:id", p) > 0;
            }
            catch (Exception ex)
            {
                Trace.TraceError("修改内容报备链接号码异常，ex=" + ex);
            }
            return false;
        }
        /// <summary>
        /// 根据获取内容报备Id获取内容报备
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ContentPreparationModel GetContentPreparationLinkById(string id)
        {
            try
            {
                return this.QueryRecord<ContentPreparationModel>("select * from base_texttemplateLink where id=" + id);
            }
            catch (Exception ex)
            {
                Trace.TraceError("根据报备内容链接号码id获取报备内容异常，ex=" + ex);
            }
            return null;
        }

        public bool UpdateContentLinkStatus(string status, string[] id)
        {
            try
            {
                //if (status=="0")
                //{
                //    return this.Execute("update base_texttemplate set status=1 where id=" + id)>0;
                //}
                //else if(status=="1")
                //{
                //    return this.Execute("update base_texttemplate set status=0 where id=" + id) > 0;
                //}
                return this.Execute("update base_texttemplateLink set status=" + status + " where id in (select * from table(fun_split('" + id.Join(",") + "')))") > 0;
            }
            catch (Exception ex)
            {
                Trace.TraceError("UpdateContentStatus，修改内容报备链接号码异常，ex=" + ex);
            }
            return false;
        }

        /// <summary>
        /// 批量检查文本内容是否已存在于Base_TexttemplateLink表中
        /// </summary>
        /// <param name="textList">要检查的文本列表</param>
        /// <returns>已存在的文本列表</returns>
        public List<string> CheckExistingTexts(List<string> textList)
        {
            try
            {
                if (textList == null || textList.Count == 0)
                    return new List<string>();

                // 将文本列表转换为Oracle可用的格式，使用参数化查询避免SQL注入
                var existingTexts = new List<string>();

                // 分批处理，每次最多处理1000条，避免Oracle IN子句限制
                const int batchSize = 1000;
                for (int i = 0; i < textList.Count; i += batchSize)
                {
                    var batch = textList.Skip(i).Take(batchSize).ToList();
                    if (batch.Count == 0) continue;

                    // 构建参数化查询
                    var parameters = new DynamicParameters();
                    var inClause = new List<string>();

                    for (int j = 0; j < batch.Count; j++)
                    {
                        var paramName = $"text{j}";
                        parameters.Add(paramName, batch[j]);
                        inClause.Add($":{paramName}");
                    }

                    var sql = $"SELECT DISTINCT TEXT FROM base_texttemplateLink WHERE TEXT IN ({string.Join(",", inClause)})";
                    var batchResults = this.Query<string>(sql, parameters);

                    if (batchResults != null)
                    {
                        existingTexts.AddRange(batchResults);
                    }
                }

                Trace.TraceInformation($"批量检查文本是否存在：检查{textList.Count}条，发现{existingTexts.Count}条已存在");
                return existingTexts;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"批量检查文本是否存在异常，ex={ex}");
                return new List<string>(); // 出错时返回空列表，不影响主流程
            }
        }

        /// <summary>
        /// 批量添加内容报备链接号码模板（忽略重复）
        /// </summary>
        /// <param name="templates">模板列表</param>
        /// <returns>添加结果</returns>
        public BatchImportResult BatchAddContentPreparationLink(List<ContentPreparationModel> templates)
        {
            var result = new BatchImportResult();

            try
            {
                if (templates == null || templates.Count == 0)
                {
                    result.Message = "没有要导入的数据";
                    return result;
                }

                // 分批处理，每次处理500条，避免Oracle参数限制
                const int batchSize = 500;
                for (int i = 0; i < templates.Count; i += batchSize)
                {
                    var batch = templates.Skip(i).Take(batchSize).ToList();
                    var batchResult = ProcessBatch(batch);

                    result.TotalCount += batchResult.TotalCount;
                    result.SuccessCount += batchResult.SuccessCount;
                    result.SkippedCount += batchResult.SkippedCount;
                    result.ErrorCount += batchResult.ErrorCount;
                    result.Errors.AddRange(batchResult.Errors);
                }

                result.Success = true;
                result.Message = $"导入完成：总计{result.TotalCount}条，成功{result.SuccessCount}条，跳过{result.SkippedCount}条，失败{result.ErrorCount}条";

                Trace.TraceInformation($"批量添加内容报备链接号码模板完成：{result.Message}");
                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = "批量导入异常：" + ex.Message;
                result.ErrorCount = templates.Count;
                Trace.TraceError($"批量添加内容报备链接号码模板异常，ex={ex}");
                return result;
            }
        }

        /// <summary>
        /// 处理单个批次的数据
        /// </summary>
        private BatchImportResult ProcessBatch(List<ContentPreparationModel> batch)
        {
            var result = new BatchImportResult();
            result.TotalCount = batch.Count;

            try
            {
                // 1. 检查哪些数据已存在（基于text和userid的组合唯一键）
                var existingCheck = CheckExistingTemplatesByUserAndText(batch);
                var existingKeys = new HashSet<string>(existingCheck);

                // 2. 过滤出不存在的数据
                var newTemplates = batch.Where(t =>
                    !existingKeys.Contains($"{t.UserId}_{t.Text?.Trim()}")
                ).ToList();

                result.SkippedCount = batch.Count - newTemplates.Count;

                // 3. 批量插入新数据
                if (newTemplates.Count > 0)
                {
                    foreach (var template in newTemplates)
                    {
                        try
                        {
                            if (AddContentPreparationLink(template))
                            {
                                result.SuccessCount++;
                            }
                            else
                            {
                                result.ErrorCount++;
                                result.Errors.Add($"添加失败：用户ID{template.UserId}，内容：{template.Text?.Substring(0, Math.Min(20, template.Text?.Length ?? 0))}...");
                            }
                        }
                        catch (Exception ex)
                        {
                            result.ErrorCount++;
                            result.Errors.Add($"添加异常：{ex.Message}");
                        }
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                result.ErrorCount = batch.Count;
                result.Errors.Add($"批次处理异常：{ex.Message}");
                Trace.TraceError($"处理批次数据异常，ex={ex}");
                return result;
            }
        }

        /// <summary>
        /// 检查模板是否已存在（基于userid和text组合）
        /// </summary>
        private List<string> CheckExistingTemplatesByUserAndText(List<ContentPreparationModel> templates)
        {
            try
            {
                if (templates == null || templates.Count == 0)
                    return new List<string>();

                var existingKeys = new List<string>();

                // 分批查询，避免Oracle IN子句限制
                const int checkBatchSize = 1000;
                for (int i = 0; i < templates.Count; i += checkBatchSize)
                {
                    var checkBatch = templates.Skip(i).Take(checkBatchSize).ToList();
                    if (checkBatch.Count == 0) continue;

                    // 构建参数化查询
                    var parameters = new DynamicParameters();
                    var conditions = new List<string>();

                    for (int j = 0; j < checkBatch.Count; j++)
                    {
                        var template = checkBatch[j];
                        if (string.IsNullOrEmpty(template.Text?.Trim())) continue;

                        var userIdParam = $"userid{j}";
                        var textParam = $"text{j}";

                        parameters.Add(userIdParam, template.UserId);
                        parameters.Add(textParam, template.Text.Trim());

                        conditions.Add($"(USERID = :{userIdParam} AND TEXT = :{textParam})");
                    }

                    if (conditions.Count > 0)
                    {
                        var sql = $"SELECT USERID || '_' || TEXT as UserText FROM base_texttemplateLink WHERE {string.Join(" OR ", conditions)}";
                        var batchResults = this.Query<string>(sql, parameters);

                        if (batchResults != null)
                        {
                            existingKeys.AddRange(batchResults);
                        }
                    }
                }

                return existingKeys;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"检查模板是否存在异常，ex={ex}");
                return new List<string>(); // 出错时返回空列表，不影响主流程
            }
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using YLHT.GP.Common;
using System.IO;
using YLHT.GP.Models;
using YLHT_GP_Business.Business;
using NAudio.Wave;
using NAudio.MediaFoundation;
using RestSharp;
using Newtonsoft.Json;
using YLHT_GP_Business;

namespace YLHT.GP.Client.Controllers
{
    public class VoiceController : BaseController
    {
        ClientBLL clientBLL = new ClientBLL();
        VoiceBll VoiceBll = new VoiceBll();
        public ActionResult SendVoice()
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                if (showmenu.Contains(UserMenu.VoiceSend.ToInt()))
                {
                    var us = clientBLL.GetCustomerById(UserId.ToString());
                    GP.Models.VoiceTemplate voiceTemplate = new GP.Models.VoiceTemplate() { UserId=UserId,Status = 1, PageSize = 1000000000, CurrPage = 1, ServiceType = 1 };
                    VoiceBll.GetVoiceTemplates(voiceTemplate);
                    List<SelectListItem> smsText = new List<SelectListItem>();
                    smsText.Add(new SelectListItem { Text = "全部", Value = "-1" });
                    smsText.AddRange(voiceTemplate.DataList.Select(r => new SelectListItem { Text = r.TemplateName, Value = r.TemplateId + "" }));
                    ViewBag.Smss = smsText;

                    List<SelectListItem> voiceText = new List<SelectListItem>();
                    voiceTemplate.ServiceType = 2;
                    VoiceBll.GetVoiceTemplates(voiceTemplate);
                    voiceText.Add(new SelectListItem { Text = "全部", Value = "-1" });
                    voiceText.AddRange(voiceTemplate.DataList.Select(r => new SelectListItem { Text = r.TemplateName, Value = r.TemplateId + "" }));
                    ViewBag.Voices = voiceText;
                    return View(us);
                }
                return Redirect("/Account/Login");
            }
            return Redirect("/Account/Login");
        }
        [HttpPost]
        public JsonResult SendSmsVoice(SendSmsVoice sendSmsVoice)
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                try
                {
                    if (sendSmsVoice.Mobile != null && sendSmsVoice.Mobile.Split(",").Count() > 10000)
                    {
                        return Json(new { Code = 2, Description = "号码不能超过1万条" });
                    }
                    var us = clientBLL.GetCustomerById(UserId.ToString());
                    
                    var url = ConfigurationManager.AppSettings["InternalServiceUrl"];
                    InternalService.InternalService interser = new InternalService.InternalService();
                    interser.Url = url;
                    var ary = sendSmsVoice.Mobile.Trim().Replace("\r\n", ",").Replace(" ", ",").Replace("，", ",").Replace("\r", ",").Replace("\n", ",").Split(',', StringSplitOptions.RemoveEmptyEntries);
                    if (ary == null || ary.Length < 1)
                    {
                        return Json(new { Code = 2, Description = "号码为空！" });
                    }
                    if (!ary.Any(x => x.Length == 11))
                    {
                        return Json(new { Code = 2, Description = "号码错误！" });
                    }
                    if (VoiceBll.GetVoiceTemplate(sendSmsVoice.SmsTemplate)==null)
                    {
                        return Json(new { Code = 2, Description = "短信模板错误！" });
                    }
                    if (VoiceBll.GetVoiceTemplate(sendSmsVoice.VoiceTemplate) == null)
                    {
                        return Json(new { Code = 2, Description = "语音模板错误！" });
                    }
                    var timespan = NativeMethods.GetTimeStamp();
                    var sercet = NativeMethods.Md5Encrypt(us.ToKen + timespan);
                    InternalService.SendResult sendResult = interser.SendVoiceSms(us.UserId, us.UserName, sercet, timespan, sendSmsVoice.Mobile,sendSmsVoice.PlanTime, sendSmsVoice.TaskId,sendSmsVoice.SmsTemplate,sendSmsVoice.VoiceTemplate,sendSmsVoice.TriggerSms);
                    if (sendResult.StatusCode != InternalService.StatusCode.Success)
                    {
                        return Json(new { Code = sendResult.StatusCode, Description = sendResult.Description });
                    }
                    else
                    {
                        return Json(new
                        {
                            Code =1,
                            Description="发送成功"
                        });
                    }
                }
                catch (Exception ex)
                {
                    Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name},SendSmsVoice,ex={ex}");
                    return Json(new { Code = 2, Description = "后台异常" });
                }
            }
            return Json(new { Code = 2, Description = "您的身份已失效，请重新登录" });
        }
        public ActionResult VoiceTemplate(VoiceTemplate  voiceTemplate,int currPage=1)
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                if (showmenu.Contains(UserMenu.VoiceTemplate.ToInt()))
                {
                    voiceTemplate.CurrPage = currPage;
                    voiceTemplate.ServiceType = 2;
                    voiceTemplate.PageSize = 50;
                    voiceTemplate.UserId = UserId;
                    VoiceBll.GetVoiceTemplates(voiceTemplate);
                    
                    if (currPage > voiceTemplate.PageCount)
                    {
                        currPage = voiceTemplate.PageCount;
                    }
                    if (currPage < 0 && voiceTemplate.PageCount != 0)
                    {
                        currPage = 1;
                    }
                    ViewBag.curpage = currPage;
                    ViewBag.allPage = voiceTemplate.PageCount;
                    ViewBag.allSize = voiceTemplate.Rows;
                    return View(voiceTemplate);
                }
            }
            return Redirect("/Account/Login");
        }
        
        public ActionResult VoiceTemplateAdd()
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                if (showmenu.Contains(UserMenu.VoiceTemplate.ToInt()))
                {
                    return View();
                }
            }
            return Redirect("/Account/Login");
        }
        [HttpPost]
        public JsonResult VoiceTemplateAdd1(VoiceTemplate voiceTemplate)
        {
                string msg = "{\"Code\":\"{0}\",\"msg\":\"{1}\"}";
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                
                {
                    try
                    {

                    
                    if (voiceTemplate.ServiceType == 2)
                    {
                        if (showmenu.Contains(UserMenu.VoiceTemplate.ToInt()))
                        {
                            if (Request.Files.Count > 0)
                            {
                                var httpPostedFileBase = Request.Files[0];
                                if (httpPostedFileBase != null && (Request.Files.Count > 0 && httpPostedFileBase.ContentLength > 0))
                                {
                                    string fileName = httpPostedFileBase.FileName;
                                    string parentDic = ConfigurationManager.AppSettings["VoiceFilePath"] + "VoiceFiles";
                                        string outputFile = ConfigurationManager.AppSettings["VoiceOutFilePath"];
                                        if (!Directory.Exists(parentDic))
                                    {
                                        Directory.CreateDirectory(parentDic);
                                    }
                                        string ra = Guid.NewGuid().ToString("N");
                                        fileName = ra + fileName;
                                        string savePath = parentDic + "\\" + fileName;


                                        httpPostedFileBase.SaveAs(savePath);
                                    voiceTemplate.FilePath = savePath;
                                    voiceTemplate.FileName = fileName;
                                        //获取文件路径
                                        string savePath1 = Server.MapPath("~/UploadFile");
                                        //如果该文件夹不存在则创建该文件夹
                                        Directory.CreateDirectory(savePath1);
                                        string SaveFilePath = savePath1 + "\\" + fileName;

                                        string filename3 = outputFile+ ra + ".wav";

                                        string arg = "-i " + savePath + " -acodec libmp3lame -ac 2 "+ filename3;
                                        if(CMD(arg,new DataReceivedEventHandler(Output)))
                                        {
                                            voiceTemplate.WebAddress = ConfigurationManager.AppSettings["WebPath"].ToString() + "/UploadFile/" + ra + ".mp3";
                                        }
                                        else
                                        {
                                            Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name},添加模板：{voiceTemplate},失败,转换文件失败");
                                            return Json(2);
                                        }
                                        //httpPostedFileBase.SaveAs(SaveFilePath);
                                        
                                }
                                else
                                {
                                    Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name},添加模板：{voiceTemplate},失败,没有录音文件");
                                    return Json(2);
                                }
                            }
                            voiceTemplate.UserId = UserId;
                            voiceTemplate.ServiceType = 2;
                            if (VoiceBll.AddVoiceTemplate(voiceTemplate))
                            {
                                Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name},添加模板：{voiceTemplate},成功");
                                return Json(1);
                            }
                            else
                            {
                                Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name},添加模板：{voiceTemplate},失败");
                                return Json(2);
                            }
                        }
                    }
                    else if (voiceTemplate.ServiceType == 1) {
                        voiceTemplate.UserId = UserId;
                        if (showmenu.Contains(UserMenu.VoiceSmsTemplate.ToInt()))
                        {
                            voiceTemplate.ServiceType = 1;
                            if (string.IsNullOrWhiteSpace(voiceTemplate.Text))
                            {
                                Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name},添加模板：{voiceTemplate},失败，Text为空");
                                return Json(2);
                            }
                            if (VoiceBll.AddVoiceTemplate(voiceTemplate))
                            {
                                Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name},添加模板：{voiceTemplate},成功");
                                return Json(1);
                            }
                            else
                            {
                                Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name},添加模板：{voiceTemplate},失败");
                                return Json(2);
                            }
                        }
                    }
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError("UserId="+UserId+",UserName="+User.Identity.Name+"报备语音模板异常，ex="+ex);
                    }
                    return Json(2);
                }
            }
            return Json(3);
        }
        [HttpPost]
        public JsonResult VoiceTemplateAdd(VoiceTemplate voiceTemplate)
        {
            string msg = "{\"Code\":\"{0}\",\"msg\":\"{1}\"}";
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {

                {
                    try
                    {


                        if (voiceTemplate.ServiceType == 2)
                        {
                            if (showmenu.Contains(UserMenu.VoiceTemplate.ToInt()))
                            {
                                if (Request.Files.Count > 0)
                                {
                                    var httpPostedFileBase = Request.Files[0];
                                    if (httpPostedFileBase != null && (Request.Files.Count > 0 && httpPostedFileBase.ContentLength > 0))
                                    {
                                        string fileName = httpPostedFileBase.FileName;
                                        string parentDic = ConfigurationManager.AppSettings["VoiceFilePath"] + "VoiceFiles";
                                        string outputFile = ConfigurationManager.AppSettings["VoiceOutFilePath"];
                                        if (!Directory.Exists(parentDic))
                                        {
                                            Directory.CreateDirectory(parentDic);
                                        }
                                        string ra = Guid.NewGuid().ToString("N");
                                        fileName = ra + fileName;
                                        string savePath = parentDic + "\\" + fileName;


                                        httpPostedFileBase.SaveAs(savePath);
                                        voiceTemplate.FilePath = savePath;
                                        voiceTemplate.FileName = fileName;
                                        //获取文件路径
                                        string savePath1 = Server.MapPath("~/UploadFile");
                                        //如果该文件夹不存在则创建该文件夹
                                        Directory.CreateDirectory(savePath1);
                                        string SaveFilePath = savePath1 + "\\" + fileName;

                                        string filename3 = outputFile + ra + ".wav";

                                        //string arg = "-i " + savePath + " -acodec libmp3lame -ac 2 " + filename3;
                                        string arg = "-i " + savePath + "  -ac 1 -ar 8000 " + filename3;
                                        if (CMD(arg, new DataReceivedEventHandler(Output)))
                                        {
                                            //voiceTemplate.WebAddress = ConfigurationManager.AppSettings["WebPath"].ToString() + "/UploadFile/" + ra + ".mp3";
                                            voiceTemplate.WebAddress = ConfigurationManager.AppSettings["WebPath"].ToString() + "/UploadFile/" + ra + ".wav";
                                            var result=FilePost(ConfigurationManager.AppSettings["RemoteVoiceFile"].ToString(), filename3,out string s);
                                            if (result!=null)
                                            {
                                                if (result.Code!=200)
                                                {
                                                    Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name},添加模板：{voiceTemplate},失败,远程报备失败，返回：{s}");
                                                    return
                                                        Json(2);
                                                }
                                            }
                                            else
                                            {
                                                Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name},添加模板：{voiceTemplate},失败,远程报备失败，返回：null");
                                                return Json(2);
                                            }
                                        }
                                        else
                                        {
                                            return Json(2);
                                        }
                                        //httpPostedFileBase.SaveAs(SaveFilePath);

                                    }
                                    else
                                    {
                                        Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name},添加模板：{voiceTemplate},失败,没有录音文件");
                                        return Json(2);
                                    }
                                }
                                voiceTemplate.UserId = UserId;
                                voiceTemplate.ServiceType = 2;
                                if (VoiceBll.AddVoiceTemplate(voiceTemplate))
                                {
                                    Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name},添加模板：{voiceTemplate},成功");
                                    return Json(1);
                                }
                                else
                                {
                                    Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name},添加模板：{voiceTemplate},失败");
                                    return Json(2);
                                }
                            }
                        }
                        else if (voiceTemplate.ServiceType == 1)
                        {
                            voiceTemplate.UserId = UserId;
                            if (showmenu.Contains(UserMenu.VoiceSmsTemplate.ToInt()))
                            {
                                voiceTemplate.ServiceType = 1;
                                if (string.IsNullOrWhiteSpace(voiceTemplate.Text))
                                {
                                    Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name},添加模板：{voiceTemplate},失败，Text为空");
                                    return Json(2);
                                }
                                if (VoiceBll.AddVoiceTemplate(voiceTemplate))
                                {
                                    Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name},添加模板：{voiceTemplate},成功");
                                    return Json(1);
                                }
                                else
                                {
                                    Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name},添加模板：{voiceTemplate},失败");
                                    return Json(2);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError("UserId=" + UserId + ",UserName=" + User.Identity.Name + "报备语音模板异常，ex=" + ex);
                    }
                    return Json(2);
                }
            }
            return Json(3);
        }
        private OutPut FilePost(string url,string filepath,out string resu)
        {
            var client = new RestClient(url);
            client.Timeout = -1;
            var request = new RestRequest(Method.POST);
            request.AddFile("file", filepath);
            IRestResponse response = client.Execute(request);
            var result = response.Content;
            if (!string.IsNullOrEmpty(result)||!string.IsNullOrWhiteSpace(result))
            {
                OutPut ouput = JsonConvert.DeserializeObject<OutPut>(result);
                resu = result;
                return ouput;
            }
            resu = result;
            return null;
        }

        private bool CMD(string strArg, DataReceivedEventHandler eventHandler)
        {
            try
            {
                //string ffmpeg = @"C:\Users\<USER>\Downloads\ffmpeg-18639\ffmpeg.exe";
                string ffmpeg = Server.MapPath(@"~\ffmpeg.exe");
                Process ProCMD = new Process();
                ProCMD.StartInfo.FileName = ffmpeg;
                ProCMD.StartInfo.Arguments = strArg;

                ProCMD.StartInfo.UseShellExecute = false;
                ProCMD.StartInfo.RedirectStandardError = true;
                ProCMD.StartInfo.CreateNoWindow = true;
                ProCMD.EnableRaisingEvents = true;
                ProCMD.ErrorDataReceived += eventHandler;
                //ProCMD.Exited += eventHandler;
                ProCMD.Start();

                ProCMD.BeginErrorReadLine();
                ProCMD.WaitForExit();
                ProCMD.Close();
                ProCMD.Dispose();
                return true;
            }
            catch (Exception e)
            {
                Trace.TraceError("CMd,ex="+e);
                return false;
            }
        }
        private void Process_Exited(object sender, EventArgs e)
        {

        }
        private void Output(object sendProcess, DataReceivedEventArgs dataReceivedEventArgs)
        {
            try
            {
                //string path = Server.MapPath("~/ffoutput.txt");`
                //if (!String.IsNullOrEmpty(dataReceivedEventArgs.Data))
                //{
                //    if (System.IO.File.Exists(path))
                //    {
                //        System.IO.File.Create(path);
                //    }
                //    FileStream aFile = new FileStream(path, FileMode.Append);
                //    StreamWriter sw = new StreamWriter(aFile);
                //    sw.WriteLine(dataReceivedEventArgs.Data);
                //    sw.Close();
                //}
            }
            catch (IOException e)
            {
                Trace.TraceError("Output,ex="+e);
            }
        }
        [HttpPost]
        public JsonResult VoiceTemplateDelete(int templateId)
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                if (showmenu.Contains(UserMenu.VoiceTemplate.ToInt()))
                {
                    if (templateId>0)
                    {
                        VoiceTemplate voiceTemplate= VoiceBll.GetVoiceTemplate(templateId);
                        if (voiceTemplate.Status==1)
                        {
                            if (VoiceBll.DeleteVoiceTemplate(templateId))
                            {
                                Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name}，删除模板：{voiceTemplate}，成功");
                                return Json(1);
                            }
                            else
                            {
                                Trace.TraceInformation($"UserId={UserId},UserName={User.Identity.Name}，删除模板：{voiceTemplate}，失败");
                                return Json(2);
                            }
                        }
                        else
                        {
                            return Json(2);
                        }
                    }
                    else
                    {
                        return Json(3);
                    }
                }
            }
            return Json(4);
        }





        public ActionResult VoiceSmsTemplate(VoiceTemplate voiceTemplate,int currPage=1)
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                try
                {
                    if (showmenu.Contains(UserMenu.VoiceSmsTemplate.ToInt()))
                    {
                        voiceTemplate.CurrPage = currPage;
                        voiceTemplate.ServiceType = 1;
                        voiceTemplate.UserId = UserId;
                        voiceTemplate.PageSize = 50;
                        VoiceBll.GetVoiceTemplates(voiceTemplate);

                        if (currPage > voiceTemplate.PageCount)
                        {
                            currPage = voiceTemplate.PageCount;
                        }
                        if (currPage < 0 && voiceTemplate.PageCount != 0)
                        {
                            currPage = 1;
                        }
                        
                        ViewBag.curpage = currPage;
                        ViewBag.allPage = voiceTemplate.PageCount;
                        ViewBag.allSize = voiceTemplate.Rows;
                        return View(voiceTemplate);
                    }
                }
                catch (Exception ex)
                {
                    Trace.TraceError("UserId=" + UserId + ",UserName=" + User.Identity.Name + ",VoiceSmsTemplate,ex=" + ex);
                }
            }
            return Redirect("/Account/Login");
        }
        public ActionResult VoiceSmsTemplateAdd()
        {
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                if (showmenu.Contains(UserMenu.VoiceSmsTemplate.ToInt()))
                {
                    return View(clientBLL.GetCustomerById(UserId.ToString()));
                }
            }
            return Redirect("/Account/Login");
        }



        public ViewResult QueryVoiceDetail(VoiceDetails md, int currpage = 0, int PageSize = 50)
        {
            md.UserId = UserId;
            md.UserName = User.Identity.Name;
            int rows = 0;
            int pages = 0;
           
            if (currpage <= 0)
            {
                currpage = 1;
            }
            md.CurrPage = currpage;
            md.PageSize = PageSize;
            if (Request.RequestType != "GET")
            {
                
                if (md.TaskId != null && md.TaskId != "")
                {
                    md.QueryCount = 0;
                    ViewBag.DetailList = VoiceBll.GetVoiceDetails(md);
                    pages = md.PageCount;
                    rows = md.Rows;
                    if (pages < currpage)
                    {
                        currpage = pages;
                    }
                    if (pages != 0 && currpage <= 0)
                    {
                        currpage = 1;
                    }
                }
                else
                {
                    md.QueryCount = 1;
                    ViewBag.DetailList = VoiceBll.GetVoiceDetails(md);
                }
            }
            else if (md.TaskId != null && md.TaskId != "")
            {
                if (currpage <= 0)
                {
                    currpage = 1;
                }
                md.CurrPage = currpage;
                md.PageSize = PageSize;
                md.QueryCount = 0;
                ViewBag.DetailList = VoiceBll.GetVoiceDetails(md);
                pages = md.PageCount;
                rows = md.Rows;
                if (pages < currpage)
                {
                    currpage = pages;
                }
                if (pages != 0 && currpage <= 0)
                {
                    currpage = 1;
                }
            }

            //当前页数
            ViewBag.curpage = currpage;
            //总条数
            ViewBag.allsize = rows;
            //总页数
            ViewBag.allPage = pages;
            ViewBag.PageSize = PageSize;
            return View(md);
        }
        public JsonResult QueryVoiceDetailRowsPage(VoiceDetails md, int currpage = 0, int PageSize = 50)
        {
            md.UserId = UserId;
            md.QueryCount = 2;
            md.CurrPage = currpage;
            md.PageSize = PageSize;
            int rows = 0;
            int pages = 0;
            if (currpage <= 0)
            {
                currpage = 1;
            }
            VoiceBll.GetVoiceDetails(md);
            rows = md.Rows;
            pages = md.PageCount;
            if (pages < currpage)
            {
                currpage = pages;
            }
            if (pages != 0 && currpage <= 0)
            {
                currpage = 1;
            }
            return Json(new { Rows = rows, PageCount = pages });
        }



        public ViewResult QueryVoiceTask(MsgVoiceTask mt, int currpage = 0, int PageSize = 50)
        {
            mt.UserId = UserId;
            mt.UserName = User.Identity.Name;
            mt.ServiceType = 8;
            int rows = 0;
            int pages = 0;
            if (currpage <= 0)
            {
                currpage = 1;
            }
            if (Request.RequestType != "GET")
            {
                
                if (mt.TaskId != null && mt.TaskId != "")
                {
                    mt.QueryRows = 0;
                    mt.PageSize = PageSize;
                    mt.CurrPage = currpage;
                    ViewBag.TaskList = VoiceBll.GetMsgVoiceTasks(mt);
                    pages = mt.PageCount;
                    rows = mt.Rows;
                    if (currpage > pages)
                    {
                        currpage = rows;
                    }
                    if (pages != 0 && currpage <= 0)
                    {
                        currpage = 1;
                    }
                }
                else
                {
                    mt.QueryRows = 1; 
                    mt.PageSize = PageSize;
                    mt.CurrPage = currpage;
                    ViewBag.TaskList = VoiceBll.GetMsgVoiceTasks(mt);
                }
            }

            //当前页数
            ViewBag.curpage = currpage;
            //总条数
            ViewBag.allSize = rows;
            //总页数
            ViewBag.allPage = pages;
            ViewBag.PageSize = PageSize;
            return View(mt);
        }
        /// <summary>
        /// 获取短信查询总条数和总页数
        /// </summary>
        /// <param name="mt"></param>
        /// <param name="currpage"></param>
        /// <param name="PageSize"></param>
        /// <returns></returns>
        public JsonResult QuerySmsRowPageCount(MsgVoiceTask mt, int currpage = 0, int PageSize = 50)
        {
            mt.UserId = UserId;
            int rows = 0;
            int pages = 0;
            mt.QueryRows = 2;
            if (currpage <= 0)
            {
                currpage = 1;
            }
            VoiceBll.GetMsgVoiceTasks(mt);
            if (currpage > pages)
            {
                currpage = rows;
            }
            if (pages != 0 && currpage <= 0)
            {
                currpage = 1;
            }
            return Json(new { Rows = rows, PageCount = pages });
        }


        public JsonResult SendSmsTemplate(string templateId,string mobile)
        {
            
            try
            {
                SendSmsModel ssm = new SendSmsModel();
                if (Request.IsAuthenticated && Session["UserID"] != null)
                {
                    if (!string.IsNullOrEmpty(templateId) && !string.IsNullOrWhiteSpace(templateId) && int.TryParse(templateId, out int tempId))
                    {

                        if (tempId > 0)
                        {
                            VoiceTemplate voiceTemplate= VoiceBll.GetVoiceTemplate(tempId);
                            if (!string.IsNullOrWhiteSpace(voiceTemplate.Text)&&!string.IsNullOrEmpty(voiceTemplate.Text))
                            {
                                ssm.Content = voiceTemplate.Text;
                                ssm.Mobile = mobile;
                                int.TryParse(ConfigurationManager.AppSettings["SubmitMaxCount"], out int a);
                                if (a <= 0)
                                {
                                    a = 100000;
                                }
                                if (ssm.Mobile != null && ssm.Mobile.Split(",").Count() > a)
                                {
                                    return Json(new { Code = 2, Description = "号码不能超过十万条" });
                                }
                                //string UPassWord = NativeMethods.DESDecrypt(Convert.ToString(CookiesHelper.GetCookie("YLHTClientInfo").Values["sPassWord"]));//NativeMethods.Md532Encrypt(NativeMethods.DESDecrypt(Convert.ToString(CookiesHelper.GetCookie("YLHTClientInfo").Values["sPassWord"]))).ToUpper();
                                //string UPassWord= Convert.ToString(CookiesHelper.GetCookie("YLHTClientInfo").Values["sPassWord"]);
                                //ClientBLL clientBLL = new ClientBLL();
                                var us = clientBLL.GetCustomerById(UserId.ToString());
                                ssm.UserId = UserId;
                                ssm.Account = User.Identity.Name;
                                ssm.ExtNumber = "";
                                ssm.PassWord = us.ToKen;
                                ssm.TaskId = "";
                                var url = ConfigurationManager.AppSettings["InternalServiceUrl"];
                                InternalService.InternalService interser = new InternalService.InternalService();
                                interser.Url = url;

                                //InternalService.NResult sendResult = interser.SendSms1(ssm.UserId, ssm.Account, sercet, timespan, ssm.Mobile, ssm.Content, ssm.PlanTime, ssm.ExtNumber, ssm.TaskId);
                                InternalService.ServiceType serviceType = InternalService.ServiceType.Sms;
                                switch (ssm.SmsType)
                                {
                                    case 1:
                                        serviceType = InternalService.ServiceType.Sms;
                                        break;
                                    case 4:
                                        serviceType = InternalService.ServiceType.Fms;
                                        break;
                                    default:
                                        break;
                                }
                                var ary = ssm.Mobile.Trim().Replace("\r\n", ",").Replace(" ", ",").Replace("，", ",").Replace("\r", ",").Replace("\n", ",").Split(',', StringSplitOptions.RemoveEmptyEntries);
                                if (ary == null || ary.Length < 1)
                                {
                                    return Json(new { Code = 2, Description = "号码为空！" });
                                }
                                if (!ary.Any(x => x.Length == 11))
                                {
                                    return Json(new { Code = 2, Description = "号码错误！" });
                                }
                                ssm.Content = ssm.Content.Trim();
                                if (ssm.Content.Length > 2000)
                                {
                                    return Json(new { Code = 2, Description = "内容太长！" });
                                }
                                int i = 0;
                                us.SUBMITPACKAGE = us.SUBMITPACKAGE > 3000 ? 3000 : (us.SUBMITPACKAGE <= 0 ? 3000 : us.SUBMITPACKAGE);
                                List<List<string>> msisdnitem = SmsOperationController.GetListGroup(ary.ToList(), us.SUBMITPACKAGE);
                                Trace.TraceInformation($"拆分后，msisdnitemCount={msisdnitem.Count}");
                                foreach (var item in msisdnitem)
                                {
                                    var timespan = NativeMethods.GetTimeStamp();
                                    var sercet = NativeMethods.Md5Encrypt(ssm.PassWord + timespan);
                                    InternalService.NResult sendResult = interser.SendSmsByServiceType(ssm.UserId, ssm.Account, sercet, timespan, item.Join(","), ssm.Content, ssm.PlanTime, ssm.ExtNumber, ssm.TaskId, serviceType);
                                    if (sendResult.StatusCode != InternalService.StatusCode.Success)
                                    {
                                        return Json(new { Code = sendResult.StatusCode, Description = sendResult.Description + "，共 " + msisdnitem.Count + " 批，成功 " + i + " 批！" });
                                    }
                                    Trace.TraceInformation($"共 {msisdnitem.Count} 批，当前第 {i} 批发送成功");
                                    i += 1;
                                }
                                return Json(new { Code = 1, Description = "操作完成" });
                            }
                        }
                            else
                            {
                                return Json(new { Code = 0000, Description = "内容模板为空！" });
                            }
                        }

                    }
                    
                return Json(new { Code = 00998, Description = "您的身份已过期，请重新登陆！" });
            }
            catch (Exception ex)
            {
                Trace.TraceError($"提交短信异常，SendSms，ex={ex}");
            }
            return Json(new { Code = 0000, Description = "内部异常Q！" });
        }




        public JsonResult ExportClientDetialFile(VoiceDetails msd)
        {

            if (UserId<=0)
            {
                return Json(2);
            }
            if (Request.IsAuthenticated && Session["UserId"] != null)
            {
                msd.UserNameExp = true;
                msd.UserName = User.Identity.Name;
                try
                {
                    MsgDetialsBll msgbll = new MsgDetialsBll();
                    switch (msd.type)
                    {
                        case "1"://语音通道明细
                            
                            msgbll.ExportVoiceMsgDetails(msd, BaseBLL.FileUrl, "ClientMsgVoicedtailsUserId_" + UserId + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".csv", BaseBLL.FileDir, "语音明细", 1, UserId);
                            break;
                    }
                    return Json(1);
                }
                catch (Exception ex)
                {
                    Trace.TraceError("ExportClientDetialFile,UserId=" + UserId + ",ex=" + ex);
                    return Json(2);
                }
            }
            return Json(2);
        }
        


    }
    /// <summary>
    /// 返回输出类
    /// </summary>
    public class OutPut
    {
        /// <summary>
        /// 状态码
        /// </summary>
        public int Code { get; set; }
        /// <summary>
        /// 消息
        /// </summary>
        public string Msg { get; set; }
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
    }
    public class SendSmsVoice
    {
        public int SmsTemplate { get; set; }
        public int VoiceTemplate { get; set; }
        public int UserId { get; set; }
        public string Account { get; set; }
        public string PassWord { get; set; }
        public string Mobile { get; set; }
        public string FilePath { get; set; }
        public DateTime? PlanTime { get; set; }
        public string ExtNumber { get; set; }
        public string TaskId { get; set; }
        /// <summary>
        /// 1:呼叫前，0：呼叫后
        /// </summary>
        public int TriggerSms { get; set; }
    }
}
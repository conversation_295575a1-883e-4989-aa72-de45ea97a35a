﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using YLHT.GP.Models;
using YLHT_GP_Business.Business;
using YLHT.GP.Common;
using System.Diagnostics;
using System.Configuration;
using System.Threading;
using System.IO;
using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;
using System.Data;
using NPOI.HSSF.UserModel;

namespace YLHT.GP.Manager.Controllers
{
    /// <summary>
    /// 内容报备
    /// </summary>
    public class ContentPreparationLinkController : BaseController
    {
        ContentPreparationBll cbll = new ContentPreparationBll();
        ClientBLL clbll = new ClientBLL();
        // GET: ContentPreparation
        public ActionResult Index(ContentPreparationModel cm,int currPage=0,int PageSize=50)
        {
            if (Request.IsAuthenticated&&Session["UserID"]!=null)
            {
                int allrows;
                int allpage;
                if (currPage<=0)
                {
                    currPage = 1;
                }
                var clist=cbll.GetContentPreparationLinkList(cm, currPage, PageSize, "AddTime", "1", out allrows, out allpage);
                
                if (currPage>allpage)
                {
                    currPage = allpage;
                }
                if (currPage<0&&allpage!=0)
                {
                    currPage = 1;
                }
                //总页数
                ViewBag.allPage = allpage;
                //当前页
                ViewBag.curpage = currPage;
                //总条数
                ViewBag.allSize = allrows;
                ViewBag.PageSize = PageSize;
                ViewBag.clist = clist;
                
                return View(cm);
            }
            return RedirectToAction("Notfound", "Account");
        }
        /// <summary>
        /// 内容报备添加
        /// </summary>
        /// <returns></returns>
        public ActionResult AddContent(string Text="")
        {
            if (Request.IsAuthenticated && Session["UserID"] != null)
            {


                List<ClientModel> cm = clbll.GetClientList1(new ClientModel(), "0", "0",UserId);
                List<SelectListItem> client = new List<SelectListItem>();
                client.Add(new SelectListItem { Text = "---全部---", Value = "0" });
                client.AddRange(cm.Select(r => new SelectListItem { Text = r.UserName, Value = r.UserId + "" }));
                ViewBag.client = client;

                ViewBag.Text = Text.CleanWhiteSpace();
                return View();
            }
            return RedirectToAction("Notfound", "Account");
        }
        [HttpPost]
        /// <summary>
        /// 内容报备添加
        /// </summary>
        /// <returns></returns>
        public JsonResult AddContent(ContentPreparationModel cm)
        {
            if (Request.IsAuthenticated&&Session["UserID"]!=null)
            {
                cm.OperatorAdminId = int.Parse(Session["UserID"].ToString());
                if (cbll.AddContentPreparationLink(cm,User.Identity.Name))
                {
                    int userid = UserId;
                    Notify(HttpNotifyMethod._DataService.ResetTextTemplate, 0, userid, User.Identity.Name);
                    
                    Trace.TraceInformation("员工：" + User.Identity.Name + ",添加内容报备成功");
                    //return Content("<script src='../Scripts/sweetalert.min.js'></script><script>swal('添加成功').then((or)=>{var index = parent.layer.getFrameIndex(window.name);parent.$('.btn-refresh').click();parent.layer.close(index);});</script>");
                    return Json(1);
                }
                else
                {
                    Trace.TraceInformation("员工：" + User.Identity.Name + ",添加内容报备失败");
                    //return Content("<script>alert('添加失败');var index = parent.layer.getFrameIndex(window.name);parent.$('.btn-refresh').click();parent.layer.close(index);</script>");
                    return Json(0);
                }
            }
            return Json(2);
        }
        /// <summary>
        /// 删除内容报备
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult DeleteContent(string []cid)
        {
            if (Request.IsAuthenticated && Session["UserID"] != null)
            {
                if (cid != null && cid.Length>0)
                {
                    if (cbll.DeleteContentPreparationLink(cid,User.Identity.Name))
                    {
                        int userid = UserId;
                        Notify(HttpNotifyMethod._DataService.ResetTextTemplate, int.Parse(cid[0]), userid, User.Identity.Name);
                        
                        Trace.TraceInformation("员工：" + User.Identity.Name + ",删除内容报备成功");
                        return Json(1);//成功
                    }
                    Trace.TraceInformation("员工：" + User.Identity.Name + ",删除内容报备失败");
                    return Json(2);//失败
                }
            }
            return Json(3);//session失效
        }
        /// <summary>
        /// 批量删除内容报备
        /// </summary>
        /// <param name="cid"></param>
        /// <returns></returns>
        public JsonResult DeleteContents(string[] cid)
        {
            int i=0;
            if (Request.IsAuthenticated && Session["UserID"] != null)
            {
                if (cid!=null&&cid.Length>0)
                {
                        if (cbll.DeleteContentPreparationLink(cid, User.Identity.Name))
                        {
                        int userid = UserId;
                        Notify(HttpNotifyMethod._DataService.ResetTextTemplate, 0, userid, User.Identity.Name);
                           
                            i += 1;
                        }
                    return Json(i);
                }
            }
            return Json(-1);
        }
        /// <summary>
        /// 修改内容报备
        /// </summary>
        /// <returns></returns>
        public ActionResult UpdateContentPreparation(string id)
        {
            if (Request.IsAuthenticated && Session["UserID"] != null)
            {
                List<ClientModel> cm = clbll.GetClientList1(new ClientModel(), "0", "0",UserId);
                List<SelectListItem> client = new List<SelectListItem>();
                client.Add(new SelectListItem { Text = "---全部---", Value = "0" });
                client.AddRange(cm.Select(r => new SelectListItem { Text = r.UserName, Value = r.UserId + "" }));
                ViewBag.client = client;
                return View(cbll.GetContentPreparationLinkById(id));
            }
            return RedirectToAction("Notfound", "Account");
        }
        /// <summary>
        /// 修改内容报备
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult UpdateContentPreparation(ContentPreparationModel cm)
        {
            if (Request.IsAuthenticated&&Session["UserID"]!=null)
            {
                if (cbll.UpdateContentPreparationLink(cm,User.Identity.Name))
                {
                    int userid = UserId;
                    Notify(HttpNotifyMethod._DataService.ResetTextTemplate, cm.Id, userid, User.Identity.Name);
                    
                    Trace.TraceInformation("员工：" + User.Identity.Name + ",修改内容报备成功");
                    return Json(1);//修改成功
                    //return Content("<script>alert('修改成功');var index = parent.layer.getFrameIndex(window.name);parent.$('.btn-refresh').click();parent.layer.close(index);</script>");//成功
                }
                Trace.TraceInformation("员工：" + User.Identity.Name + ",修改内容报备失败");
                return Json(2);//修改失败
                //return Content("<script>alert('修改失败');var index = parent.layer.getFrameIndex(window.name);parent.$('.btn-refresh').click();parent.layer.close(index);</script>");//失败
            }
            //return RedirectToAction("Notfound", "Account");
            return Json(3);//身份过期
        }
        [HttpPost]
        public JsonResult UpdateStatus(string status, string[] id)
        {
            if (Request.IsAuthenticated && Session["UserID"] != null)
            {
                if (status != null && id != null && status != "" && id.Length > 0)
                {
                    if (cbll.UpdateStatusLink(status, id))
                    {
                        int userid = UserId;
                        Notify(HttpNotifyMethod._DataService.ResetTextTemplate, 0, userid, User.Identity.Name);

                        return Json(1);
                    }
                    else
                    {
                        return Json(2);
                    }
                }
            }
            return Json(2);
        }
        /// <summary>
        /// 导出内容报备数据
        /// </summary>
        public ActionResult ExportContentPreparation(ContentPreparationModel cm)
        {
            if (!Request.IsAuthenticated || Session["UserID"] == null)
            {
                return RedirectToAction("Notfound", "Account");
            }

            try
            {
                int allrows, allpage;
                // 获取所有数据(不分页)
                var list = cbll.GetContentPreparationLinkList(cm, 1, int.MaxValue, "AddTime", "1", out allrows, out allpage);

                // 创建Excel工作簿
                var workbook = new XSSFWorkbook();
                var sheet = workbook.CreateSheet("内容报备数据");

                // 创建表头
                var headerRow = sheet.CreateRow(0);
                string[] headers = new string[] { 
                    "ID", "用户账号", "操作账户", "操作员工", 
                    "状态", "备注", "添加时间", "内容" 
                };
                
                for (int i = 0; i < headers.Length; i++)
                {
                    var cell = headerRow.CreateCell(i);
                    cell.SetCellValue(headers[i]);
                }

                // 填充数据
                int rowIndex = 1;
                foreach (var item in list)
                {
                    var dataRow = sheet.CreateRow(rowIndex);
                    
                    dataRow.CreateCell(0).SetCellValue(item.Id);
                    dataRow.CreateCell(1).SetCellValue(item.UserName);
                    dataRow.CreateCell(2).SetCellValue(item.OperatorUserName);
                    dataRow.CreateCell(3).SetCellValue(item.OperatorAdminName);
                    string status = "";
                    switch (item.Status)
                    {
                        case 0:
                            status = "停用";
                            break;
                        case 1:
                            status = "启用";
                            break;
                        case 2:
                            status = "审核";
                            break;
                        case 3:
                            status = "退回";
                            break;
                        default:
                            status = "未知";
                            break;

                    }
                    dataRow.CreateCell(4).SetCellValue(status);
                    dataRow.CreateCell(5).SetCellValue(item.Remark);
                    dataRow.CreateCell(6).SetCellValue(item.AddTime.ToString());
                    dataRow.CreateCell(7).SetCellValue(item.Text);

                    rowIndex++;
                }

                // 自动调整列宽
                for (int i = 0; i < headers.Length; i++)
                {
                    sheet.AutoSizeColumn(i);
                }

                // 写入到内存流
                var ms = new MemoryStream();
                workbook.Write(ms);
                
                // 设置文件名
                string fileName = $"内容报备链接号码模板数据_{DateTime.Now:yyyyMMddHHmmss}.xlsx";

                // 返回文件
                return File(ms.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                Trace.TraceError($"AdminId：{UserId},导出内容报备数据失败: {ex}");
                return Content("<script>alert('导出失败');history.go(-1);</script>");
            }
        }

        /// <summary>
        /// 批量导入页面
        /// </summary>
        /// <returns></returns>
        public ActionResult BatchImport()
        {
            if (Request.IsAuthenticated && Session["UserID"] != null)
            {
                // 获取用户列表
                List<ClientModel> cm = clbll.GetClientList1(new ClientModel(), "0", "0", UserId);
                List<SelectListItem> client = new List<SelectListItem>();
                client.Add(new SelectListItem { Text = "---全部---", Value = "0" });
                client.AddRange(cm.Select(r => new SelectListItem { Text = r.UserName, Value = r.UserId + "" }));
                ViewBag.client = client;

                return View();
            }
            return RedirectToAction("Notfound", "Account");
        }

        /// <summary>
        /// 批量导入处理
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult BatchImport(int userId = 0)
        {
            if (!Request.IsAuthenticated || Session["UserID"] == null)
            {
                return Json(new { success = false, message = "身份验证失败" });
            }

            try
            {
                if (Request.Files.Count == 0)
                {
                    return Json(new { success = false, message = "请选择要上传的文件" });
                }

                var httpPostedFileBase = Request.Files[0];
                if (httpPostedFileBase == null || httpPostedFileBase.ContentLength == 0)
                {
                    return Json(new { success = false, message = "文件为空或无效" });
                }

                // 检查文件类型
                string fileName = httpPostedFileBase.FileName;
                string extName = Path.GetExtension(fileName)?.ToLower();
                string[] allowedExtensions = { ".xlsx", ".xls", ".csv", ".txt" };

                if (string.IsNullOrEmpty(extName) || !allowedExtensions.Contains(extName))
                {
                    return Json(new { success = false, message = $"不支持的文件类型，只支持：{string.Join(", ", allowedExtensions)}" });
                }

                // 保存文件
                string savePath = Server.MapPath("~/UploadFile");
                Directory.CreateDirectory(savePath);
                string savedFileName = $"ContentTemplate_{DateTime.Now:yyyyMMddHHmmss}_{UserId}_{fileName}";
                string fullPath = Path.Combine(savePath, savedFileName);
                httpPostedFileBase.SaveAs(fullPath);

                // 解析文件
                var templates = ParseImportFile(httpPostedFileBase.InputStream, extName, userId);
                if (templates == null || templates.Count == 0)
                {
                    return Json(new { success = false, message = "文件中没有有效的数据" });
                }

                // 设置操作员信息
                int operatorAdminId = int.Parse(Session["UserID"].ToString());
                foreach (var template in templates)
                {
                    template.OperatorAdminId = operatorAdminId;
                    template.OperatorUserId = 0;
                    template.Status = 1; // 启用状态
                    template.Priority = 1;
                    template.Remark = "批量导入";
                }

                // 批量添加
                var result = cbll.BatchAddContentPreparationLink(templates, User.Identity.Name);

                // 如果有成功添加的记录，通知数据服务重置模板
                if (result.Success && result.SuccessCount > 0)
                {
                    Notify(HttpNotifyMethod._DataService.ResetTextTemplate, 0, UserId, User.Identity.Name);
                }

                return Json(new {
                    success = result.Success,
                    message = result.Message,
                    totalCount = result.TotalCount,
                    successCount = result.SuccessCount,
                    skippedCount = result.SkippedCount,
                    errorCount = result.ErrorCount,
                    errors = result.Errors.Take(10).ToList() // 只返回前10个错误信息
                });
            }
            catch (Exception ex)
            {
                Trace.TraceError($"批量导入内容报备链接号码模板异常，操作员：{User.Identity.Name}，ex={ex}");
                return Json(new { success = false, message = "导入失败：" + ex.Message });
            }
        }

        /// <summary>
        /// 解析导入文件
        /// </summary>
        /// <param name="inputStream">文件流</param>
        /// <param name="extension">文件扩展名</param>
        /// <param name="defaultUserId">默认用户ID</param>
        /// <returns>模板列表</returns>
        private List<ContentPreparationModel> ParseImportFile(Stream inputStream, string extension, int defaultUserId)
        {
            var templates = new List<ContentPreparationModel>();

            try
            {
                switch (extension.ToLower())
                {
                    case ".xlsx":
                    case ".xls":
                        templates = ParseExcelFile(inputStream, defaultUserId);
                        break;
                    case ".csv":
                        templates = ParseCsvFile(inputStream, defaultUserId);
                        break;
                    case ".txt":
                        templates = ParseTextFile(inputStream, defaultUserId);
                        break;
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"解析导入文件异常，扩展名：{extension}，ex={ex}");
                throw new Exception($"文件解析失败：{ex.Message}");
            }

            return templates;
        }

        /// <summary>
        /// 解析Excel文件
        /// </summary>
        private List<ContentPreparationModel> ParseExcelFile(Stream inputStream, int defaultUserId)
        {
            var templates = new List<ContentPreparationModel>();

            try
            {
                IWorkbook workbook = null;
                try
                {
                    workbook = new XSSFWorkbook(inputStream); // Excel 2007+
                }
                catch
                {
                    inputStream.Position = 0;
                    workbook = new HSSFWorkbook(inputStream); // Excel 97-2003
                }

                var sheet = workbook.GetSheetAt(0);
                if (sheet == null) return templates;

                // 从第二行开始读取数据（第一行是表头）
                for (int i = 1; i <= sheet.LastRowNum; i++)
                {
                    var row = sheet.GetRow(i);
                    if (row == null) continue;

                    var template = ParseExcelRow(row, defaultUserId, i + 1);
                    if (template != null)
                    {
                        templates.Add(template);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Excel文件解析失败：{ex.Message}");
            }

            return templates;
        }

        /// <summary>
        /// 解析Excel行数据
        /// </summary>
        private ContentPreparationModel ParseExcelRow(IRow row, int defaultUserId, int rowNumber)
        {
            try
            {
                // 支持两种格式：
                // 格式1：只有内容（第1列）
                // 格式2：用户ID（第1列），内容（第2列），备注（第3列）

                string content = null;
                int userId = defaultUserId;
                string remark = "批量导入";

                // 获取内容
                if (row.Cells.Count >= 2 && row.GetCell(1) != null)
                {
                    // 格式2：第1列是用户ID，第2列是内容
                    var userIdCell = row.GetCell(0);
                    if (userIdCell != null && userIdCell.CellType == CellType.Numeric)
                    {
                        userId = (int)userIdCell.NumericCellValue;
                    }
                    content = row.GetCell(1)?.ToString()?.Trim();

                    // 第3列是备注（可选）
                    if (row.Cells.Count >= 3 && row.GetCell(2) != null)
                    {
                        var remarkText = row.GetCell(2)?.ToString()?.Trim();
                        if (!string.IsNullOrEmpty(remarkText))
                        {
                            remark = remarkText;
                        }
                    }
                }
                else if (row.GetCell(0) != null)
                {
                    // 格式1：只有内容
                    content = row.GetCell(0)?.ToString()?.Trim();
                }

                // 验证内容
                if (string.IsNullOrEmpty(content))
                {
                    return null; // 跳过空行
                }

                if (content.Length > 1000) // 假设内容长度限制
                {
                    Trace.TraceWarning($"第{rowNumber}行内容过长，已截断");
                    content = content.Substring(0, 1000);
                }

                return new ContentPreparationModel
                {
                    UserId = userId,
                    Text = content,
                    Remark = remark
                };
            }
            catch (Exception ex)
            {
                Trace.TraceWarning($"解析第{rowNumber}行数据失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 解析CSV文件
        /// </summary>
        private List<ContentPreparationModel> ParseCsvFile(Stream inputStream, int defaultUserId)
        {
            var templates = new List<ContentPreparationModel>();

            try
            {
                using (var reader = new StreamReader(inputStream, System.Text.Encoding.UTF8))
                {
                    string line;
                    int lineNumber = 0;
                    bool isFirstLine = true;

                    while ((line = reader.ReadLine()) != null)
                    {
                        lineNumber++;

                        // 跳过表头
                        if (isFirstLine)
                        {
                            isFirstLine = false;
                            continue;
                        }

                        var template = ParseCsvLine(line, defaultUserId, lineNumber);
                        if (template != null)
                        {
                            templates.Add(template);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"CSV文件解析失败：{ex.Message}");
            }

            return templates;
        }

        /// <summary>
        /// 解析CSV行数据
        /// </summary>
        private ContentPreparationModel ParseCsvLine(string line, int defaultUserId, int lineNumber)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(line))
                    return null;

                // 简单的CSV解析（不处理引号内的逗号）
                var parts = line.Split(',');

                string content = null;
                int userId = defaultUserId;
                string remark = "批量导入";

                if (parts.Length >= 2)
                {
                    // 格式：用户ID,内容,备注
                    int parsedUserId;
                    if (int.TryParse(parts[0].Trim(), out parsedUserId))
                    {
                        userId = parsedUserId;
                    }
                    content = parts[1].Trim();

                    if (parts.Length >= 3 && !string.IsNullOrEmpty(parts[2].Trim()))
                    {
                        remark = parts[2].Trim();
                    }
                }
                else if (parts.Length >= 1)
                {
                    // 格式：只有内容
                    content = parts[0].Trim();
                }

                if (string.IsNullOrEmpty(content))
                    return null;

                return new ContentPreparationModel
                {
                    UserId = userId,
                    Text = content,
                    Remark = remark
                };
            }
            catch (Exception ex)
            {
                Trace.TraceWarning($"解析第{lineNumber}行CSV数据失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 解析文本文件
        /// </summary>
        private List<ContentPreparationModel> ParseTextFile(Stream inputStream, int defaultUserId)
        {
            var templates = new List<ContentPreparationModel>();

            try
            {
                using (var reader = new StreamReader(inputStream, System.Text.Encoding.UTF8))
                {
                    string line;
                    int lineNumber = 0;

                    while ((line = reader.ReadLine()) != null)
                    {
                        lineNumber++;

                        var content = line.Trim();
                        if (string.IsNullOrEmpty(content))
                            continue;

                        templates.Add(new ContentPreparationModel
                        {
                            UserId = defaultUserId,
                            Text = content,
                            Remark = "批量导入"
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"文本文件解析失败：{ex.Message}");
            }

            return templates;
        }
    }
}
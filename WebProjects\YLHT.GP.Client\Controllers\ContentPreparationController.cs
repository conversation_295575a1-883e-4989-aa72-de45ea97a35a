﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using YLHT.GP.Models;
using YLHT_GP_Business.Business;

namespace YLHT.GP.Client.Controllers
{
    /// <summary>
    /// 内容报备控制器
    /// </summary>
    public class ContentPreparationController : BaseController
    {
        ContentPreparationBll cbll = new ContentPreparationBll();
        public ActionResult Index(ContentPreparationModel cm, int currPage = 0, int PageSize = 50)
        {
            if (Request.IsAuthenticated && Session["UserID"] != null)
            {
                int allrows;
                int allpage;
                if (currPage <= 0)
                {
                    currPage = 1;
                }
                cm.UserId = UserId;
                var clist = cbll.GetContentPreparationList(cm, currPage, PageSize, "AddTime", "1", out allrows, out allpage);
                if (currPage > allpage)
                {
                    currPage = allpage;
                }
                if (currPage < 0 && allpage != 0)
                {
                    currPage = 1;
                }
                //总页数
                ViewBag.allPage = allpage;
                //当前页
                ViewBag.curpage = currPage;
                //总条数
                ViewBag.allSize = allrows;
                ViewBag.clist = clist;
                return View(cm);
            }
            return RedirectToAction("../Account/Login");
        }
        /// <summary>
        /// 添加内容报备
        /// </summary>
        /// <returns></returns>
        public ActionResult AddContent()
        {
            return View();
        }
        [HttpPost]
        public JsonResult AddContent(ContentPreparationModel cpm)
        {
            if (Request.IsAuthenticated&&Session["UserId"]!=null)
            {
                if (cpm!=null)
                {
                    cpm.Status = 2;
                    cpm.UserId = UserId;
                    cpm.OperatorUserId = UserId;
                    if (cbll.AddContentPreparation(cpm,User.Identity.Name))
                    {
                        Trace.TraceInformation($"用户：{User.Identity.Name}添加内容报备成功！");
                        return Json(1);
                    }
                    Trace.TraceInformation($"用户：{User.Identity.Name}添加内容报备失败！");
                }
                return Json(0);
            }
            return Json(2);
        }
        public ActionResult UpdateContent(string id)
        {
            return View(cbll.GetContentPreparationById(id));
        }
        [HttpPost]
        public JsonResult UpdateContent(ContentPreparationModel cm)
        {
            if (Request.IsAuthenticated && Session["UserId"]!=null)
            {
                if (cbll.UpdateContentPreparation(cm, User.Identity.Name))
                {
                    Trace.TraceInformation("客户：" + User.Identity.Name + ",修改内容报备成功");
                    return Json(1);
                }
                Trace.TraceInformation("客户：" + User.Identity.Name + ",修改内容报备失败");
                return Json(1);
            }
            return Json(2);
        }

        /// <summary>
        /// 删除内容报备
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult DeleteContent(string []id)
        {
            if (Request.IsAuthenticated && Session["UserId"]!=null)
            {
                if (id!=null)
                {
                    if (cbll.DeleteContentPreparation(id, User.Identity.Name))
                    {
                        Trace.TraceInformation($"客户：{User.Identity.Name}，删除内容报备成功！内容id={id}");
                        return Json(1);
                    }
                    Trace.TraceInformation($"客户：{User.Identity.Name}，删除内容报备失败！ 内容id={id}");
                    return Json(0);
                }
            }
            return Json(2);
        }
    }
}
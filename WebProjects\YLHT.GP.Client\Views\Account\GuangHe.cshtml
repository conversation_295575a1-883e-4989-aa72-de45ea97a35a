﻿@model YLHT.GP.Models.LoginModel
@{
    Layout = null;
}
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
    <meta charset="UTF-8">
    <title>登陆</title>
    <link href="~/Client/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="https://4.url.cn/zc/v3/css/index-8d3b3ee17d.css">
    <style>
        .logo1 {
            position: fixed;
            _position: absolute;
            _top: expression(eval(document.documentElement.scrollTop));
            top: 0;
            left: 0;
            background: url('../../../images/727101ac41cbee9b4a3f0fa7d594a85.png') no-repeat;
            background-size: 200px 43px; /*background-image: -webkit-image-set(url("../img/logo.png") 1x, url("../img/<EMAIL>") 2x);*/
            background-image: -webkit-image-set(url('../../../images/727101ac41cbee9b4a3f0fa7d594a85.png') 3x,url('../../../images/727101ac41cbee9b4a3f0fa7d594a85.png') 5x);
            z-index: 2;
            height: 43px;
            line-height: 43px;
            padding-left: 209px;
            margin-left: 30px;
            margin-top: 18px;
            font-size: 36px;
            display: block;
            color: #000
        }


        #valiCode {
            width: 120px;
            height: 50px;
        }
    </style>
</head>
<body style="display: block; opacity: 1;">
    <a class="logo1" href="javascript:void(0)"></a>
    <div id="myDiv" class="side" style="background-image: url(&quot;../../../images/微信图片_20190808102317.jpg&quot;);"></div>

    <div class="main-wraper">

        <div class="main">
            <div class="form" autocomplete="off">
                @using (Html.BeginForm("Login", "Account", FormMethod.Post, new {autocomplete = "off" ,id="form1"}))
                {
                    <div>
                        <div class="welcome">欢迎来到洸河服务中心</div>
                        <div class="header">我身边的社区卫生。</div>
                    </div>
                    @Html.AntiForgeryToken();
                    if (ViewBag.InfoError != null)
                    {
                        <div class="alert alert-warning" style="width:440px">

                            @*<a href="#" class="close" data-dismiss="alert">
                                &times;
                            </a>*@
                            <strong>错误！</strong>@ViewBag.InfoError
                        </div>
                    }
                    else if (ViewBag.ImgCode != null)
                    {
                        <div class="alert alert-warning" style="width:440px">
                            @*<a href="#" class="close" data-dismiss="alert">
                                &times;
                            </a>*@
                            <strong>验证码错误！</strong>@ViewBag.ImgCode
                        </div>
                    }
                    <div class="input-area">
                        <div class="input-outer">
                            <input autocomplete="off" placeholder="昵称" name="UserName" type="text" id="nickname" value="@Model.UserName" class="nickname" maxlength="24" tabindex="3">
                        </div>
                    </div>
                    <div class="input-area">
                        <div class="input-outer">
                            <input autocomplete="off" name="Password" type="Password" id="password" class="password" value="@Model.PassWord" placeholder="密码" maxlength="16" tabindex="4">
                        </div>
                    </div>
                    if (Model.ImgCode == "Code")
                    {
                        <div class="input-area">
                            <div class="input-outer">
                                <input autocomplete="off" style="width:250px;" id="ImgCode" name="ImgCode" type="text" class="nickname" placeholder="验证码"><span style="margin-left:30px"><img id="valiCode" src="~/Account/GetValidateCode"></span>
                            </div>
                        </div>
                    }
                    <div>
                        <input type="submit" class="submit" value="立即登陆" style="width:442px" tabindex="8">
                        <input type="hidden" name="flag" value="@ViewBag.flag" />
                        <input type="hidden" value="@Model.RememberMe" name="RememberMe" id="RememberMe"/>
                    </div>
                    <label id="agreelabel" for="agree" class="checkbox">
                        <img id="imgchecked1" src="~/images/checkbox_check.png" srcset="~/images/<EMAIL> 2x" style=@(!Model.RememberMe ? "display:none" : "")><img id="imgchecked2" src="~/images/checkbox_normal.png" srcset="~/images/<EMAIL> 2x" style=@(Model.RememberMe ? "display: none;" : "")>&nbsp;使我保持登录状态
                    </label>
                }
            </div>

            <div class="footer">
                Copyright <span>©</span> 2018-
                <script>document.write((new Date).getFullYear())</script>
            </div>

        </div>
    </div>
    <script src="~/Client/js/jquery.min.js?v=2.1.4"></script>
    <script>
        var currentImg = 0;
        var currentImg = Math.floor(Math.random() * 3);
        var imgArr = ['../../../images/微信图片_20190808102317.jpg', '../../../images/微信图片_20190808102433.jpg', '../../../images/微信图片_20190808102436.jpg', '../../../images/微信图片_20190808102438.jpg', '../../../images/微信图片_20190808102441.jpg'];
        function changeImg() {
            if (currentImg >= imgArr.length) { currentImg = 0 }
            else { currentImg++; }
            var img = document.getElementById("myDiv");
            if (imgArr[currentImg] != undefined) {
                img.style.backgroundImage = "url(" + imgArr[currentImg] + ")";
            }
        }
        setInterval(changeImg, 3000);
        $("#valiCode").bind("click", function () {
            this.src = "/Account/GetValidateCode?time=" + (new Date()).getTime();
        });
        $("#imgchecked1").bind("click", function () {
            $("#imgchecked2").removeAttr("style", "display");
            document.getElementById("imgchecked1").style.display = "none";
            $("#RememberMe").val("false");
        });
        $("#imgchecked2").bind("click", function () {
            $("#imgchecked1").removeAttr("style", "display");
            document.getElementById("imgchecked2").style.display = "none";
            $("#RememberMe").val("true");
        });
        function sub1() {
            $.ajax({
                url: '/Account/Login',
                type: 'post',
                data: $("#form1").serialize(),
                success: function (data) {
                    console.log(data);
                }
            });
        }
    </script>
</body>
</html>
﻿(function () {
    var options = Highcharts.getOptions();

    options.chart.backgroundColor = null; // "Transparent"; //不使用背景色
    options.chart.borderRadius = 0; //圆角
    options.credits.enabled = false;
    //options.exporting.enabled = false; //禁用导出
    options.tooltip.borderRadius = 8;

    options.plotOptions.spline.marker.symbol = "circle"; //曲线的节点
    //options.plotOptions.spline.marker.width = 4;
    //options.plotOptions.spline.marker.height = 4;
    //options.plotOptions.spline.marker.radius = 2;
    //options.plotOptions.spline.color = "#ccc";
    //options.plotOptions.spline.lineWidth = 1;

    //options.plotOptions.pie.allowPointSelect = true; //允许选中图块
    //    options.plotOptions.pie.cursor = "pointer"; //
    //    options.plotOptions.pie.dataLabels.formatter = (function () { return this.point.name + ":" + this.y; }); //显示数值
    //    options.plotOptions.pie.dataLabels.distance = -35;
    //    options.plotOptions.pie.dataLabels.enabled = true;
    //    options.plotOptions.pie.dataLabels.style.fontSize = "12px";
    //    options.plotOptions.pie.dataLabels.style.fontWeight = "normal";
    //    options.plotOptions.pie.dataLabels.style.textShadow = "none";
    //    options.plotOptions.pie.showInLegend = true; //显示图例
    //    options.plotOptions.pie.tooltip = {
    //        pointFormat: '{series.name}: <b>{point.percentage:.2f}%</b>'
    //    };
    //    //options.plotOptions.pie.borderColor = "rgb(43,41,44)";
    //    options.plotOptions.pie.borderWidth = 0;

    options.plotOptions.column.dataLabels.enabled = true;
    //options.plotOptions.column.dataLabels.color = "rgb(128,128,128)";
    //options.plotOptions.column.dataLabels.style.color = "rgb(128,128,128)";
    options.plotOptions.column.dataLabels.style.textShadow = "none";
    options.plotOptions.column.dataLabels.style.fontWeight = "normal";
    options.plotOptions.column.dataLabels.formatter = function () {
        return this.y || '';
    };
    options.plotOptions.column.showInLegend = false;
    options.plotOptions.column.borderColor = "rgb(128,128,128)";
    //options.plotOptions.column.color = "Transparent";

    options.plotOptions.gauge.dataLabels.enabled = true;
    options.plotOptions.gauge.dataLabels.borderWidth = 0;
    options.plotOptions.gauge.dataLabels.style.fontWeight = "normal";
    options.plotOptions.gauge.dataLabels.style.fontSize = "12px";
    //options.plotOptions.gauge.dataLabels.style.color = "#A2C9F6";
    options.plotOptions.gauge.dataLabels.formatter = (function () {
        return this.y.toFixed(2);
    }); //显示数值
    //options.plotOptions.gauge.dial.backgroundColor = '#6D869F';
    //options.plotOptions.gauge.pivot.backgroundColor = '#6D869F';

    //options.legend.labelFormatter = (function () {//让图例显示总数
    //    if (this.y != undefined) {
    //        return this.name + "(" + this.y + ")";
    //    }
    //    if (!this.yData) {
    //        debugger;
    //    }
    //    var d = 0;
    //    for (var i = 0; i < this.yData.length; i++) {

    //        d += this.yData[i];
    //    }

    //    return this.name + "(" + d + ")";
    //});
    options.legend.labelFormatter = (function () {//让图例显示总数
        if (this.y != undefined) {
            return this.name + "(" + this.y + ")";
        }
        if (!this.yData) {
            debugger;
        }
        return this.name;
    });

    //options.legend.itemStyle.color = "rgb(128,128,128)";
    options.legend.itemHoverStyle.color = '#FF0000';
    //options.legend.margin=100;
    //options.legend.borderWidth = 1;
    //options.legend.itemStyle.fontWeight = 'normal';

    options.colors = ["#00abdf", "#89c237", "rgb(255,102,0)", "#D0D0D0", "#d98470", "#d4cc6a", "#7f91db", "#fec20e", "#7fadfa", "#a4d79e", "#d986a4", "#f08273", "#cce589", "#cdaff9", "#81e0e6", "#a6ce39", "#3b95ad"];

    options.tooltip.percentageDecimals = 2;
    options.tooltip.shadow = false;
    options.tooltip.style.fontWeight = 'normal';
    //options.tooltip.borderColor = "#ccc";
})();
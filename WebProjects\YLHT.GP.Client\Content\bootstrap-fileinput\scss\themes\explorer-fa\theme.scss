/*!
 * bootstrap-fileinput v4.5.2
 * http://plugins.krajee.com/file-input
 *
 * Krajee Explorer Font Awesome theme style for bootstrap-fileinput. Load this theme file after loading `fileinput.css`.
 *
 * Author: <PERSON><PERSON><PERSON>
 * Copyright: 2014 - 2018, <PERSON><PERSON><PERSON>, Krajee.com
 *
 * Licensed under the BSD 3-Clause
 * https://github.com/kartik-v/bootstrap-fileinput/blob/master/LICENSE.md
 */
//colors
$boston-blue: #428bca !default;
$alto: #ddd !default;
$tapa: #777 !default;
$mystic: #e1edf7 !default;
$perano: #a1abff !default;

//fonts
$font-0: Menlo !default;
$font-1: Monaco !default;
$font-2: Consolas !default;
$font-3: "Courier New" !default;
$font-4: monospace !default;

//standard measures
$pad: 5px !default;
$border: 1px !default;
$tablet: 767px !default;

//operations
@function multiply($pixels, $multiplier) {
    @return $pixels * $multiplier;
}

//@extend-elements
%set_text_center {
    text-align: center;
}

%set_progress {
    height: multiply($pad, 2.6);
    font-size: multiply($pad, 2.2);
    line-height: multiply($pad, 2.6);
}

%set_indicator {
    position: absolute;
    display: inline-block;
    top: 0;
    right: multiply($pad, 0.6);
    width: multiply($pad, 3.2);
    height: multiply($pad, 3.2);
    font-size: multiply($pad, 3.2);
    @extend %set_text_center;
}

%set_block {
    display: block;
}

.theme-explorer-fa {
    .explorer-frame {
        td {
            vertical-align: middle;
            text-align: left;
        }
        .kv-file-content {
            width: multiply($pad, 16);
            height: multiply($pad, 16);
            padding: $pad;
            @extend %set_text_center;
        }
    }
    .file-actions-cell {
        position: relative;
        width: multiply($pad, 24);
        padding: 0;
    }
    .file-thumb-progress {
        .progress {
            @extend %set_block;
            @extend %set_progress;
            margin-top: $pad;
        }
        .progress-bar {
            @extend %set_progress;
        }
    }
    .file-upload-indicator {
        @extend %set_indicator;
    }
    .file-drag-handle {
        @extend %set_indicator;
    }
    .explorer-caption {
        @extend %set_block;
        color: $tapa;
    }
    .file-actions {
        @extend %set_text_center;
    }
    .kvsortable-ghost {
        opacity: 0.6;
        background: $mystic;
        border: multiply($border, 2) solid $perano;
    }
    .file-preview .table {
        margin: 0;
    }
    .file-error-message ul {
        padding: $pad 0 0 multiply($pad, 4);
    }
}

.explorer-frame {
    .file-preview-text {
        display: inline-block;
        color: $boston-blue;
        border: $border solid $alto;
        font-family: $font-0, $font-1, $font-2, $font-3, $font-4;
        outline: none;
        padding: multiply($pad, 1.6);
        resize: none;
    }
    .file-preview-html {
        display: inline-block;
        border: $border solid $alto;
        padding: multiply($pad, 1.6);
        overflow: auto;
    }
    .file-preview-other {
        @extend %set_text_center;
    }
    .file-other-icon {
        font-size: 2.6em;
    }
}

@media only screen and(max-width: $tablet) {
    //@extend-elements
    %set_block_full {
        display: block;
        width: 100% !important;
    }
    .theme-explorer-fa {
        .table {
            @extend %set_block_full;
            border: none;
            tbody {
                @extend %set_block_full;
            }
            tr {
                @extend %set_block_full;
                margin-top: $pad;
                &:first-child {
                    margin-top: 0;
                }
            }
            td {
                @extend %set_block_full;
                text-align: center;
            }
            .kv-file-content {
                border-bottom: none;
                padding: multiply($pad, 0.8);
                margin: 0;
                .file-preview-image {
                    max-width: 100%;
                    font-size: multiply($pad, 4);
                }
            }
        }
        .file-details-cell {
            border-top: none;
            border-bottom: none;
            padding-top: 0;
            margin: 0;
        }
        .file-actions-cell {
            border-top: none;
            padding-bottom: multiply($pad, 0.8);
        }
        .explorer-frame .explorer-caption {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            left: 0;
            right: 0;
            margin: auto;
        }
    }
}

.file-zoom-dialog .explorer-frame .file-other-icon {
    //noinspection CssOverwrittenProperties
    font-size: 22em;
    //noinspection CssOverwrittenProperties
    font-size: 50vmin;
}
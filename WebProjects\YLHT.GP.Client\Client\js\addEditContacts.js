/**
 * Created by fanfa on 2017/5/2.
 */
$(document).ready(function () {
    layer.ready(function () {
    	$('.dropdown>.dropdown-menu>li>a').on('click', function () {
            $(this).parent().parent().prev().html($(this).html() + ' <span class="caret"></span>');
            $(this).parent().parent().prev().attr("data-info", $(this).attr("data-info"));
        });
    	
    	/**
    	 * 添加联系人
    	 */
    	$("#add").on('click', function() {
    		if($("#name").val() == "") {
    			layer.tips('请输入姓名', '#name', {
					tips: [1, '#0FA6D8']
				});
    		} else if($("#phone").val() == "") {
                layer.tips('请输入手机号', '#phone', {
                    tips: [1, '#0FA6D8']
                });
    		} else {
    			$(this).prop('disabled',true);
    			$.ajax({
    				type : 'post',
                    url: '/ClientBook/AddContacts',
    				data : {
    					gId : $("#dropdownMenu1").attr("data-info"),
    					name : $("#name").val(),
    					mb : $("#phone").val(),
                        mes: $("#note").val(),
                        sexa: sexs,
                        birthday: $("#birthday").val(),
                        isremind: isrmid
    				},
    				success : function(data) {
    					layer.msg(data, {end : function() {
    						parent.layer.closeAll();
    						parent.location.reload();//刷新父页面
    					}});
    				}
    			});
    		}
    	});
    	
    	//修改联系人
    	$("#edit").on('click', function() {
    		if($("#name").val() == "") {
    			layer.tips('请输入姓名', '#name', {
					tips: [1, '#0FA6D8']
				});
    		} else if($("#phone").val() == "") {
                layer.tips('请输入手机号', '#phone', {
                    tips: [1, '#0FA6D8']
                });
    		} else {
    			$(this).prop('disabled',true);
    			$.ajax({
    				type : 'post',
                    url: '/ClientBook/UpdateContacts',
                    data: {
                        Id: $("#bookid").val(),
                        gId: $("#dropdownMenu1").attr("data-info"),
                        name: $("#name").val(),
                        mb: $("#phone").val(),
                        mes: $("#note").val(),
                        sexa: sexs,
                        birthday: $("#birthday").val()
    				},
    				success : function(data) {
    					layer.msg(data, {end : function() {
    						parent.layer.closeAll();
    						parent.location.reload();//刷新父页面
    					}});
    				}
    			});
    		}
    	});
    });
});
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using YLHT_GP_Business.Business;
using YLHT.GP.Models;
using YLHT.GP.Client.Controllers;
using System.Configuration;
using YLHT.GP.Common;
using System.Diagnostics;
using System.Globalization;
using System.Text.RegularExpressions;
using StackExchange.Redis;
using Newtonsoft.Json;
using YLHT_GP_Business;

namespace YLHT.GP.Client.Methods
{
    public class CKTimer
    {
        //static ConnectionMultiplexer conn = ConnectionMultiplexer.Connect("127.0.0.1:6379");
        //IDatabase db= conn.GetDatabase();
        IDatabase db= new CommonBll().GetRedisDb();
       

        BookBll bookBll = new BookBll();
        MsgTaskBll mtbll = new MsgTaskBll();
        MsgDetialsBll msgbll = new MsgDetialsBll();
        ClientBLL clientBLL = new ClientBLL();
        string usernamematch = ConfigurationManager.AppSettings["usernamematch"];
        string sexmath = ConfigurationManager.AppSettings["sexmath"];
        public void CheckedTimer()
        {
            try
            {

            
            if (true)
            {

            
            List<Book> books = bookBll.GetBooks(new Book());
            var book = books.Where(x => x.BirthdayTime.Substring(x.BirthdayTime.IndexOf("/")+1).Replace(" 0:00:00","") == DateTime.Now.Date.Month+"/"+DateTime.Now.Date.Day && x.GroupId != 0 && x.UserId != 0 && x.IsRemind == "1");
                if (book.Any())
                {

                    foreach (var item in book)
                    {
                        SendSmsModel ssm = new SendSmsModel();
                        var us = clientBLL.GetCustomerById(item.UserId.ToString());
                        ssm.UserId = us.UserId;
                        ssm.Account = us.UserName;
                        ssm.ExtNumber = "";
                        ssm.PassWord = us.ToKen;
                        ssm.TaskId = "";
                        ssm.SmsType = 1;
                        ssm.Mobile = item.Msisdn;
                        if (_filterMessage(item.Template, out Dictionary<string, string> dic))
                        {

                            dic[usernamematch] = item.UserName;
                            dic[sexmath] = item.Sex.ToString() == "1" ? "女士" : "先生";
                            ssm.Content = item.Template.Replace("{{" + sexmath + "}}", dic[sexmath]).Replace("{{" + usernamematch + "}}", dic[usernamematch]);

                        }
                        else
                        {
                            ssm.Content = item.Template;
                        }

                            //MsgTask md = new MsgTask();
                            //md.UserId = item.UserId;
                            //md.StartTime = DateTime.Now.Date;
                            //md.QueryRows = 1;
                            //md.ServiceType = 1;
                            //md.UserName = us.UserName;
                            //md.Text = ssm.Content;
                            //var msgDetails=mtbll.QueryTaskListProc(md, 1, 50, out int allSize, out int allPageSize);

                            //var bookds= db.HashGet("ClientBooks", DateTime.Now.Date.ToString("yyyy-MM-dd"));
                            var bookds= db.HashGet("ClientBooks", DateTime.Now.Date.ToString("yyyy-MM-dd"));
                            List<RedisClientBooks> redisClientBooks = new List<RedisClientBooks>();
                            List<RedisClientBooks> redisClientBooks1 = new List<RedisClientBooks>();
                            if (!bookds.IsNull)
                            {
                                redisClientBooks = JsonConvert.DeserializeObject<List<RedisClientBooks>>(bookds);
                            }
                            redisClientBooks1 = redisClientBooks.Where(x => x.UserId == item.UserId && x.AddTime == DateTime.Now.Date && x.Msisdn == item.Msisdn && x.Content == ssm.Content).ToList(); ;
                            //redisClientBooks1 = redisClientBooks.Where(x => x.UserId == item.UserId && x.Msisdn == item.Msisdn && x.Content == ssm.Content).ToList(); ;
                            if (!redisClientBooks1.Any())
                        //if (!msgDetails.Any())
                            {
                            var url = ConfigurationManager.AppSettings["InternalServiceUrl"];
                            InternalService.InternalService interser = new InternalService.InternalService();
                            interser.Url = url;
                            var timespan = NativeMethods.GetTimeStamp();
                            var sercet = NativeMethods.Md5Encrypt(ssm.PassWord + timespan);
                            InternalService.ServiceType serviceType = InternalService.ServiceType.Sms;
                            switch (ssm.SmsType)
                            {
                                case 1:
                                    serviceType = InternalService.ServiceType.Sms;
                                    break;
                                case 4:
                                    serviceType = InternalService.ServiceType.Fms;
                                    break;
                                default:
                                    break;
                            }
                            InternalService.NResult sendResult = interser.SendSmsByServiceType(ssm.UserId, ssm.Account, sercet, timespan, ssm.Mobile, ssm.Content, ssm.PlanTime, ssm.ExtNumber, ssm.TaskId, serviceType);
                            if (sendResult.StatusCode == InternalService.StatusCode.Success)
                            {
                                    redisClientBooks.Add(new RedisClientBooks() { UserId = item.UserId,Msisdn=item.Msisdn,Content= ssm.Content,AddTime=DateTime.Now.Date });
                                    string addjson = JsonConvert.SerializeObject(redisClientBooks);
                                    //db.HashSet("ClientBooks", DateTime.Now.Date.ToString("yyyy-MM-dd"), addjson);
                                    db.HashSet("ClientBooks", DateTime.Now.Date.ToString("yyyy-MM-dd"), addjson);
                                    Trace.TraceInformation("addjson="+addjson);
                                Trace.TraceInformation("发送生日祝福，成功，UserId=" + item.UserId + ",Content=" + ssm.Content);
                            }
                            else
                            {
                                    redisClientBooks.Add(new RedisClientBooks() { UserId = item.UserId, Msisdn = item.Msisdn, Content = ssm.Content, AddTime = DateTime.Now.Date });
                                    string addjson = JsonConvert.SerializeObject(redisClientBooks);
                                    db.HashSet("ClientBooks", DateTime.Now.Date.ToString("yyyy-MM-dd"), addjson);
                                    Trace.TraceInformation("addjson=" + addjson);
                                    Trace.TraceInformation("发送生日祝福，失败，UserId=" + item.UserId + ",Content=" + ssm.Content+",失败原因："+sendResult.Description);
                            }
                        }
                    }
                }
            }
            }
            catch (Exception ex)
            {
                Trace.TraceError("CheckedTimer,ex=" + ex);
            }
        }
        private static string RepalceCC(Match m)
        {
            return "#";
        }

        /// <summary>
        /// 使用短信模板将短信中的关键内容进行提取 , 并返回字典
        /// </summary>
        /// <param name="msgContent">短信内容</param>
        /// <param name="matchString">短信模板</param>
        /// <param name="dictionary">关键字:值 字典</param>
        private bool _filterMessage(string matchString, out Dictionary<string, string> dictionary)
        {
            try
            {

                matchString = matchString.Replace("(", "").Replace(")", "");
                dictionary = new Dictionary<string, string>();
                string sRegex = "({{)(.*?)(}})";
                Regex r = new Regex(sRegex);
                MatchEvaluator myEvaluator = new MatchEvaluator(RepalceCC);
                string temString = r.Replace(matchString, myEvaluator);   // 将分隔符替换为统一的 #   将无效信息用#分割
                MatchCollection items = r.Matches(matchString);  // 提取模板中{{}}标注的关键字
                for (int i = 0; i < items.Count; i++)
                {
                    Match gcm = items[i] as Match;
                    string keyWord = gcm.Groups[2].ToString();
                    dictionary[keyWord] = "";
                }
                if (dictionary.Count>0)
                {
                    return true;
                }
                return false;
            }
            catch (Exception e)
            {
                dictionary = null;

                return false;
            }
        }
        
    }
    public class RedisClientBooks
    {
        public int UserId { get; set; }
        public string Msisdn { get; set; }
        public string Content { get; set; }
        public DateTime AddTime { get; set; }
    }
}
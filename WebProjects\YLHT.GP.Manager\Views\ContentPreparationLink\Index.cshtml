﻿@model YLHT.GP.Models.ContentPreparationModel

@{ ViewBag.Title = "内容报备链接号码模板";}

<nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 客户管理 <span class="c-gray en">&gt;</span> 内容报备链接号码模板 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
<div class="page-container">
    @using (Html.BeginForm("Index", "ContentPreparationLink", FormMethod.Post, new { id = "form1" }))
    {
        <div>
            用户账号：<input type="text" class="input-text" value="@Model.UserName" style="width:150px" placeholder="输入用户账号" id="UserName" autocomplete="off" name="UserName">
            报备内容：<input type="text" class="input-text" name="Text" style="width:250px" value="@Model.Text" placeholder="报备内容" />
            报备状态：
            <span class="select-box inline">
                <select size="1" class="select" style="width:100px">
                    <option value="-1">全部状态</option>
                    <option value="0">停用</option>
                    <option value="1">启用</option>
                    <option value="2">审核</option>
                    <option value="3">退回</option>
                </select>
                @*@Html.DropDownList("Status", ((List<SelectListItem>)ViewBag.Status), new { style = "width:100px", size = "1", @class = "select" })*@
            </span>
            <input type="hidden" value="@ViewBag.curpage" id="hdcurpage" name="currpage" /><!--当前页-->
            <button type="submit" class="btn btn-success radius" id="" name=""><i class="Hui-iconfont">&#xe665;</i> 查询</button>
        </div>
    }
    <div class="cl pd-5 bg-1 bk-gray mt-20">
        <span class="l">
            <a href="javascript:;" style="display:none" onclick="datadel()" class="btn btn-danger radius"><i class="Hui-iconfont">&#xe6e2;</i> 批量删除</a>
            <a href="javascript:;" onclick="content_add('新增免审内容','/ContentPreparationLink/AddContent','800','500')" class="btn btn-primary radius"><i class="Hui-iconfont">&#xe600;</i> 新增免审内容</a>
            <a href="javascript:;" onclick="batchImport()" class="btn btn-warning radius"><i class="Hui-iconfont">&#xe642;</i> 批量导入</a>
            <a href="javascript:;" onclick="Upstatus('3','退回')" class="btn btn-primary radius"> 退回</a>
            <a href="javascript:;" onclick="Upstatus('2','审核')" class="btn btn-primary radius"> 审核</a>
            <a href="javascript:;" onclick="Upstatus('0','停用')" class="btn btn-primary radius"> 停用</a>
            <a href="javascript:;" onclick="Upstatus('1','启用')" class="btn btn-primary radius"> 启用</a>
            <a href="javascript:;" onclick="exportData()" class="btn btn-success radius"><i class="Hui-iconfont">&#xe644;</i> 导出数据</a>
        </span>
        <span class="r">共有数据：<strong>@ViewBag.allsize</strong> 条</span>
    </div>
    <table class="table table-border table-bordered table-bg">
        <thead>
            <tr class="text-c">
                <th width="25"><input type="checkbox" name="" value=""></th>
                <th width="130">Id</th>
                <th width="40">用户</th>
                <th width="100">操作账户</th>
                <th width="100">操作员工</th>
                <th width="70">状态</th>
                <th width="130">备注</th>
                <th width="120">添加时间</th>
                <th width="100">操作</th>
            </tr>
        </thead>
        <tbody>
            @if (ViewBag.clist != null)
            {
                foreach (YLHT.GP.Models.ContentPreparationModel item in (List<YLHT.GP.Models.ContentPreparationModel>)ViewBag.clist)
                {
                    <tr class="text-c">
                        <td><input type="checkbox" value="@item.Id" name="checkbox" /></td>
                        <td>@item.Id</td>
                        <td>@item.UserName</td>
                        <td>@item.OperatorUserName</td>
                        <td>@item.OperatorAdminName</td>

                        <td class="td-status">

                            @switch (item.Status)
                            {
                                case 0:
                                    <span title="" class="label label-default radius">停用</span>
                                    break;
                                case 1:
                                    <span title="" class="label label-success radius">启用</span>
                                    break;
                                case 2:
                                    <span title="" class="label label-success radius">审核</span>
                                    break;
                                case 3:
                                    <span title="" class="label label-danger radius">退回</span>
                                    break;
                                default:
                                    <span title="" class="label label-default radius">未知</span>
                                    
                                    break;
                            }

                        </td>
                        <td>@item.Remark</td>
                        <td>@item.AddTime</td>
                        <td class="td-manage">
                            <a title="编辑" href="javascript:;" onclick="content_edit('编辑','/ContentPreparationLink/UpdateContentPreparation/@item.Id','','510')" class="ml-5" @*style="text-decoration:none"*@>@*<i class="Hui-iconfont">&#xe6df;</i>*@编辑</a>
                            <a title="删除" href="javascript:;" onclick="content_del(this,'@item.Id')" class="ml-5" @*style="text-decoration:none"*@>删除@*<i class="Hui-iconfont">&#xe6e2;</i>*@</a>
                        </td>
                    </tr>
                    <tr class="text-r">
                        <td colspan="9">
                            @item.Text
                        </td>
                    </tr>
                }
            }
            else
            {
                <tr>
                    <td scope="col" colspan="9" style="text-align:center">暂无数据</td>
                </tr>
            }
        </tbody>
        @if (ViewBag.allSize > ViewBag.PageSize)
        {
            <tfoot>
                <tr><td colspan="9" class="text-r"><div id="page1" class="page"></div></td></tr>
            </tfoot>
        }
    </table>

</div>
<script src="~/Scripts/MyScript/Jquery.Common.js"></script>
<script>
    /*
    参数解释：
    title	标题
    url		请求的url
    id		需要操作的数据id
    w		弹出层宽度（缺省调默认值）
    h		弹出层高度（缺省调默认值）
*/
    /*内容报备-编辑*/
    function content_edit(title, url, w, h) {
        layer_show(title, url, w, h);
    }
    /*内容报备-添加*/
    function content_add(title, url, w, h) {
        layer_show(title, url, w, h);
    }
    /*内容报备-删除*/
    function content_del(obj, id) {
        var arrid = [];
        arrid.push(id);
        layer.confirm('确认要删除吗？', function (index) {
            $.ajax({
                type: 'POST',
                url: '/ContentPreparationLink/DeleteContent',
                data: { cid: arrid },
                dataType: 'json',
                success: function (data) {
                    if (data == 1) {
                        $(obj).parents("tr").remove();
                        layer.msg('已删除!', { icon: 1, time: 1000 });
                    } else {
                        layer.msg('删除失败!', { icon: 2, time: 1000 });
                        location.replace(location.href);
                    }
                },
                error: function (data) {
                    console.log(data);
                },
            });
        });
    }
    /**内容报备-批量删除 */
    function datadel() {
        var arrid = [];
        $("input[name='checkbox']:checked").each(function () {
            arrid.push(this.getAttribute("value"));
        });
        if (arrid.length == 0) {
            layer.msg("请选择要删除的内容报备", { icon: 7, time: 1000 });
            return;
        }
        layer.confirm('确认要删除吗？', function (index) {
            $.ajax({
                type: 'POST',
                url: '/ContentPreparationLink/DeleteContents',
                dataType: 'json',
                data: { cid: arrid },
                success: function (data) {
                    if (data > 0) {
                        $("input[name='checkbox']:checked").each(function () {
                            $(this).parents("tr").remove();
                        });
                        layer.msg('已删除!', { icon: 1, time: 1000 });
                    } else {
                        layer.msg('删除失败!', { icon: 1, time: 1000 });
                        location.replace(location.href);
                    }
                },
                error: function (data) {
                    console.log(data.msg);
                }
            });
        });
    }

    /*修改状态*/
    function updatestatus(thi, obj, id) {
        var msg = "";
        if (obj == 0) {
            msg = "确认要启用吗？";
        } else if (obj == 1) {
            msg = "确认要停用吗？";
        }
        layer.confirm(msg, function (index) {
            $.ajax({
                type: 'POST',
                url: '/ContentPreparationLink/UpdateStatus',
                data: { status: obj, id: id },
                dataType: 'json',
                success: function (data) {
                    if (data == 1) {
                        if (obj == 0) {
                            $(thi).parents("tr").find(".td-status").html('<span onclick="updatestatus(this,1,\'' + id + '\')" title="点击停用内容报备" class="label label-success radius">已启用</span>');
                        } else if (obj == 1) {
                            $(thi).parents("tr").find(".td-status").html('<span onclick="updatestatus(this,0,\'' + id + '\')" title="点击启用内容报备" class="label label-defaunt radius">已禁用</span>');
                        }
                        layer.msg('修改成功!', { icon: 1, time: 1000 });
                    } else {
                        layer.msg('修改失败!', { icon: 2, time: 1000 });
                        //location.replace(location.href);
                    }
                },
                error: function (data) {
                    console.log(data);
                },
            });
        });
    }

    function Upstatus(statu, Description, id) {
        var idList;
        if (id) {
            idList = [id];
        } else {
            var arrid = [];
            $("input[name='checkbox']:checked").each(function () {
                arrid.push(this.getAttribute("value"));
            });
            if (arrid.length == 0) {
                layer.msg("请选择要操作的记录！", { icon: 7, time: 1000 });
                return;
            }
            idList = arrid;

        }
        layer.confirm('确认设为(' + Description + ') ?', function (index) {

            try {
                $.ajax({
                    type: 'POST',
                    url: '/ContentPreparationLink/UpdateStatus',
                    dataType: 'json',
                    data: { id: idList.join(','), status: statu },
                    success: function (data) {
                        console.log(data);
                        if (data == 1) {
                            layer.msg('操作成功!', { icon: 1, time: 2000 }, function () {
                                loadshow();
                                location.replace(location.href);
                            });
                        } else {
                            layer.msg('操作失败!', { icon: 2, time: 2000 }, function () {
                                loadshow();
                                location.replace(location.href);
                            });
                        }
                    },
                    error: function (data) {
                        console.log(data.Text);
                    }
                });
            } catch (e) {
                alert('操作错误' + e);
            }
        });
    }

    // 添加导出函数
    function exportData() {
        var userName = $("#UserName").val();
        var text = $("input[name='Text']").val();
        var status = $(".select").val();

        // 构建导出URL
        var url = '/ContentPreparationLink/ExportContentPreparation?' +
            'UserName=' + encodeURIComponent(userName) +
            '&Text=' + encodeURIComponent(text) +
            '&Status=' + status;

        // 使用window.location直接下载文件
        window.location.href = url;
    }

    /*批量导入*/
    function batchImport() {
        layer.confirm('批量导入功能可以快速导入大量模板数据。<br><br>' +
            '<strong>📋 支持格式：</strong><br>' +
            '• Excel文件（.xlsx, .xls）<br>' +
            '• CSV文件（.csv）<br>' +
            '• 文本文件（.txt）<br><br>' +
            '<strong>⚠️ 注意事项：</strong><br>' +
            '• 系统会自动跳过重复的模板<br>' +
            '• 建议单次导入不超过10000条记录<br>' +
            '• 导入前请确保数据格式正确<br><br>' +
            '是否继续进入批量导入页面？', {
            btn: ['进入导入页面', '取消'],
            icon: 3,
            title: '批量导入内容报备模板',
            area: ['500px', 'auto']
        }, function(index) {
            layer.close(index);
            // 在新窗口中打开批量导入页面
            layer.open({
                type: 2,
                title: '批量导入内容报备链接号码模板',
                area: ['900px', '700px'],
                fix: false,
                maxmin: true,
                content: '/ContentPreparationLink/BatchImport',
                end: function() {
                    // 导入完成后刷新当前页面
                    location.reload();
                }
            });
        });
    }
</script>
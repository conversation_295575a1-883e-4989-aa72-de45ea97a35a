/**
 * Website: http://git.oschina.net/hbbcs/bootStrap-addTabs
 *
 * Version : 1.7
 *
 * Created by joe on 2016-2-15.
 */
$.fn.addtabs=function(a){Addtabs.options=$.extend({content:"",close:true,monitor:"body",iframeUse:true,iframeHeight:$(document).height()-107,contextmenu:true,obj:$(this),local:{"refreshLabel":"刷新此标签","closeThisLabel":"关闭此标签","closeOtherLabel":"关闭其他标签","closeLeftLabel":"关闭左侧标签","closeRightLabel":"关闭右侧标签"},callback:function(){}},a||{});$(Addtabs.options.monitor).on("click","[data-addtab]",function(){Addtabs.add({id:$(this).data("addtab"),title:$(this).data("title")?$(this).data("title"):$(this).html(),content:Addtabs.options.content?Addtabs.options.content:$(this).data("content"),url:$(this).data("url"),ajax:$(this).data("ajax")?true:false})});Addtabs.options.obj.on("click",".close-tab",function(){var c=$(this).prev("a").attr("aria-controls");Addtabs.close(c)});if(Addtabs.options.contextmenu){Addtabs.options.obj.on("contextmenu","li[role=presentation]",function(){var c=$(this).children("a").attr("aria-controls");Addtabs.pop(c,$(this));return false});Addtabs.options.obj.on("click","ul.rightMenu a[data-right=refresh]",function(){var d=$(this).parent("ul").attr("aria-controls").substring(4);var c=$(this).parent("ul").attr("aria-url");Addtabs.add({"id":d,"url":c,"refresh":true})});Addtabs.options.obj.on("click","ul.rightMenu a[data-right=remove]",function(){var c=$(this).parent("ul").attr("aria-controls");if(c.substring(0,4)!="tab_"){return}Addtabs.close(c);Addtabs.drop()});Addtabs.options.obj.on("click","ul.rightMenu a[data-right=remove-circle]",function(){var c=$(this).parent("ul").attr("aria-controls");Addtabs.options.obj.children("ul.nav").find("li").each(function(){var d=$(this).attr("id");if(d&&d!="tab_"+c){Addtabs.close($(this).children("a").attr("aria-controls"))}});Addtabs.drop()});Addtabs.options.obj.on("click","ul.rightMenu a[data-right=remove-left]",function(){var c=$(this).parent("ul").attr("aria-controls");$("#tab_"+c).prevUntil().each(function(){var d=$(this).attr("id");if(d&&d!="tab_"+c){Addtabs.close($(this).children("a").attr("aria-controls"))}});Addtabs.drop()});Addtabs.options.obj.on("click","ul.rightMenu a[data-right=remove-right]",function(){var c=$(this).parent("ul").attr("aria-controls");$("#tab_"+c).nextUntil().each(function(){var d=$(this).attr("id");if(d&&d!="tab_"+c){Addtabs.close($(this).children("a").attr("aria-controls"))}});Addtabs.drop()})}var b;Addtabs.options.obj.on("dragstart.h5s","li",function(c){b=$(this)}).on("dragover.h5s dragenter.h5s drop.h5s","li",function(c){if(b==$(this)){return}$(".dragBack").removeClass("dragBack");$(this).addClass("dragBack");b.insertAfter($(this))}).on("dragend.h5s","li",function(){$(".dragBack").removeClass("dragBack")});if(Addtabs.options.close){Addtabs.options.obj.on("mouseover",'li[role = "presentation"]',function(){$(this).find(".close-tab").show()});Addtabs.options.obj.on("mouseleave",'li[role = "presentation"]',function(){$(this).find(".close-tab").hide()})}$(window).resize(function(){Addtabs.options.obj.find("iframe").attr("height",Addtabs.options.iframeHeight);Addtabs.drop()})};window.Addtabs={options:{},add:function(b){var d="tab_"+b.id;Addtabs.options.obj.find('li[role = "presentation"].active').removeClass("active");Addtabs.options.obj.find('div[role = "tabpanel"].active').removeClass("active");if(Addtabs.options.obj.find("#"+d).length<=0){var c=$("<li>",{"role":"presentation","id":"tab_"+d,"aria-url":b.url}).append($("<a>",{"href":"#"+d,"aria-controls":d,"role":"tab","data-toggle":"tab"}).html(b.title));if(Addtabs.options.close){c.append($("<i>",{"class":"close-tab glyphicon glyphicon-remove"}))}var a=$("<div>",{"class":"tab-pane","id":d,"role":"tabpanel"});Addtabs.options.obj.children(".nav-tabs").append(c);Addtabs.options.obj.children(".tab-content").append(a)}else{if(!b.refresh){$("#tab_"+d).addClass("active");$("#"+d).addClass("active");Addtabs.drop();return}else{var a=$("#"+d);a.html("")}}if(b.content){a.append(b.content)}else{if(Addtabs.options.iframeUse&&!b.ajax){a.append($("<iframe>",{"class":"iframeClass","height":Addtabs.options.iframeHeight,"frameborder":"no","border":"0","src":b.url}))}else{$.get(b.url,function(e){a.append(e)})}}$("#tab_"+d).addClass("active");$("#"+d).addClass("active");Addtabs.drop()},createMenu:function(a,b,c){return $("<a>",{"href":"javascript:void(0);","class":"list-group-item","data-right":a}).append($("<i>",{"class":"glyphicon "+b})).append(c)},pop:function(h,g){$("body").find("#popMenu").remove();var d=g.attr("id")?Addtabs.createMenu("refresh","glyphicon-refresh",Addtabs.options.local.refreshLabel):"";var b=g.attr("id")?Addtabs.createMenu("remove","glyphicon-remove",Addtabs.options.local.closeThisLabel):"";var f=g.prev("li").attr("id")?Addtabs.createMenu("remove-left","glyphicon-chevron-left",Addtabs.options.local.closeLeftLabel):"";var c=g.next("li").attr("id")?Addtabs.createMenu("remove-right","glyphicon-chevron-right",Addtabs.options.local.closeRightLabel):"";var a=$("<ul>",{"aria-controls":h,"class":"rightMenu list-group",id:"popMenu","aria-url":g.attr("aria-url")}).append(d).append(b).append(Addtabs.createMenu("remove-circle","glyphicon-remove-circle",Addtabs.options.local.closeOtherLabel)).append(f).append(c);a.css({"top":g[0].offsetTop+10,"left":g[0].offsetLeft+60});a.appendTo(Addtabs.options.obj).fadeIn("slow");a.mouseleave(function(){$(this).fadeOut("slow")});$("body").click(function(){a.fadeOut("slow")})},close:function(a){if(Addtabs.options.obj.find("li.active").attr("id")==="tab_"+a){$("#tab_"+a).prev().addClass("active");$("#"+a).prev().addClass("active")}$("#tab_"+a).remove();$("#"+a).remove();Addtabs.drop();Addtabs.options.callback()},closeAll:function(){$.each(Addtabs.options.obj.find("li[id]"),function(){var b=$(this).children("a").attr("aria-controls");$("#tab_"+b).remove();$("#"+b).remove()});Addtabs.options.obj.find('li[role = "presentation"]').first().addClass("active");var a=Addtabs.options.obj.find('li[role = "presentation"]').first().children("a").attr("aria-controls");$("#"+a).addClass("active");Addtabs.drop()},drop:function(){element=Addtabs.options.obj.find(".nav-tabs");var b=$("<li>",{"class":"dropdown pull-right hide tabdrop tab-drop"}).append($("<a>",{"class":"dropdown-toggle","data-toggle":"dropdown","href":"#"}).append($("<i>",{"class":"glyphicon glyphicon-align-justify"})).append($("<b>",{"class":"caret"}))).append($("<ul>",{"class":"dropdown-menu"}));if(!$(".tabdrop").html()){b.prependTo(element)}else{b=element.find(".tabdrop")}if(element.parent().is(".tabs-below")){b.addClass("dropup")}var a=0;element.append(b.find("li")).find(">li").not(".tabdrop").each(function(){if(this.offsetTop>0||element.width()-$(this).position().left-$(this).width()<83){b.find("ul").prepend($(this));a++}});if(a>0){b.removeClass("hide");if(b.find(".active").length==1){b.addClass("active")}else{b.removeClass("active")}}else{b.addClass("hide")}}};

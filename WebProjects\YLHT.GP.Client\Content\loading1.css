.sk-three-bounce11 {
    /*使用弹性布局让加载动画持续会于页面中央，不随滚动条变化*/
    position: fixed;
    top: 50%;
    left: 50%;
}

    .sk-three-bounce11 .sk-child {
        /*在这里设置加载球的大小*/
        width: 30px;
        height: 30px;
        /*加载求的颜色在这里修改*/
        background-color: deepskyblue;
        border-radius: 100%;
        display: inline-block;
        -webkit-animation: sk-three-bounce11 1.4s ease-in-out 0s infinite both;
        animation: sk-three-bounce11 1.4s ease-in-out 0s infinite both;
    }

.sk-three-bounce11 .sk-bounce1 {
	-webkit-animation-delay: -0.32s;
	animation-delay: -0.32s;
}

.sk-three-bounce11 .sk-bounce2 {
	-webkit-animation-delay: -0.16s;
	animation-delay: -0.16s;
}

@-webkit-keyframes sk-three-bounce11 {
	0%,80%,100% {
		-webkit-transform: scale(0);
		transform: scale(0);
	}

	40% {
		-webkit-transform: scale(1);
		transform: scale(1);
	}
}

@keyframes sk-three-bounce11 {
	0%,80%,100% {
		-webkit-transform: scale(0);
		transform: scale(0);
	}

	40% {
		-webkit-transform: scale(1);
		transform: scale(1);
	}
}

.modal {
		    position: fixed;
		    top: 0;	
		    right: 0;
		    bottom: 0;
		    left: 0;
		    overflow: hidden;
		    outline: 0;
		    -webkit-overflow-scrolling: touch;
		    background-color: rgb(0, 0, 0);  
		    filter: alpha(opacity=60);  
		    background-color: rgba(0, 0, 0, 0.6); 
		    z-index: 1;
		}
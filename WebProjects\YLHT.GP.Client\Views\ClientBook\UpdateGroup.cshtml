﻿@model YLHT.GP.Models.BookGroup
@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="renderer" content="webkit">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <title>修改分组</title>

    <link href="~/Content/bootstrap.min.css" rel="stylesheet" />
    <link href="~/Client/css/font-awesome.min.css" rel="stylesheet" />
    <link href="~/Client/css/animate.min.css" rel="stylesheet" />
    <link href="~/Client/css/addGroup.css?v=1.0" rel="stylesheet" />
    <link href="~/Client/css/bootstrap-select.min.css" rel="stylesheet" />
    <script src="~/Client/js/jquery.min.js"></script>
    <script src="~/Client/js/bootstrap.min.js"></script>
    <script src="~/Client/js/layer/layer.js"></script>
    <script src="~/Client/js/plugins/layer/laydate/laydate.js"></script>
    <link href="~/Client/css/addEditContacts.css" rel="stylesheet" />
    <script src="~/Client/js/addGroup.js?v=1.0"></script>
    <script src="~/Client/js/bootstrap-select.min.js"></script>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-xs-12 col-title">
                <div class="form-group">
                    <label for="group-name">分组名称：</label>
                    <input value="@Model.GroupId" name="GroupId" id="group-id" type="hidden"/>
                    <input id="group-name" type="text" value="@Model.GroupName" class="form-control">
                </div>
                <div class="form-group">
                    <label for="contacts-group">生日模板：</label>
                    <select id="group-template" class="selectpicker1 form-control" data-live-search="true">
                        @if (ViewBag.BirthdayTemplates != null)
                        {
                            foreach (var item in (List<YLHT.GP.Models.BirthdayTemplate>)ViewBag.BirthdayTemplates)
                            {
                                <option @(item.TEMPLATEID==Model.TemplateId ? "selected" : "") value="@item.TEMPLATEID">@item.TEMPLATEID,@item.Template</option>
                            }
                        }
                    </select>
                </div>
                <button class="btn btn-primary" id="edit">确认修改</button>
            </div>
        </div>
    </div>
</body>
</html>

﻿using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Web.Mvc.Ajax;
using System.Web.Routing;
using System.Linq;
using YLHT_GP_Business.Business;
using YLHT.GP.Common;
using YLHT_GP_Business.Interface;
using System.Web.Mvc;
using YLHT.GP.Models;

namespace System.Web.Mvc.Html
{
    public static class MvcHelper
    {
        static Dictionary<int, string> provs = (Dictionary<int, string>)HttpRuntime.Cache["ProvinceListKey"];
        static Dictionary<int, string> citys = (Dictionary<int, string>)HttpRuntime.Cache["CityListKey"];
        static CommonBll cb = new CommonBll();
        /// <summary>
        /// 获取通道名称
        /// </summary>
        /// <param name="html"></param>
        /// <param name="channelInfo"></param>
        /// <returns></returns>
        public static IHtmlString GetChannelNames(this HtmlHelper html,string channelInfo,string isAudit="1")
        {
            
            Dictionary<string,int> dictionary= cb.GetChannelName(channelInfo);
            if (dictionary!=null)
            {
                var name = "";
                if (isAudit=="1")
                {
                    name = dictionary.Select(r => (r.Key == "" ? "未知" : r.Key) + "(" + r.Value + ")").Join(",");
                }
                else
                {
                    name = dictionary.Select(r => (r.Key == "" ? "未知" : r.Key)).Join(",");
                }
               
                return MvcHtmlString.Create(name);
            }
            return MvcHtmlString.Create("未知");
        }
        /// <summary>
        /// 获取切换通道名称
        /// </summary>
        /// <param name="html"></param>
        /// <param name="newChannelInfo"></param>
        /// <returns></returns>
        public static IHtmlString GetNewChannelNames(this HtmlHelper html,string newChannelInfo) {
            
            return MvcHtmlString.Create(cb.GetChannelById(newChannelInfo));
        }
        /// <summary>
        /// 获取审核页面运营商展示字段
        /// </summary>
        /// <returns></returns>
        public static IHtmlString GetOperatorShow(this HtmlHelper html, string OperatorInfo)
        {
            string name="";
            try
            {

            
            if (OperatorInfo != null && OperatorInfo != "")
            {
                if (OperatorInfo.Contains(";"))
                {
                    string[] arr = OperatorInfo.Split(';');
                    for (int i = 0; i < arr.Length; i++)
                    {
                        string[] arr1 = arr[i].Split(',');
                        string[] arr2 = arr1[3].Split(':');
                        name += (OperatorTypeEnum)int.Parse(arr1[1]) + "(" + arr2[1] + "),";
                    }
                }
                else
                {

                    string[] arr1 = OperatorInfo.Split(',');
                    string[] arr2 = arr1[3].Split(':');
                    name += (OperatorTypeEnum)int.Parse(arr1[1]) + "(" + arr2[1] + "),";

                }
                if (name.Substring(name.Length - 1, 1) == ",")
                {
                    name = name.Substring(0, name.Length - 1);
                }

            }
            }
            catch (Exception ex)
            {
                Trace.TraceError("获取审核页面运营商展示字段方法异常,MvcHelper类中GetOperatorShow方法，ex=" + ex);
            }
            return MvcHtmlString.Create(name);
        }
        /// <summary>
        /// 获取运营商名称
        /// </summary>
        /// <param name="html"></param>
        /// <param name="OperatorInfo"></param>
        /// <returns></returns>
        public static IHtmlString GetOperatorName(this HtmlHelper html,string OperatorInfo)
        {

            //Dictionary<string, string> dictionary = cb.GetOperatorName(OperatorInfo);
            //if (dictionary!=null)
            //{
            //    var name = dictionary.Select(r=>((r.Value.Split(':')[0]==""|| r.Value.Split(':')[0] == null) ? "未知": r.Value.Split(':')[0])+"-"+((r.Key == ""|| r.Key==null) ? "未知" : r.Key)+"("+r.Value.Split(':')[1]+")").Join(",");
            //    //dictionary.Select(r=> {
            //    //    r.Key.Split(':')[0]+ r.Key.Split(':')[1]+
            //    //});
            //    return MvcHtmlString.Create(name);
            //}
            string str = "";
            string cc = "";//国家
            string oper = "";//运营商
            string prov = "";//省
            string city = "";//市
            List<string> list = new List<string>();

            try
            {

            
            if (OperatorInfo.Contains(";")) {
                    List<string> arr = OperatorInfo.Split(';').ToList();
                    for (int i = 0; i < arr.Count(); i++)
                    {
                        List<string> arr1 = arr[i].Split(':').ToList();
                        List<string> arr2 = arr1[0].Split(',').ToList();

                        //arr[0];//国家
                        //arr2[2];//省
                        //arr2[3];//市
                        cc=(arr2[0]=="86" ? "中国": arr2[0]);
                        provs.TryGetValue(int.Parse(arr2[2]),out prov);//省
                    citys.TryGetValue(int.Parse(arr2[3]), out city);//市
                    oper= GetOperatorName(arr2[1]);//运营商
                    if (city!=null)
                    {
                        city = "-" + city;
                    }
                    string tmp = GetOperatorName(arr2[1]) + "(" + arr1[1] + ")" + " " + cc + "-" + prov + city;
                    list.Add(tmp);
                    }
                }
                else
                {
                    List<string> arr = OperatorInfo.Split(':').ToList();
                    List<string> arr1 = arr[0].Split(',').ToList();
                provs.TryGetValue(int.Parse(arr1[2]), out prov);//省
                citys.TryGetValue(int.Parse(arr1[3]), out city);//市
                oper = GetOperatorName(arr1[1]);//运营商
                cc = (arr1[0] == "86" ? "中国" : arr1[0]);
                if (city != null)
                {
                    city = "-" + city;
                }
                string tmp = GetOperatorName(arr1[1]) + "(" + arr[1] + ")" + " " + cc + "-" + prov + city;

                list.Add(tmp);
            }
            }
            catch (Exception ex)
            {
                Trace.TraceError("获取运营商类型异常，MvcHelper类中GetOperatorName方法，ex=" + ex);
            }

            //list = cb.GetOperatorName(OperatorInfo);

            return MvcHtmlString.Create(list.Join(","));
        }
        /// <summary>
        /// 获取优先级名称
        /// </summary>
        /// <param name="html"></param>
        /// <param name="Priority"></param>
        /// <returns></returns>
        public static IHtmlString GetPriority(this HtmlHelper html,int Priority)
        {
            //优先级；1：最低；2：较低；3：低；4：高；5：较高；6：最高
            string tmp;
            switch (Priority)
            {
                case 0:
                    tmp = "最低";
                    break;
                case 1:
                    tmp = "较低";
                    break;
                case 2:
                    tmp = "低";
                    break;
                case 3:
                    tmp = "正常";
                    break;
                case 4:
                    tmp = "高于正常";
                    break;
                case 5:
                    tmp = "高";
                    break;
                case 6:
                    tmp = "较高";
                    break;
                case 7:
                    tmp = "最高";
                    break;
                default:
                    tmp = "未知";
                    break;
            }
            return MvcHtmlString.Create(tmp);
        }
        /// <summary>
        /// 获取访问方式名称
        /// </summary>
        /// <param name="html"></param>
        /// <param name="Access"></param>
        /// <returns></returns>
        public static IHtmlString GetAccessOrigin(this HtmlHelper html,int Access)
        {
            string tmp;
            switch (Access)
            {
                case 0:
                    tmp = "未知";
                                        break;
                case 1:
                    tmp = "平台";
                                        break;
                case 2:
                    tmp = "Http";
                                        break;
                case 4:
                    tmp = "WebService";
                                        break;
                case 8:
                    tmp = "Cmpp20";
                                        break;
                case 10:
                    tmp = "Cmpp30";
                                        break;
                case 20:
                    tmp = "Sgip12";
                                        break;
                case 40:
                    tmp = "Smgp30";
                                        break;
                default:
                    tmp = "未知";
                    break;
            }
            return MvcHtmlString.Create(tmp);
        }
        /// <summary>
        /// 获取额度类型
        /// </summary>
        /// <param name="html"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public static IHtmlString GetAmountType(this HtmlHelper html,int type) {
            string amountType="";
            switch (type)
            {
                case 1:
                    amountType = "短信";
                    break;
                case 2:
                    amountType = "彩信";
                    break;
                case 3:
                    amountType = "闪信";
                    break;
                case 9:
                    amountType = "金额";
                    break;
                default:
                    break;
            }
            return MvcHtmlString.Create(amountType);
        }
        /// <summary>
        /// 获取操作类型方法
        /// </summary>
        /// <param name="html"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public static IHtmlString GetActionType(this HtmlHelper html,int type) {
            string actionType = "";
            switch (type)
            {
                case 1:
                    actionType = "充值";
                    break;
                case 2:
                    actionType = "扣费";
                    break;
                case 3:
                    actionType = "子账户充值";
                    break;
                case 4:
                    actionType = "任务退回";
                    break;
                case 5:
                    actionType = "失败返还";
                    break;
                default:
                    break;
            }
            return MvcHtmlString.Create(actionType);
        }
        /// <summary>
        /// 获取业务类型名称
        /// </summary>
        /// <param name="html"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public static IHtmlString GetServiceType(this HtmlHelper html,int type)
        {
            string serviceType = "未知";
            switch (type)
            {
                case 1:
                    serviceType = "短信";
                    break;
                case 2:
                    serviceType = "彩信";
                    break;
                case 3:
                    serviceType = "闪信";
                    break;
                default:
                    break;
            }
            return MvcHtmlString.Create(serviceType);
        }
        /// <summary>
        /// 获取客户列表根据通道组id
        /// </summary>
        /// <param name="html"></param>
        /// <param name="ProductId"></param>
        /// <returns></returns>
        public static IHtmlString GetProductUserName(this HtmlHelper html, string ProductId)
        {
            if (ProductId!=null&&ProductId!="")
            {
                ProductBll pb = new ProductBll();
                return MvcHtmlString.Create(pb.GetClientListByProductId(ProductId).Select(r=>r.UserName).Join(","));
            }
            return MvcHtmlString.Create("");
        }
        /// <summary>
        /// 获取审核描述
        /// </summary>
        /// <param name="html"></param>
        /// <param name="AuditData"></param>
        /// <returns></returns>
        public static IHtmlString GetAuditData(this HtmlHelper html,string AuditData,int AuditType)
        {

            string tmpData = "";
            switch (AuditType)
            {
                case 1://账户设置  审核条数：号码数量
                    tmpData = "账户设置，" + AuditData;
                    break;
                case 2://缺少通道  cc，operatorType，provinceId，cityId
                    if (AuditData != null && AuditData != "")
                    {
                        string[] data1 = AuditData.Split(";");
                        if (data1.Count() > 1)
                        {
                            for (int i = 0; i < data1.Count(); i++)
                            {
                                string prov="未知";//省
                                string city = "未知";//市
                                try
                                {
                                    provs.TryGetValue(int.Parse(data1[i].Split(",")[2]), out prov);
                                
                                    citys.TryGetValue(int.Parse(data1[i].Split(",")[3]), out city);


                                        tmpData += GetOperatorName(data1[i].Split(",")[1]) + "：" + prov + "-" + city + ",";

                                }
                                catch (Exception ex)
                                {
                                    Trace.TraceError("缺少通道展示异常，MvcHelper类中GetAuditData方法,ex="+ex);
                                    tmpData += "未知" + "：" + prov + "-" + city + ",";
                                }
                            }
                        }
                        else
                        {
                            string prov = "未知";//省
                            string city = "未知";//市
                            try
                            {
                                provs.TryGetValue(int.Parse(data1[0].Split(",")[2]), out prov);

                            citys.TryGetValue(int.Parse(data1[0].Split(",")[3]), out city);
                            tmpData += GetOperatorName(data1[0].Split(",")[1]) + "：" + prov + "-" + city + ",";
                            }
                            catch (Exception ex)
                            {
                                Trace.TraceError("缺少通道展示异常，MvcHelper类中GetAuditData方法,ex=" + ex);
                                tmpData += "未知" + "：" + prov + "-" + city + ",";
                            }
                        }
                        tmpData = "缺少通道，" + tmpData.Substring(0, tmpData.LastIndexOf(","));
                    }
                    break;
                default:
                    break;
            }
            return MvcHtmlString.Create(tmpData);
        }
        /// <summary>
        /// 运营商类型
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string GetOperatorName(string id)
        {
            string tmp = "未知";
            switch (id)
            {
                case "0":
                    tmp = "全网";
                    break;
                case "1":
                    tmp = "移动";
                    break;
                case "2":
                    tmp = "联通";
                    break;
                case "3":
                    tmp = "电信";
                    break;
                default:
                    break;
            }
            return tmp;
        }
        /// <summary>
        /// 运营商类型
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static IHtmlString GetOperName(this HtmlHelper html, string id)
        {
            string tmp = "未知";
            switch (id)
            {
                case "0":
                    tmp = "全网";
                    break;
                case "1":
                    tmp = "移动";
                    break;
                case "2":
                    tmp = "联通";
                    break;
                case "3":
                    tmp = "电信";
                    break;
                default:
                    break;
            }
            return MvcHtmlString.Create(tmp);
        }
        public static IHtmlString MmsHtmlString(this HtmlHelper html, string msgid, DateTime? dateTime, int userid)
        {
            var sbmmsBuilder = new StringBuilder();
            sbmmsBuilder.Append("<a href=\"../MmsOperation/DownloadMms?TaskId=" + msgid + "\">下载彩信</a>&nbsp;&nbsp;");
            sbmmsBuilder.Append("<a href=\"javascript:OpenMmsDetails('" + msgid + "','" + userid + "','" + dateTime + "');\">查看详细</a>");
            return MvcHtmlString.Create(sbmmsBuilder.ToString());
        }
        public static IHtmlString MmsTemplateHtmlString(this HtmlHelper html, string msgid, DateTime? dateTime, int userid)
        {
            var sbmmsBuilder = new StringBuilder();
            sbmmsBuilder.Append("<a href=\"../MmsOperation/DownloadMmsTemplate?TaskId=" + msgid + "\">下载彩信</a>&nbsp;&nbsp;");
            sbmmsBuilder.Append("<a href=\"javascript:OpenMmsTemplateDetails('" + msgid + "','" + userid + "','" + dateTime + "');\">查看详细</a>&nbsp;&nbsp;");
            //sbmmsBuilder.Append("<a href=\"javascript:layer_show('彩信预览', '/MmsOperation/MmsTemplateView?TaskId=" + msgid + "&UserId=" + userid + "_" + dateTime + "', '600', '620');\">彩信预览</a>");
            return MvcHtmlString.Create(sbmmsBuilder.ToString());
        }

        /// <summary>
        /// 获取动态彩信帧内容html
        /// </summary>
        /// <param name="html"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public static IHtmlString GetFrameView(this HtmlHelper html,string id)
        {
            //var framehtml = $@"
            //                    <div class='layui-row layui-col-space10'>
            //                        <div class='layui-col-md2'>
            //                            <textarea class='textarea' style='width:180px'></textarea>
            //                        </div>
            //                        <div class='layui-col-md10'>
            //                            <div class='container-fluid'>
            //                                <div class='panel panel-primary'>
            //                                    <div class='panel-heading' align='center'>
            //                                        <label style='text-align: center;font-size: 18px;'>文 件 上 传</label>
            //                                    </div>
            //                                    <div class='panel-body'>
            //                                        <input id='input-{id}' name='file' multiple type='file' data-show-caption='true'>
            //                                    </div>
            //                                </div>
            //                            </div>
            //                        </div>
            //                    </div>
            //                ";
            var framehtml = $@"
                                <div class='layui-row layui-col-space10'>
                                    <div class='layui-col-md12'>
                                        <div class='container-fluid'>
                                            <div class='panel panel-primary'>
                                                <div class='panel-heading' align='center'>
                                                    <label style='text-align: center;font-size: 18px;'>文 件 上 传</label>
                                                </div>
                                                <div class='panel-body'>
                                                    <input id='input-{id}' name='file' multiple type='file' data-show-caption='true'>
                                                    <br />
                                                    <label style='text-align: left;font-size: 18px;'>时间：<input type='text' class='text' name='' id='zhentime-{id}' value='5' />&nbsp; 单位秒</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ";
            return MvcHtmlString.Create(framehtml);
        }
        static VoiceBll voiceBll = new VoiceBll ();
        public static IHtmlString GetVoiceTemplateName(this HtmlHelper html, int smsid)
        {
            VoiceTemplate vt = voiceBll.GetVoiceTemplate(smsid);
            if (vt != null)
            {
                return MvcHtmlString.Create(vt.TemplateName);
            }
            return MvcHtmlString.Create("未找到模板");
        }

    }
}

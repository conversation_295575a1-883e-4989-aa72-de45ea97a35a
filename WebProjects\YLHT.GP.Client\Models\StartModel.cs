﻿using System;
using System.Collections.Generic;
using System.Linq;
using YLHT.GP.Models;

namespace YLHT.GP.Client.Models
{
    public class StartModel
    {
        private const int max = 12;

        public StartModel(CollectModel data)
        {
            var hour1 = from x in data.UserCollect
                        group x by x.Hour into g
                        select new
                        {
                            g.Key,
                            SubmitCount = g.Sum(a => a.SubmitCount),//提交数量
                            SuccessCount = g.Sum(a => a.SuccessCount),//下发成功
                            FailedCount = g.Sum(a => a.FailedCount),//下发失败
                            NullCount = g.Sum(a => a.NullCount),//未知
                        };

            var hour2 = (from x1 in Enumerable.Range(0, 24)
                         join x2 in hour1 on x1 equals x2.Key into g
                         from a1 in g.DefaultIfEmpty(new { Key = 0, SubmitCount = 0, SuccessCount = 0, FailedCount = 0, NullCount = 0 })
                         select new { Key = x1, a1.SubmitCount, a1.SuccessCount, a1.FailedCount, a1.NullCount }).ToArray();



            this.Hour = new Chart
            {
                categories = hour2.Select(x => x.Key + "").ToArray(),
                series = new[]
                {
                    new ChartSeries ($"提交({data.UserCollect.Sum(x=>x.SubmitCount)})",  hour2.Select(x => new ChartData(x.SubmitCount))),
                    new ChartSeries ($"成功({data.UserCollect.Sum(x=>x.SuccessCount)})", hour2.Select(x => new ChartData(x.SuccessCount))),
                    new ChartSeries($"失败({data.UserCollect.Sum(x=>x.FailedCount)})",hour2.Select(x=>new ChartData(x.FailedCount))),
                    new ChartSeries($"未知({data.UserCollect.Sum(x=>x.NullCount)})",hour2.Select(x=>new ChartData(x.NullCount))),
                }
            };
        }

        public DateTime Date { get; set; }
        public Chart Hour { get; private set; }
        public Chart User { get; private set; }
        public Chart Channel { get; private set; }

        public class Chart
        {
            public string[] categories { get; set; }
            public ChartSeries[] series { get; set; }
        }

        public class ChartSeries
        {
            public ChartSeries(string name, IEnumerable<ChartData> data)
            {
                this.name = name;
                this.data = data.ToArray();
            }

            public string name { get; set; }
            public ChartData[] data { get; set; }
        }

        public class ChartData
        {
            public ChartData(decimal y)
            {
                this.y = y;
            }
            public decimal y { get; set; }
            public string name { get; set; }
        }
    }
}
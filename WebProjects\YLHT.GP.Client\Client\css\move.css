.col-title {
    padding-top: 10px;
}

.form-control {
    border-radius: 0;
}

.form-control:focus {
    box-shadow: none;
    border-color: #1ab394 !important
}

body,
.container-fluid {
    overflow: hidden;
}

.btn {
    width: 100%;
    border-radius: 0;
    margin: auto;
    display: block;
    padding: 10px;
}

.m-radio {
    display: block;
    width: 100%;
}

.m-radio > input {
    display: none;
}

.m-radio > i {
    font-style: inherit;
    display: block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.42857143;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid #cccccc;
}

.m-radio > input:checked + i {
    border-color: #286090;
    color: #286090;
}
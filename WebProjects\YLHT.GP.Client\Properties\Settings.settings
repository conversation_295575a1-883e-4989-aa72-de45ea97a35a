﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="YLHT.GP.Client.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="YLHT_GP_Client_DataService_DataService" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://127.0.0.1:50020/DataService.asmx</Value>
    </Setting>
    <Setting Name="YLHT_GP_Client_BillingService_BillingService" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://127.0.0.1:50020/BillingService.asmx</Value>
    </Setting>
    <Setting Name="YLHT_GP_Client_InternalService_InternalService" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://127.0.0.1:5001/InternalService.asmx</Value>
    </Setting>
  </Settings>
</SettingsFile>
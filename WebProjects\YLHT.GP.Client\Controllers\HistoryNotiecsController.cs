﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using YLHT_GP_Business.Business;
using YLHT.GP.Models;

namespace YLHT.GP.Client.Controllers
{
    /// <summary>
    /// 历史公告
    /// </summary>
    public class HistoryNotiecsController : BaseController
    {
        NoticesBll noticesBll = new NoticesBll();
        // GET: HistoryNotiecs
        public ActionResult Index(NoticesModel nm,int currpage=0,int PageSize=50)
        {
            if (Request.IsAuthenticated&&Session["UserID"]!=null)
            {
                int rows = 0;
                int pageCount;
                if (currpage <= 0)
                {
                    currpage = 1;
                }
                nm.UserId = UserId;
                nm.Status = 2;
                ViewBag.alist=noticesBll.GetNoticesList(nm,PageSize,currpage,out rows,out pageCount);
                if (currpage > pageCount)
                {
                    currpage = pageCount;
                }
                if (currpage <= 0 && pageCount != 0)
                {
                    currpage = 1;
                }
                //总页数
                ViewBag.allPage = pageCount;
                //当前页
                ViewBag.curpage = currpage;
                //总条数
                ViewBag.allSize = rows;
                
                return View(nm);
            }
            return RedirectToAction("Account","Login");
        }
    }
}